import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/common/gf_text_field.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _showEmailForm = false;
  bool _isLogin = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  void _toggleEmailForm({bool isLogin = false}) {
    setState(() {
      _showEmailForm = !_showEmailForm;
      _isLogin = isLogin;
    });
  }

  Future<void> _handleSignIn() async {
    if (_formKey.currentState?.validate() ?? false) {
      // For sign up, make sure passwords match
      if (!_isLogin &&
          _passwordController.text != _confirmPasswordController.text) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('Passwords do not match')));
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      developer.log('LoginScreen: Starting authentication process - isLogin: $_isLogin');
      if (kDebugMode) {
        print('LoginScreen: Starting authentication process - isLogin: $_isLogin');
      }

      bool success;
      if (_isLogin) {
        success = await authProvider.signIn(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );
      } else {
        success = await authProvider.signUp(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        );
      }

      developer.log('LoginScreen: Authentication result: $success');
      if (kDebugMode) {
        print('LoginScreen: Authentication result: $success');
        if (!success) {
          print('LoginScreen: Error message: ${authProvider.errorMessage}');
        }
      }

      if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage ?? 'Authentication failed'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  Future<void> _testConnection() async {
    developer.log('LoginScreen: Testing Supabase connection');
    if (kDebugMode) {
      print('LoginScreen: Testing Supabase connection');
    }

    try {
      final connectionTest = await SupabaseService.instance.testConnection();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              connectionTest
                ? 'Connection successful!'
                : 'Connection failed - check backend'
            ),
            backgroundColor: connectionTest ? Colors.green : Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      developer.log('LoginScreen: Connection test result: $connectionTest');
      if (kDebugMode) {
        print('LoginScreen: Connection test result: $connectionTest');
      }
    } catch (e, stackTrace) {
      developer.log(
        'LoginScreen: Connection test error',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('LoginScreen: CONNECTION TEST ERROR: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Connection test failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          floatingActionButton: kDebugMode
              ? FloatingActionButton(
                  onPressed: _testConnection,
                  backgroundColor: AppColors.gfGreen,
                  child: const Icon(Icons.wifi, color: Colors.black),
                )
              : null,
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.gfLightTeal,
                      AppColors.gfTeal,
                      AppColors.gfDarkBlue,
                    ],
                    stops: [0.0, 0.5, 1.0],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // GameFlex Logo - Centered in the top half of the screen
                        Expanded(
                          flex:
                              5, // This will take up the top half of the screen
                          child: Center(
                            child: Column(
                              mainAxisAlignment:
                                  MainAxisAlignment.center, // Center vertically
                              children: [
                                SizedBox(
                                  width:
                                      180, // Slightly smaller to balance with wider logo
                                  height: 180,
                                  child: SvgPicture.asset(
                                    'assets/images/icons/icon_teal.svg',
                                    fit: BoxFit.contain,
                                  ),
                                ),
                                const SizedBox(height: 24),
                                // Logo takes up 80% of screen width (10% margin on each side)
                                LayoutBuilder(
                                  builder: (context, constraints) {
                                    // Calculate 80% of the available width
                                    double logoWidth =
                                        MediaQuery.of(context).size.width * 0.8;
                                    return SizedBox(
                                      width: logoWidth,
                                      child: SvgPicture.asset(
                                        'assets/images/logos/gameflex_text.svg',
                                        fit: BoxFit.fitWidth,
                                      ),
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ),
                        // This will take up the bottom half of the screen for buttons
                        Expanded(
                          flex: 8,
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                const SizedBox(height: 20), // Reduced spacing

                                if (!_showEmailForm) ...[
                                  // Sign up with email button
                                  _buildLoginButton(
                                    icon: Icons.email_outlined,
                                    text: 'Sign up with email',
                                    backgroundColor: Colors.black,
                                    textColor: Colors.white,
                                    onPressed: () {
                                      setState(() {
                                        _showEmailForm = true;
                                        _isLogin = false;
                                      });
                                    },
                                  ),
                                ],

                                // Email form that appears when button is clicked
                                if (_showEmailForm) ...[
                                  const SizedBox(height: 10),
                                  // Back button
                                  Align(
                                    alignment: Alignment.centerLeft,
                                    child: TextButton.icon(
                                      onPressed: () {
                                        setState(() {
                                          _showEmailForm = false;
                                        });
                                      },
                                      icon: const Icon(
                                        Icons.arrow_back,
                                        color: AppColors.gfGreen,
                                      ),
                                      label: const Text(
                                        'Back',
                                        style: TextStyle(
                                          color: AppColors.gfGreen,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Container(
                                    width:
                                        MediaQuery.of(context).size.width * 0.8,
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.black.withValues(
                                        alpha: 179,
                                      ), // 0.7 opacity
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: SingleChildScrollView(
                                      child: Form(
                                        key: _formKey,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            GFTextField(
                                              label: 'Email',
                                              hint: 'Enter your email',
                                              controller: _emailController,
                                              keyboardType:
                                                  TextInputType.emailAddress,
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please enter your email';
                                                }
                                                if (!value.contains('@')) {
                                                  return 'Please enter a valid email';
                                                }
                                                return null;
                                              },
                                            ),
                                            const SizedBox(height: 12),
                                            GFTextField(
                                              label: 'Password',
                                              hint: 'Enter your password',
                                              controller: _passwordController,
                                              obscureText: !_isPasswordVisible,
                                              validator: (value) {
                                                if (value == null ||
                                                    value.isEmpty) {
                                                  return 'Please enter your password';
                                                }
                                                if (value.length < 6) {
                                                  return 'Password must be at least 6 characters';
                                                }
                                                return null;
                                              },
                                              suffixIcon: IconButton(
                                                icon: Icon(
                                                  _isPasswordVisible
                                                      ? Icons.visibility_off
                                                      : Icons.visibility,
                                                  color: AppColors.gfGrayText,
                                                ),
                                                onPressed: () {
                                                  setState(() {
                                                    _isPasswordVisible =
                                                        !_isPasswordVisible;
                                                  });
                                                },
                                              ),
                                            ),
                                            const SizedBox(height: 12),
                                            // Only show confirm password field for sign up
                                            if (!_isLogin) ...[
                                              GFTextField(
                                                label: 'Confirm Password',
                                                hint: 'Confirm your password',
                                                controller:
                                                    _confirmPasswordController,
                                                obscureText:
                                                    !_isConfirmPasswordVisible,
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.isEmpty) {
                                                    return 'Please confirm your password';
                                                  }
                                                  if (value !=
                                                      _passwordController
                                                          .text) {
                                                    return 'Passwords do not match';
                                                  }
                                                  return null;
                                                },
                                                suffixIcon: IconButton(
                                                  icon: Icon(
                                                    _isConfirmPasswordVisible
                                                        ? Icons.visibility_off
                                                        : Icons.visibility,
                                                    color: AppColors.gfGrayText,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _isConfirmPasswordVisible =
                                                          !_isConfirmPasswordVisible;
                                                    });
                                                  },
                                                ),
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Only show TOS and Sign Up button for sign up (not login)
                                  if (!_isLogin) ...[
                                    const SizedBox(height: 12),
                                    // Terms of Service and Privacy Policy text
                                    Container(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                      ),
                                      child: RichText(
                                        textAlign: TextAlign.center,
                                        text: TextSpan(
                                          style: const TextStyle(
                                            color: AppColors.gfOffWhite,
                                            fontSize: 12,
                                            height: 1.4,
                                          ),
                                          children: [
                                            const TextSpan(
                                              text:
                                                  'By clicking Sign up you acknowledge that you understand the ',
                                            ),
                                            TextSpan(
                                              text: 'Privacy Policy',
                                              style: const TextStyle(
                                                color: AppColors.gfGreen,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                              // In a real app, you would add a GestureRecognizer here
                                              // to handle the link tap
                                            ),
                                            const TextSpan(
                                              text: ' and agree to the ',
                                            ),
                                            TextSpan(
                                              text: 'Terms of Service',
                                              style: const TextStyle(
                                                color: AppColors.gfGreen,
                                                decoration:
                                                    TextDecoration.underline,
                                              ),
                                              // In a real app, you would add a GestureRecognizer here
                                              // to handle the link tap
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    // Large Sign Up button
                                    SizedBox(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      height: 55,
                                      child: ElevatedButton(
                                        onPressed: _handleSignIn,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color(
                                            0xFF28F4C3,
                                          ), // The specified color
                                          foregroundColor: Colors.black,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          elevation: 0,
                                        ),
                                        child: const Text(
                                          'Sign Up',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ] else ...[
                                    const SizedBox(height: 16),
                                    // Log In button
                                    SizedBox(
                                      width:
                                          MediaQuery.of(context).size.width *
                                          0.8,
                                      height: 55,
                                      child: ElevatedButton(
                                        onPressed: _handleSignIn,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: const Color(
                                            0xFF28F4C3,
                                          ), // The specified color
                                          foregroundColor: Colors.black,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                          ),
                                          elevation: 0,
                                        ),
                                        child: const Text(
                                          'Log In',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ],

                                if (!_showEmailForm) ...[
                                  const SizedBox(height: 20),
                                  // Apple login button
                                  _buildLoginButton(
                                    icon: Icons.apple,
                                    text: 'Log in with Apple ID',
                                    backgroundColor: Colors.white,
                                    textColor: Colors.black,
                                    onPressed: () {
                                      // Apple login functionality
                                    },
                                  ),
                                  const SizedBox(height: 20),
                                  // Google login button
                                  _buildLoginButton(
                                    icon: Icons.g_mobiledata,
                                    text: 'Log in with Google',
                                    backgroundColor: Colors.white,
                                    textColor: Colors.black,
                                    onPressed: () {
                                      // Google login functionality
                                    },
                                  ),
                                  const SizedBox(height: 24),
                                  // Log in text button
                                  Center(
                                    child: TextButton(
                                      onPressed: () {
                                        setState(() {
                                          _showEmailForm = true;
                                          _isLogin = true;
                                        });
                                      },
                                      child: const Text(
                                        'Log in',
                                        style: TextStyle(
                                          color: AppColors.gfGreen,
                                          fontSize: 16,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                                const SizedBox(height: 20),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Loading overlay
              if (authProvider.isLoading)
                Container(
                  color: Colors.black.withValues(alpha: 128), // 0.5 opacity
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColors.gfGreen,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoginButton({
    required IconData icon,
    required String text,
    required Color backgroundColor,
    required Color textColor,
    required VoidCallback onPressed,
  }) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate 80% of the available width (10% margin on each side)
        double buttonWidth = MediaQuery.of(context).size.width * 0.8;

        return SizedBox(
          width: buttonWidth,
          height: 55, // Slightly taller buttons
          child: ElevatedButton.icon(
            onPressed: onPressed,
            style: ElevatedButton.styleFrom(
              backgroundColor: backgroundColor,
              foregroundColor: textColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 0,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            icon: Icon(icon, size: 24),
            label: Text(
              text,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: textColor,
              ),
            ),
          ),
        );
      },
    );
  }
}
