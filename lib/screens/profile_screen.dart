import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../theme/app_theme.dart';
import '../providers/auth_provider.dart';
import '../widgets/common/gf_button.dart';
import 'liked_posts_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;

        return Scaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 20),

                  // Profile Avatar
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: const LinearGradient(
                        colors: [AppColors.gfLightTeal, AppColors.gfTeal],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      border: Border.all(color: AppColors.gfGreen, width: 3),
                    ),
                    child: const Icon(
                      Icons.person,
                      size: 60,
                      color: AppColors.gfDarkBlue,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // User Display Name
                  Text(
                    user?.displayName ?? 'User',
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: AppColors.gfOffWhite,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // User Email
                  Text(
                    user?.email ?? 'No email',
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.gfGrayText,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Profile Stats
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.gfDarkBackground,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppColors.gfGreen.withValues(
                          alpha: 77,
                        ), // 0.3 opacity
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem('Posts', '0'),
                            _buildStatItem('Followers', '0'),
                            _buildStatItem('Following', '0'),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem('Likes', '0'),
                            _buildStatItem('Comments', '0'),
                            _buildStatItem('Shares', '0'),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Account Information
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.gfDarkBackground,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppColors.gfGreen.withValues(
                          alpha: 77,
                        ), // 0.3 opacity
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Account Information',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: AppColors.gfOffWhite,
                          ),
                        ),
                        const SizedBox(height: 16),
                        _buildInfoRow('User ID', user?.id ?? 'N/A'),
                        const SizedBox(height: 12),
                        _buildInfoRow(
                          'Email Verified',
                          user?.isEmailConfirmed == true ? 'Yes' : 'No',
                        ),
                        const SizedBox(height: 12),
                        _buildInfoRow(
                          'Member Since',
                          _formatDate(user?.createdAt),
                        ),
                        const SizedBox(height: 12),
                        _buildInfoRow(
                          'Last Updated',
                          _formatDate(user?.updatedAt),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Action Buttons
                  Column(
                    children: [
                      // Edit Profile Button
                      SizedBox(
                        width: double.infinity,
                        child: GFButton(
                          text: 'Edit Profile',
                          onPressed: () {
                            // TODO: Navigate to edit profile screen
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text(
                                  'Edit Profile feature coming soon!',
                                ),
                                backgroundColor: AppColors.gfTeal,
                              ),
                            );
                          },
                          type: GFButtonType.secondary,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Liked Posts Button
                      SizedBox(
                        width: double.infinity,
                        child: GFButton(
                          text: 'Liked Posts',
                          onPressed: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder: (context) => const LikedPostsScreen(),
                              ),
                            );
                          },
                          type: GFButtonType.secondary,
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Settings Button
                      SizedBox(
                        width: double.infinity,
                        child: GFButton(
                          text: 'Settings',
                          onPressed: () {
                            // TODO: Navigate to settings screen
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Settings feature coming soon!'),
                                backgroundColor: AppColors.gfTeal,
                              ),
                            );
                          },
                          type: GFButtonType.secondary,
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Logout Button
                      SizedBox(
                        width: double.infinity,
                        child: GFButton(
                          text: 'Log Out',
                          onPressed:
                              () => _showLogoutDialog(context, authProvider),
                          type: GFButtonType.danger,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.gfGreen,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: AppColors.gfGrayText),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            label,
            style: const TextStyle(fontSize: 14, color: AppColors.gfGrayText),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14, color: AppColors.gfOffWhite),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showLogoutDialog(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColors.gfDarkBackground,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'Log Out',
            style: TextStyle(
              color: AppColors.gfOffWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            'Are you sure you want to log out?',
            style: TextStyle(color: AppColors.gfGrayText),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(color: AppColors.gfGrayText),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await authProvider.signOut();
              },
              child: const Text(
                'Log Out',
                style: TextStyle(color: AppColors.gfGreen),
              ),
            ),
          ],
        );
      },
    );
  }
}
