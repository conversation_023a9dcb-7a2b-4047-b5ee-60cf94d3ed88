class LikeModel {
  final String id;
  final String userId;
  final String? postId;
  final String? commentId;
  final DateTime createdAt;
  
  // User information (from join)
  final String? username;
  final String? displayName;
  final String? avatarUrl;
  
  // Post information (from join)
  final String? postContent;
  final String? postMediaUrl;

  LikeModel({
    required this.id,
    required this.userId,
    this.postId,
    this.commentId,
    required this.createdAt,
    this.username,
    this.displayName,
    this.avatarUrl,
    this.postContent,
    this.postMediaUrl,
  });

  factory LikeModel.fromJson(Map<String, dynamic> json) {
    return LikeModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      postId: json['post_id'] as String?,
      commentId: json['comment_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      username: json['username'] as String?,
      displayName: json['display_name'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      postContent: json['post_content'] as String?,
      postMediaUrl: json['post_media_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'post_id': postId,
      'comment_id': commentId,
      'created_at': createdAt.toIso8601String(),
      'username': username,
      'display_name': displayName,
      'avatar_url': avatarUrl,
      'post_content': postContent,
      'post_media_url': postMediaUrl,
    };
  }

  LikeModel copyWith({
    String? id,
    String? userId,
    String? postId,
    String? commentId,
    DateTime? createdAt,
    String? username,
    String? displayName,
    String? avatarUrl,
    String? postContent,
    String? postMediaUrl,
  }) {
    return LikeModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      postId: postId ?? this.postId,
      commentId: commentId ?? this.commentId,
      createdAt: createdAt ?? this.createdAt,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      postContent: postContent ?? this.postContent,
      postMediaUrl: postMediaUrl ?? this.postMediaUrl,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  bool get isPostLike => postId != null;
  bool get isCommentLike => commentId != null;

  @override
  String toString() {
    return 'LikeModel(id: $id, userId: $userId, postId: $postId, commentId: $commentId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LikeModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
