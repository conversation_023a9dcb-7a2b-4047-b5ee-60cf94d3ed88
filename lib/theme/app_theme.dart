import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppColors {
  // Main brand colors
  static const Color gfGreen = Color(0xFF28F4C3);
  static const Color gfDarkBackground = Color(0x992A3642); // 60% opacity
  static const Color gfDarkBackground100 = Color(0xFF2A3642); // 100% opacity
  static const Color gfDarkBackground40 = Color(0x662A3642); // 40% opacity

  // Text colors
  static const Color gfGrayText = Color(0x80F4F4F4); // 50% opacity
  static const Color gfOffWhite = Color(0xFFF4F4F4);
  static const Color gfTextLight = Color(0xFF8FAABD);

  // Additional colors
  static const Color gfYellow = Color(0xFFF2DE76);
  static const Color gfBlue = Color(0xFF00D5FF);

  // Gradient colors
  static const Color gfLightTeal = Color(0xFF8BE4CF);
  static const Color gfTeal = Color(0xFF2BA5A5);
  static const Color gfDarkBlue = Color(0xFF293642);

  // Card background
  static const Color gfCardBackground = Color(0xFF1A2530);

  // Border colors
  static const Color gfGrayBorder = Color(0xFF3A4A5A);
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.gfGreen,
      scaffoldBackgroundColor: Colors.white,
      colorScheme: const ColorScheme.light(
        primary: AppColors.gfGreen,
        secondary: AppColors.gfBlue,
        background: Colors.white,
        surface: Colors.white,
      ),
      textTheme: GoogleFonts.robotoTextTheme(),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.gfGreen,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.gfGreen,
          side: const BorderSide(color: AppColors.gfGreen),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      primaryColor: AppColors.gfGreen,
      scaffoldBackgroundColor: AppColors.gfDarkBackground100,
      colorScheme: const ColorScheme.dark(
        primary: AppColors.gfGreen,
        secondary: AppColors.gfBlue,
        background: AppColors.gfDarkBackground100,
        surface: AppColors.gfDarkBackground,
      ),
      textTheme: GoogleFonts.robotoTextTheme(ThemeData.dark().textTheme),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.gfGreen,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.gfGreen,
          side: const BorderSide(color: AppColors.gfGreen),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.gfDarkBackground100,
        elevation: 0,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.gfDarkBackground100,
        selectedItemColor: AppColors.gfGreen,
        unselectedItemColor: AppColors.gfGrayText,
      ),
    );
  }
}
