# GameFlex Backend Development Setup Script (PowerShell)
# This script starts the Supabase development environment

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "GameFlex Backend Development Setup" -ForegroundColor Green
    Write-Host "Usage: .\start.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script will start the GameFlex development backend using Docker Compose."
    Write-Host "Make sure Docker Desktop is running before executing this script."
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting GameFlex Development Backend..." -ForegroundColor Green

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop and try again." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available. Please install Docker Desktop with Compose support." -ForegroundColor Red
    exit 1
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
$directories = @(
    "volumes\storage",
    "volumes\functions"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "   Created: $dir" -ForegroundColor Gray
    }
}

# Check if .env file exists
if (!(Test-Path ".env")) {
    Write-Host "❌ .env file not found. Please create one based on the provided template." -ForegroundColor Red
    exit 1
}

Write-Host "📋 Environment file found" -ForegroundColor Green

# Check if hosts file is configured for domain names
Write-Host "🔍 Checking hosts file configuration..." -ForegroundColor Yellow
$hostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
$hostsContent = Get-Content $hostsFile -ErrorAction SilentlyContinue
if ($hostsContent -notcontains "# GameFlex Development - START") {
    Write-Host "⚠️  GameFlex domain names not configured in hosts file!" -ForegroundColor Yellow
    Write-Host "   To enable domain names (api.gameflex.local, studio.gameflex.local, etc.):" -ForegroundColor Gray
    Write-Host "   Run as Administrator: .\setup-hosts-windows.ps1" -ForegroundColor White
    Write-Host "   Or continue with localhost URLs..." -ForegroundColor Gray
    Write-Host ""
} else {
    Write-Host "✅ GameFlex domain names configured" -ForegroundColor Green
}

# Load environment variables from .env file
$envVars = @{}
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $envVars[$matches[1]] = $matches[2]
    }
}

# Set default ports if not specified
$STUDIO_PORT = if ($envVars["STUDIO_PORT"]) { $envVars["STUDIO_PORT"] } else { "3000" }
$KONG_HTTP_PORT = if ($envVars["KONG_HTTP_PORT"]) { $envVars["KONG_HTTP_PORT"] } else { "8000" }
$POSTGRES_PORT = if ($envVars["POSTGRES_PORT"]) { $envVars["POSTGRES_PORT"] } else { "5432" }

# Start the services
Write-Host "🐳 Starting Docker containers..." -ForegroundColor Yellow
try {
    docker-compose up -d
    Write-Host "✅ Docker containers started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start Docker containers. Check the logs for details." -ForegroundColor Red
    docker-compose logs
    exit 1
}

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Function to check if a URL is responding
function Test-Url {
    param([string]$Url, [int]$TimeoutSeconds = 5)
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

# Function to check database connectivity
function Test-Database {
    try {
        docker-compose exec -T db pg_isready -U postgres -h localhost 2>$null | Out-Null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

# Check if database is ready
Write-Host "🔍 Checking database health..." -ForegroundColor Yellow
$dbReady = $false
$attempts = 0
$maxAttempts = 30

while (-not $dbReady -and $attempts -lt $maxAttempts) {
    $dbReady = Test-Database
    if (-not $dbReady) {
        Write-Host "   Waiting for database... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($dbReady) {
    Write-Host "✅ Database is ready!" -ForegroundColor Green
} else {
    Write-Host "❌ Database failed to start within expected time" -ForegroundColor Red
    Write-Host "Check logs with: docker-compose logs db" -ForegroundColor Yellow
}

# Check if Kong is ready
Write-Host "🔍 Checking API Gateway health..." -ForegroundColor Yellow
$kongReady = $false
$attempts = 0

while (-not $kongReady -and $attempts -lt $maxAttempts) {
    $kongReady = Test-Url "http://localhost:$KONG_HTTP_PORT/status"
    if (-not $kongReady) {
        Write-Host "   Waiting for API Gateway... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($kongReady) {
    Write-Host "✅ API Gateway is ready!" -ForegroundColor Green
} else {
    Write-Host "⚠️  API Gateway may still be starting up" -ForegroundColor Yellow
}

# Check if Studio is ready
Write-Host "🔍 Checking Supabase Studio health..." -ForegroundColor Yellow
$studioReady = $false
$attempts = 0

while (-not $studioReady -and $attempts -lt $maxAttempts) {
    $studioReady = Test-Url "http://localhost:$STUDIO_PORT/api/profile"
    if (-not $studioReady) {
        Write-Host "   Waiting for Supabase Studio... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($studioReady) {
    Write-Host "✅ Supabase Studio is ready!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Supabase Studio may still be starting up" -ForegroundColor Yellow
}

# Display success message and information
Write-Host ""
Write-Host "🎉 GameFlex Development Backend is now running!" -ForegroundColor Green -BackgroundColor DarkGreen
Write-Host ""

# Check if domain names are configured and show appropriate URLs
$hostsContent = Get-Content "$env:SystemRoot\System32\drivers\etc\hosts" -ErrorAction SilentlyContinue
if ($hostsContent -contains "# GameFlex Development - START") {
    Write-Host "🎯 GameFlex Services (Domain Names):" -ForegroundColor Cyan
    Write-Host "   📊 Supabase Studio: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://studio.gameflex.local:$STUDIO_PORT" -ForegroundColor White
    Write-Host "   🔌 API Gateway: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://api.gameflex.local:$KONG_HTTP_PORT" -ForegroundColor White
    Write-Host "   🗄️  Database: " -NoNewline -ForegroundColor Cyan
    Write-Host "db.gameflex.local:$POSTGRES_PORT" -ForegroundColor White
} else {
    Write-Host "🎯 GameFlex Services (Localhost):" -ForegroundColor Cyan
    Write-Host "   📊 Supabase Studio: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:$STUDIO_PORT" -ForegroundColor White
    Write-Host "   🔌 API Gateway: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:$KONG_HTTP_PORT" -ForegroundColor White
    Write-Host "   🗄️  Database: " -NoNewline -ForegroundColor Cyan
    Write-Host "localhost:$POSTGRES_PORT" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 To enable domain names, run as Administrator:" -ForegroundColor Yellow
    Write-Host "   .\setup-hosts-windows.ps1" -ForegroundColor White
}
Write-Host ""
Write-Host "🔑 Development Credentials:" -ForegroundColor Yellow
Write-Host "   Email: " -NoNewline -ForegroundColor Gray
Write-Host "<EMAIL>" -ForegroundColor White
Write-Host "   Password: " -NoNewline -ForegroundColor Gray
Write-Host "GameFlex123!" -ForegroundColor White
Write-Host ""
Write-Host "🔑 Admin Credentials:" -ForegroundColor Yellow
Write-Host "   Email: " -NoNewline -ForegroundColor Gray
Write-Host "<EMAIL>" -ForegroundColor White
Write-Host "   Password: " -NoNewline -ForegroundColor Gray
Write-Host "AdminGameFlex123!" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Management Commands:" -ForegroundColor Magenta
Write-Host "   To stop the backend: " -NoNewline -ForegroundColor Gray
Write-Host ".\stop.ps1 -KeepData" -ForegroundColor White
Write-Host "   To view logs: " -NoNewline -ForegroundColor Gray
Write-Host "docker-compose logs -f" -ForegroundColor White
Write-Host "   To reset data: " -NoNewline -ForegroundColor Gray
Write-Host ".\stop.ps1; .\start.ps1" -ForegroundColor White
Write-Host ""

# Open browser to Studio (optional)
$openBrowser = Read-Host "Would you like to open Supabase Studio in your browser? (y/N)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost:$STUDIO_PORT"
}
