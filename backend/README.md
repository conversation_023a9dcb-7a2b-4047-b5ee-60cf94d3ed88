# GameFlex Development Backend

This directory contains the Docker setup for the GameFlex development backend using Supabase.

## Quick Start

1. **Prerequisites**
   - Docker and Docker Compose installed
   - At least 4GB of available RAM
   - Ports 3000, 5432, 8000 available

2. **Start the backend**

   **Standard Setup:**

   Linux/macOS:
   ```bash
   cd backend
   chmod +x start.sh
   ./start.sh
   ```

   Windows (PowerShell):
   ```powershell
   cd backend
   .\start.ps1
   ```

   **Alternative: Minimal Setup (if you want fewer services):**

   Linux/macOS:
   ```bash
   cd backend
   chmod +x start-minimal.sh
   ./start-minimal.sh
   ```

   Windows (PowerShell):
   ```powershell
   cd backend
   .\start-minimal.ps1
   ```

3. **Access the services**
   - **Supabase Studio**: http://localhost:3000
   - **API Endpoint**: http://localhost:8000
   - **Database**: localhost:5432

## Development Credentials

### Default Users
- **Developer Account**
  - Email: `<EMAIL>`
  - Password: `GameFlex123!`

- **Admin Account**
  - Email: `<EMAIL>`
  - Password: `AdminGameFlex123!`

### API Keys
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0`
- **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU`

## Services

### Core Services
- **PostgreSQL Database** (port 5432)
- **Kong API Gateway** (port 8000)
- **Supabase Studio** (port 3000)
- **GoTrue Auth** (internal)
- **PostgREST API** (internal)
- **Realtime** (internal)
- **Storage** (internal)

### Supporting Services
- **Image Proxy** (internal)
- **Meta API** (internal)
- **Edge Functions** (internal)

## Database Schema

The database includes the following main tables:
- `gameflex.users` - User accounts
- `gameflex.user_profiles` - Extended user information
- `gameflex.channels` - Gaming channels/communities
- `gameflex.channel_members` - Channel membership
- `gameflex.posts` - User posts/content
- `gameflex.comments` - Post comments
- `gameflex.likes` - Likes on posts/comments
- `gameflex.follows` - User follow relationships
- `gameflex.notifications` - User notifications

## Sample Data

The database is pre-populated with:
- 5 sample users (including dev and admin accounts)
- 5 sample channels
- Sample posts, comments, and interactions
- Follow relationships between users

## Common Commands

### Linux/macOS
```bash
# Start the backend
./start.sh

# Stop the backend
./stop.sh

# Stop and remove all data
./stop.sh --remove-data

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f auth

# Access database directly
docker-compose exec db psql -U postgres

# Restart a specific service
docker-compose restart auth

# Update services
docker-compose pull
docker-compose up -d
```

### Windows (PowerShell)
```powershell
# Start the backend
.\start.ps1

# Stop the backend
.\stop.ps1

# Stop and remove all data
.\stop.ps1 -RemoveData

# View logs
docker-compose logs -f

# View logs for specific service
docker-compose logs -f auth

# Access database directly
docker-compose exec db psql -U postgres

# Restart a specific service
docker-compose restart auth

# Update services
docker-compose pull
docker-compose up -d
```

## Configuration

### Environment Variables
Key environment variables in `.env`:
- `POSTGRES_PASSWORD` - Database password
- `JWT_SECRET` - JWT signing secret
- `ANON_KEY` - Anonymous access key
- `SERVICE_ROLE_KEY` - Service role key
- `STUDIO_PORT` - Supabase Studio port
- `KONG_HTTP_PORT` - API Gateway port
- `POSTGRES_PORT` - Database port

### Customization
- Modify `volumes/db/init.sql` to change the database schema
- Modify `volumes/db/seed.sql` to change sample data
- Modify `.env` to change configuration
- Modify `docker-compose.yml` to change service configuration

## Troubleshooting

For detailed troubleshooting information, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

### Quick Fixes

1. **Database permission issues (Windows)**
   ```bash
   docker-compose down -v
   # Then restart with your platform's script
   ```

2. **Port conflicts**
   - Change ports in `.env` file
   - Default ports: 8000 (API), 5432 (DB), 3000 (Studio)

3. **Services not starting**
   - Check Docker is running: `docker info`
   - Check available memory: `docker system df`
   - View logs: `docker-compose logs`

4. **Complete reset**
   ```bash
   docker-compose down -v
   docker system prune -f
   # Then restart with your platform's script
   ```

### Reset Everything

**Linux/macOS:**
```bash
# Stop and remove all containers, networks, and volumes
docker-compose down -v

# Remove any orphaned containers
docker system prune -f

# Start fresh
./start.sh
```

**Windows (PowerShell):**
```powershell
# Stop and remove all containers, networks, and volumes
docker-compose down -v

# Remove any orphaned containers
docker system prune -f

# Start fresh
.\start.ps1
```

## Development Workflow

1. **Start the backend**:
   - Linux/macOS: `./start.sh`
   - Windows: `.\start.ps1`
2. **Access Supabase Studio**: http://localhost:3000
3. **Configure your Flutter app** to use `http://localhost:8000` as the Supabase URL
4. **Use the development credentials** to test authentication
5. **Monitor logs** with `docker-compose logs -f`

## Security Notes

⚠️ **This setup is for development only!**
- Default passwords are insecure
- JWT secrets are public
- All services are accessible without authentication
- Do not use in production

## Support

For issues with this development setup:
1. Check the logs: `docker-compose logs`
2. Verify all services are running: `docker-compose ps`
3. Check the Supabase documentation: https://supabase.com/docs
