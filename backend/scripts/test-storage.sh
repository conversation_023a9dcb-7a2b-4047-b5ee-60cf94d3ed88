#!/bin/sh

# Test script to verify storage initialization
# This script checks if the media files are accessible via Supabase storage
# Tests the new structured format: user/{user-id}/{channel-id}/{filename}

echo "🧪 Testing storage initialization..."

# Load environment variables if .env file exists
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Default values if not set
SUPABASE_URL=${SUPABASE_URL:-"http://localhost:8000"}

# List of expected media files with their structured paths
STRUCTURED_FILES="
user/00000000-0000-0000-0000-000000000003/10000000-0000-0000-0000-000000000001/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg
user/00000000-0000-0000-0000-000000000004/10000000-0000-0000-0000-000000000003/b2c3d4e5-f6g7-8901-bcde-f23456789012.jpg
user/00000000-0000-0000-0000-000000000005/10000000-0000-0000-0000-000000000004/c3d4e5f6-g7h8-9012-cdef-345678901234.webp
user/00000000-0000-0000-0000-000000000001/10000000-0000-0000-0000-000000000002/d4e5f6g7-h8i9-0123-def0-456789012345.png
"

echo "🔍 Checking if structured media files are accessible..."

echo "$STRUCTURED_FILES" | while IFS= read -r file_path; do
    # Skip empty lines
    [ -z "$file_path" ] && continue

    echo "Testing: $file_path"

    # Test public access to the file
    response=$(curl -s -w "%{http_code}" -o /dev/null "$SUPABASE_URL/storage/v1/object/public/media/$file_path")

    if [ "$response" = "200" ]; then
        echo "✅ $file_path is accessible"
    else
        echo "❌ $file_path is not accessible (HTTP $response)"
    fi
done

echo "🏁 Storage test completed!"
