-- GameFlex Development Seed Data
-- This script inserts initial development data into the public schema

-- Insert development users
INSERT INTO public.users (id, email, username, display_name, bio, is_verified, is_active) VALUES
(
    '********-0000-0000-0000-********0001',
    '<EMAIL>',
    'devuser',
    'Development User',
    'This is a development user account for testing purposes.',
    true,
    true
),
(
    '********-0000-0000-0000-********0002',
    '<EMAIL>',
    'admin',
    'Admin User',
    'Administrator account for GameFlex development.',
    true,
    true
),
(
    '********-0000-0000-0000-************',
    '<EMAIL>',
    'johndoe',
    '<PERSON>',
    'Gaming enthusiast and content creator.',
    true,
    true
),
(
    '********-0000-0000-0000-************',
    '<EMAIL>',
    'jane<PERSON>',
    '<PERSON>',
    'Professional gamer and streamer.',
    true,
    true
),
(
    '********-0000-0000-0000-********0005',
    '<EMAIL>',
    'mike<PERSON><PERSON>',
    '<PERSON>',
    'Casual gamer who loves sharing gaming moments.',
    true,
    true
)
ON CONFLICT (email) DO NOTHING;

-- Insert user profiles (only if user_profiles table exists and has the expected structure)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
        INSERT INTO public.user_profiles (user_id, first_name, last_name, country, language, preferences) VALUES
        (
            '********-0000-0000-0000-********0001',
            'Dev',
            'User',
            'United States',
            'en',
            '{"theme": "dark", "notifications": {"email": true, "push": true}}'
        ),
        (
            '********-0000-0000-0000-********0002',
            'Admin',
            'User',
            'United States',
            'en',
            '{"theme": "dark", "notifications": {"email": true, "push": true}}'
        ),
        (
            '********-0000-0000-0000-************',
            'John',
            'Doe',
            'United States',
            'en',
            '{"theme": "light", "notifications": {"email": true, "push": false}}'
        ),
        (
            '********-0000-0000-0000-************',
            'Jane',
            'Smith',
            'Canada',
            'en',
            '{"theme": "dark", "notifications": {"email": false, "push": true}}'
        ),
        (
            '********-0000-0000-0000-********0005',
            'Mike',
            'Wilson',
            'United Kingdom',
            'en',
            '{"theme": "auto", "notifications": {"email": true, "push": true}}'
        )
        ON CONFLICT (user_id) DO NOTHING;
        RAISE NOTICE 'User profiles inserted successfully';
    ELSE
        RAISE NOTICE 'user_profiles table does not exist, skipping profile insertion';
    END IF;
END
$$;

-- Insert development channels
INSERT INTO public.channels (id, name, description, owner_id, is_public, member_count) VALUES
(
    '10000000-0000-0000-0000-********0001',
    'General Gaming',
    'A place to discuss all things gaming',
    '********-0000-0000-0000-********0001',
    true,
    5
),
(
    '10000000-0000-0000-0000-********0002',
    'Mobile Gaming',
    'Share your favorite mobile gaming moments',
    '********-0000-0000-0000-************',
    true,
    3
),
(
    '10000000-0000-0000-0000-************',
    'Competitive Gaming',
    'For serious gamers and esports enthusiasts',
    '********-0000-0000-0000-************',
    true,
    2
),
(
    '10000000-0000-0000-0000-************',
    'Indie Games',
    'Discover and share amazing indie games',
    '********-0000-0000-0000-********0005',
    true,
    4
),
(
    '10000000-0000-0000-0000-********0005',
    'GameFlex Development',
    'Private channel for GameFlex development team',
    '********-0000-0000-0000-********0002',
    false,
    2
)
ON CONFLICT (id) DO NOTHING;

-- Insert channel memberships
INSERT INTO public.channel_members (channel_id, user_id, role) VALUES
-- General Gaming channel
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0001', 'owner'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0002', 'admin'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-************', 'member'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-************', 'member'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0005', 'member'),

-- Mobile Gaming channel
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-************', 'owner'),
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0005', 'member'),

-- Competitive Gaming channel
('10000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'owner'),
('10000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'member'),

-- Indie Games channel
('10000000-0000-0000-0000-************', '********-0000-0000-0000-********0005', 'owner'),
('10000000-0000-0000-0000-************', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'member'),
('10000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'member'),

-- GameFlex Development channel (private)
('10000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0002', 'owner'),
('10000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', 'admin')
ON CONFLICT (channel_id, user_id) DO NOTHING;

-- Insert sample posts
INSERT INTO public.posts (id, user_id, channel_id, content, media_url, media_type, like_count, comment_count) VALUES
(
    '20000000-0000-0000-0000-********0001',
    '********-0000-0000-0000-************',
    '10000000-0000-0000-0000-********0001',
    'Just finished an amazing gaming session! What games are you all playing this weekend?',
    'http://localhost:8000/storage/v1/object/public/media/user/********-0000-0000-0000-************/10000000-0000-0000-0000-********0001/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg',
    'image',
    5,
    2
),
(
    '20000000-0000-0000-0000-********0002',
    '********-0000-0000-0000-************',
    '10000000-0000-0000-0000-************',
    'New tournament starting next week! Who''s ready to compete?',
    'http://localhost:8000/storage/v1/object/public/media/user/********-0000-0000-0000-************/10000000-0000-0000-0000-************/b2c3d4e5-f6g7-8901-bcde-f23456789012.jpg',
    'image',
    8,
    3
),
(
    '20000000-0000-0000-0000-************',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-************',
    'Found this incredible indie game that you all need to try!',
    'http://localhost:8000/storage/v1/object/public/media/user/********-0000-0000-0000-********0005/10000000-0000-0000-0000-************/c3d4e5f6-g7h8-9012-cdef-345678901234.webp',
    'image',
    12,
    4
),
(
    '20000000-0000-0000-0000-************',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0002',
    'Mobile gaming has come so far! Check out this gameplay.',
    'http://localhost:8000/storage/v1/object/public/media/user/********-0000-0000-0000-********0001/10000000-0000-0000-0000-********0002/d4e5f6g7-h8i9-0123-def0-456789012345.png',
    'image',
    15,
    6
),
(
    '20000000-0000-0000-0000-********0005',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'Working on some exciting new features for GameFlex!',
    null,
    null,
    3,
    1
),
(
    '20000000-0000-0000-0000-********0006',
    '********-0000-0000-0000-************',
    '10000000-0000-0000-0000-********0001',
    'Epic boss fight last night! Took us 3 hours but we finally got him down! 🎮⚔️',
    null,
    'image',
    23,
    8
),
(
    '20000000-0000-0000-0000-********0007',
    '********-0000-0000-0000-************',
    '10000000-0000-0000-0000-********0002',
    'Streaming live now! Come watch me attempt this impossible speedrun 🏃‍♂️💨',
    null,
    'video',
    45,
    12
),
(
    '20000000-0000-0000-0000-********0008',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-************',
    'Just discovered this hidden gem of a game. The art style is absolutely stunning! 🎨',
    null,
    'image',
    18,
    5
),
(
    '20000000-0000-0000-0000-********0009',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-************',
    'GameFlex community is growing so fast! Thank you all for being part of this journey 🚀',
    null,
    null,
    67,
    15
),
(
    '20000000-0000-0000-0000-********0010',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'New patch notes are out! Lots of exciting changes coming to your favorite games 📝',
    null,
    null,
    34,
    9
),
(
    '20000000-0000-0000-0000-********0011',
    '********-0000-0000-0000-************',
    '10000000-0000-0000-0000-********0001',
    'Retro gaming night! Playing some classics from the 90s. What''s your favorite retro game? 🕹️',
    null,
    'image',
    29,
    11
),
(
    '20000000-0000-0000-0000-********0012',
    '********-0000-0000-0000-************',
    '10000000-0000-0000-0000-********0002',
    'VR gaming is mind-blowing! Just tried the new space exploration game 🚀🌌',
    null,
    'video',
    41,
    7
),
(
    '20000000-0000-0000-0000-********0013',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-************',
    'Cozy gaming session with some hot chocolate ☕ Perfect way to spend a rainy evening',
    null,
    'image',
    22,
    6
),
(
    '20000000-0000-0000-0000-********0014',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-************',
    'Beta testing some amazing new features! Can''t wait for you all to try them 🎯',
    null,
    null,
    38,
    13
),
(
    '20000000-0000-0000-0000-********0015',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'Game development tip: Always playtest with real users! Their feedback is invaluable 💡',
    null,
    null,
    25,
    4
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample comments
INSERT INTO public.comments (post_id, user_id, content, like_count) VALUES
('20000000-0000-0000-0000-********0001', '********-0000-0000-0000-************', 'I''m playing the new RPG that just came out!', 2),
('20000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0005', 'Same here! It''s incredible.', 1),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-************', 'Count me in! When do registrations open?', 3),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0005', 'I''ve been practicing for weeks!', 2),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'This is going to be epic!', 1),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-********0001', 'What''s the name of the game?', 4),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'I love discovering new indie games!', 2),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'Thanks for the recommendation!', 1),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-********0002', 'Adding this to my wishlist!', 1),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'Mobile gaming is the future!', 3),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-************', 'The graphics look amazing!', 2),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-********0005', 'Can''t wait to try this game!', 1),
('20000000-0000-0000-0000-************', '********-0000-0000-0000-********0002', 'Great content as always!', 2),
('20000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', 'Excited to see what''s coming!', 1)
ON CONFLICT DO NOTHING;

-- Insert sample follows
INSERT INTO public.follows (follower_id, following_id) VALUES
('********-0000-0000-0000-********0001', '********-0000-0000-0000-************'),
('********-0000-0000-0000-********0001', '********-0000-0000-0000-************'),
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0005'),
('********-0000-0000-0000-************', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-************', '********-0000-0000-0000-************'),
('********-0000-0000-0000-************', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-************', '********-0000-0000-0000-************'),
('********-0000-0000-0000-************', '********-0000-0000-0000-********0005'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-************'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-************')
ON CONFLICT (follower_id, following_id) DO NOTHING;
