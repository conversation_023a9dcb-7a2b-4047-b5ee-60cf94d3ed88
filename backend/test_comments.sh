#!/bin/bash

# Test script for comment functionality
# This script tests the comment system with proper authentication

API_URL="http://localhost:8000"
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJhbm9uIiwKICAgICJpc3MiOiAic3VwYWJhc2UtZGVtbyIsCiAgICAiaWF0IjogMTY0MTc2OTIwMCwKICAgICJleHAiOiAxNzk5NTM1NjAwCn0.dc_X5iR_VP_qT0zsiyj_I_OZ2T9FtRU2BBNWN8Bu4GE"
SERVICE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyAgCiAgICAicm9sZSI6ICJzZXJ2aWNlX3JvbGUiLAogICAgImlzcyI6ICJzdXBhYmFzZS1kZW1vIiwKICAgICJpYXQiOiAxNjQxNzY5MjAwLAogICAgImV4cCI6IDE3OTk1MzU2MDAKfQ.DaYlNEoUrrEn2Ig7tqibS-PHK5vgusbcbo7X36XVt4Q"

echo "Testing Comment System..."
echo "========================"

# Test 1: Check if tables exist
echo "1. Checking if tables exist..."
curl -s -H "apikey: $SERVICE_KEY" -H "Authorization: Bearer $SERVICE_KEY" \
  "$API_URL/rest/v1/posts?select=*&limit=1" > /dev/null
if [ $? -eq 0 ]; then
  echo "✓ Posts table accessible"
else
  echo "✗ Posts table not accessible"
fi

curl -s -H "apikey: $SERVICE_KEY" -H "Authorization: Bearer $SERVICE_KEY" \
  "$API_URL/rest/v1/comments?select=*&limit=1" > /dev/null
if [ $? -eq 0 ]; then
  echo "✓ Comments table accessible"
else
  echo "✗ Comments table not accessible"
fi

# Test 2: Create a test user (using service key)
echo -e "\n2. Creating test user..."
USER_ID="00000000-0000-0000-0000-000000000001"
curl -s -X POST \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"id\": \"$USER_ID\",
    \"email\": \"<EMAIL>\",
    \"username\": \"testuser\",
    \"display_name\": \"Test User\"
  }" \
  "$API_URL/rest/v1/users" > /dev/null

if [ $? -eq 0 ]; then
  echo "✓ Test user created"
else
  echo "✗ Failed to create test user"
fi

# Test 3: Create a test user profile (using service key)
echo -e "\n3. Creating test user profile..."
curl -s -X POST \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"$USER_ID\",
    \"first_name\": \"Test\",
    \"last_name\": \"User\",
    \"country\": \"Test Country\",
    \"language\": \"en\"
  }" \
  "$API_URL/rest/v1/user_profiles" > /dev/null

if [ $? -eq 0 ]; then
  echo "✓ Test user profile created"
else
  echo "✗ Failed to create test user profile"
fi

# Test 4: Create a test channel
echo -e "\n4. Creating test channel..."
CHANNEL_ID="00000000-0000-0000-0000-000000000001"
curl -s -X POST \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d "{
    \"id\": \"$CHANNEL_ID\",
    \"name\": \"test-channel\",
    \"description\": \"Test channel for comment testing\",
    \"owner_id\": \"$USER_ID\"
  }" \
  "$API_URL/rest/v1/channels" > /dev/null

if [ $? -eq 0 ]; then
  echo "✓ Test channel created"
else
  echo "✗ Failed to create test channel"
fi

# Test 5: Create a test post
echo -e "\n5. Creating test post..."
POST_RESPONSE=$(curl -s -X POST \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -H "Prefer: return=representation" \
  -d "{
    \"user_id\": \"$USER_ID\",
    \"content\": \"This is a test post for comment testing\",
    \"channel_id\": \"$CHANNEL_ID\"
  }" \
  "$API_URL/rest/v1/posts")

POST_ID=$(echo "$POST_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$POST_ID" ]; then
  echo "✓ Test post created with ID: $POST_ID"
else
  echo "✗ Failed to create test post"
  echo "Response: $POST_RESPONSE"
  exit 1
fi

# Test 6: Create a test comment
echo -e "\n6. Creating test comment..."
COMMENT_RESPONSE=$(curl -s -X POST \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -H "Prefer: return=representation" \
  -d "{
    \"post_id\": \"$POST_ID\",
    \"user_id\": \"$USER_ID\",
    \"content\": \"This is a test comment\"
  }" \
  "$API_URL/rest/v1/comments")

COMMENT_ID=$(echo "$COMMENT_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$COMMENT_ID" ]; then
  echo "✓ Test comment created with ID: $COMMENT_ID"
else
  echo "✗ Failed to create test comment"
  echo "Response: $COMMENT_RESPONSE"
fi

# Test 7: Check if comment count was updated
echo -e "\n7. Checking if post comment count was updated..."
POST_CHECK=$(curl -s -H "apikey: $SERVICE_KEY" -H "Authorization: Bearer $SERVICE_KEY" \
  "$API_URL/rest/v1/posts?id=eq.$POST_ID&select=comment_count")

COMMENT_COUNT=$(echo "$POST_CHECK" | grep -o '"comment_count":[0-9]*' | cut -d':' -f2)

if [ "$COMMENT_COUNT" = "1" ]; then
  echo "✓ Comment count correctly updated to 1"
else
  echo "✗ Comment count not updated correctly. Current count: $COMMENT_COUNT"
  echo "Response: $POST_CHECK"
fi

# Test 8: Create another comment to test increment
echo -e "\n8. Creating second comment..."
COMMENT2_RESPONSE=$(curl -s -X POST \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -H "Prefer: return=representation" \
  -d "{
    \"post_id\": \"$POST_ID\",
    \"user_id\": \"$USER_ID\",
    \"content\": \"This is a second test comment\"
  }" \
  "$API_URL/rest/v1/comments")

COMMENT2_ID=$(echo "$COMMENT2_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ ! -z "$COMMENT2_ID" ]; then
  echo "✓ Second test comment created with ID: $COMMENT2_ID"
else
  echo "✗ Failed to create second test comment"
fi

# Test 9: Check if comment count was incremented
echo -e "\n9. Checking if comment count was incremented..."
POST_CHECK2=$(curl -s -H "apikey: $SERVICE_KEY" -H "Authorization: Bearer $SERVICE_KEY" \
  "$API_URL/rest/v1/posts?id=eq.$POST_ID&select=comment_count")

COMMENT_COUNT2=$(echo "$POST_CHECK2" | grep -o '"comment_count":[0-9]*' | cut -d':' -f2)

if [ "$COMMENT_COUNT2" = "2" ]; then
  echo "✓ Comment count correctly incremented to 2"
else
  echo "✗ Comment count not incremented correctly. Current count: $COMMENT_COUNT2"
fi

# Test 10: Delete a comment and check decrement
echo -e "\n10. Deleting a comment and checking decrement..."
curl -s -X DELETE \
  -H "apikey: $SERVICE_KEY" \
  -H "Authorization: Bearer $SERVICE_KEY" \
  "$API_URL/rest/v1/comments?id=eq.$COMMENT2_ID" > /dev/null

# Check comment count after deletion
POST_CHECK3=$(curl -s -H "apikey: $SERVICE_KEY" -H "Authorization: Bearer $SERVICE_KEY" \
  "$API_URL/rest/v1/posts?id=eq.$POST_ID&select=comment_count")

COMMENT_COUNT3=$(echo "$POST_CHECK3" | grep -o '"comment_count":[0-9]*' | cut -d':' -f2)

if [ "$COMMENT_COUNT3" = "1" ]; then
  echo "✓ Comment count correctly decremented to 1 after deletion"
else
  echo "✗ Comment count not decremented correctly. Current count: $COMMENT_COUNT3"
fi

echo -e "\n========================"
echo "Comment system test completed!"
echo "========================"
