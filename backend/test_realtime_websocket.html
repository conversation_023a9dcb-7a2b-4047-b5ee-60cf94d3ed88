<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase Realtime WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        #messages { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🚀 Supabase Realtime WebSocket Test</h1>
    
    <div id="status" class="status info">
        Ready to test WebSocket connection...
    </div>
    
    <button onclick="connectWebSocket()">Connect to Realtime</button>
    <button onclick="subscribeToTable()">Subscribe to realtime_test</button>
    <button onclick="insertTestRecord()">Insert Test Record</button>
    <button onclick="disconnect()">Disconnect</button>
    
    <h3>Messages:</h3>
    <div id="messages"></div>
    
    <script>
        const SUPABASE_URL = 'ws://localhost:8000/realtime/v1/websocket';
        const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
        
        let socket = null;
        let isConnected = false;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function addMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const timestamp = new Date().toLocaleTimeString();
            messagesDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function connectWebSocket() {
            if (socket && isConnected) {
                addMessage('Already connected!');
                return;
            }
            
            addMessage('🔌 Connecting to Supabase Realtime...');
            updateStatus('Connecting...', 'info');
            
            socket = new WebSocket(SUPABASE_URL, ['websocket']);
            
            socket.onopen = function(event) {
                isConnected = true;
                addMessage('✅ WebSocket connection opened!');
                updateStatus('Connected to Realtime', 'success');
                
                // Send initial message
                const joinMessage = {
                    topic: 'phoenix',
                    event: 'heartbeat',
                    payload: {},
                    ref: '1'
                };
                socket.send(JSON.stringify(joinMessage));
                addMessage('📤 Sent heartbeat message');
            };
            
            socket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                addMessage(`📥 Received: ${JSON.stringify(data)}`);
            };
            
            socket.onerror = function(error) {
                addMessage(`❌ WebSocket error: ${error}`);
                updateStatus('Connection error', 'error');
            };
            
            socket.onclose = function(event) {
                isConnected = false;
                addMessage(`🔌 Connection closed: ${event.code} ${event.reason}`);
                updateStatus('Disconnected', 'info');
            };
        }
        
        function subscribeToTable() {
            if (!socket || !isConnected) {
                addMessage('❌ Not connected! Connect first.');
                return;
            }
            
            const subscribeMessage = {
                topic: 'realtime:public:realtime_test',
                event: 'phx_join',
                payload: {},
                ref: '2'
            };
            
            socket.send(JSON.stringify(subscribeMessage));
            addMessage('📤 Subscribed to realtime_test table');
        }
        
        function insertTestRecord() {
            // This would normally be done through the REST API
            addMessage('💡 To test realtime updates, insert a record via REST API or database directly');
            addMessage('💡 Example: INSERT INTO public.realtime_test (message) VALUES (\'Test message\');');
        }
        
        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
                isConnected = false;
                addMessage('🔌 Disconnected');
                updateStatus('Disconnected', 'info');
            }
        }
        
        // Auto-connect on page load
        window.onload = function() {
            addMessage('🌟 Realtime WebSocket Test Ready');
            addMessage('📋 Instructions:');
            addMessage('1. Click "Connect to Realtime" to establish WebSocket connection');
            addMessage('2. Click "Subscribe to realtime_test" to listen for table changes');
            addMessage('3. Insert records into the realtime_test table to see real-time updates');
        };
    </script>
</body>
</html>
