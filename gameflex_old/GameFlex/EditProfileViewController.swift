//
//  EditProfileViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum EditProfileCellType {
    case picture, name, flexterName, userDescription, button, none, error
}

class EditProfileViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
        
    weak var delegate: ProfileDelegate?
    
    var nameText = ""
    var flexterNameText = ""
    var userDescriptionText = ""
    var kNameTag = 455
    var kFlexterNameTag = 456
    var searchResults: [SearchResult] = []
    
    var sequenceOfCells:[EditProfileCellType] = [.name, .flexterName, .userDescription, .button, .none]
    
    static func storyboardInstance() -> EditProfileViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: EditProfileViewController.self)) as! EditProfileViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: String(describing: UITableViewCell.self))
        tableView.register(UINib(nibName: String(describing: EditProfileTextViewTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileTextViewTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: String(describing: EditProfileTextFieldTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: String(describing: EditProfileButtonTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileButtonTableViewCell.cellIdentifier)
       tableView.tableFooterView = UIView()
        let tap = UITapGestureRecognizer(target: self, action: #selector(didTapView))
        view.addGestureRecognizer(tap)
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    @IBAction func didTapButton(_ sender: UIButton)  {
    }

    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Edit Profile"
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
    }
    
    func showError() {
        sequenceOfCells = [.name, .error, .flexterName, .userDescription, .button, .none]
        tableView.reloadData()
    }
    
    func hideError() {
        sequenceOfCells = [.name, .flexterName, .userDescription, .button, .none]
        tableView.reloadData()
    }
    
    func getNextNames() -> String {
        var short = "Flex\(Int.random(in: 0...1000))"
        if !searchResults.isEmpty {
            short = searchResults.sorted(by: { $0.name?.count ?? 0 < $1.name?.count ?? 0 })[0].name ?? "Flex"
                short = "\(short)\(Int.random(in: 0...1000))"
        }
        let firstName = "\(RandomNames.getRandomName(letters: User.searchOnNumberOfLetters+2, length: User.searchOnNumberOfLetters+4))"
        
        return "\(short) or \(firstName)"
    }
    
}

extension EditProfileViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sequenceOfCells.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch sequenceOfCells[indexPath.row] {
        case .error:
            let cell = tableView.dequeueReusableCell(withIdentifier: String(describing: UITableViewCell.self), for: indexPath)
            cell.backgroundColor = .clear
            cell.selectionStyle = .none
            cell.textLabel?.numberOfLines = 0
            cell.textLabel?.textColor = .gfYellow_F2DE76
            if flexterNameText.replacingOccurrences(of: " ", with: "").count > 0 {
                cell.textLabel?.text = "\(flexterNameText) is taken. Choose another.\nHow about \(getNextNames())?"
            } else {
                cell.textLabel?.text = "Not a valid name. Choose another.\nHow about \(getNextNames())?"
            }
            cell.textLabel?.font = .systemFont(ofSize: 13)
            return cell
        
        case .name:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextFieldTableViewCell else { return EditProfileTextFieldTableViewCell() }
            cell.titleLabel.text = "Full Name"
            if User.flexter.name != nil, User.flexter.name != "" {
                cell.textField.text = User.flexter.name
            } else if nameText != "" {
                cell.textField.text = nameText
            } else {
                cell.textField.placeholder = "editProfile.name.placeholder".localized
            }
            cell.delegate = self
            cell.selectionStyle = .none
            cell.cellType = .name
            cell.textField.tag = kNameTag
            return cell
        case .flexterName:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextFieldTableViewCell else { return EditProfileTextFieldTableViewCell() }
            cell.titleLabel.text = "Flexter Name"
            if cell.textField.text == "", User.flexter.flexterName != nil, User.flexter.flexterName != "" {
                cell.textField.text = User.flexter.flexterName
            } else if flexterNameText != "" {
                cell.textField.text = flexterNameText
            } else {
                cell.textField.placeholder = "editProfile.flexterName.placeholder".localized
            }
            cell.delegate = self
            cell.selectionStyle = .none
            cell.cellType = .flexterName
            cell.textField.tag = kFlexterNameTag
            return cell
        case .userDescription:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextViewTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextViewTableViewCell else { return EditProfileTextViewTableViewCell() }
            cell.titleLabel.text = "User Description"
            if User.flexter.userDescription != nil, User.flexter.userDescription != "" {
                cell.textView.text = User.flexter.userDescription
            } else if userDescriptionText != "" {
                cell.textView.text = userDescriptionText
            } else if userDescriptionText == "" {
                cell.textView.text = "editProfile.textView.placeholder".localized
                cell.textView.textColor = .darkGray
            }
            cell.delegate = self
            cell.selectionStyle = .none
            return cell
        case .button:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileButtonTableViewCell.cellIdentifier, for: indexPath) as? EditProfileButtonTableViewCell else { return EditProfileButtonTableViewCell() }
            cell.delegate = self
            cell.selectionStyle = .none
            return cell
        default: break
        }
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch sequenceOfCells[indexPath.row] {
        case .error: return 65
        case .name, .flexterName: return 91
        case .userDescription: return 100
        case .button: return 84
        default: return 200
        }
    }
    
}

extension EditProfileViewController: ProfileDelegate {
    
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        switch type {
        case .checkForTaken:
            guard searchResults.filter({ $0.name?.lowercased() == flexterNameText.lowercased() }).count == 0 else {
                self.showError()
                return
            }
            hideError()
        case .makeTheCall:
            guard searchResults.filter({ $0.name?.lowercased() == flexterNameText.lowercased() }).count == 0,
                  flexterNameText.replacingOccurrences(of: " ", with: "").count > 0 else {
                showError()
                return
            }
            Utilities.showSpinner()
            var content: [ProfileUpdateDataType: String] = [:]
            if nameText != "" {
                content[.name] = nameText
            }
            if flexterNameText != "" {
                content[.flexterName] = flexterNameText
            }
            if userDescriptionText != "", userDescriptionText != "editProfile.textView.placeholder".localized {
                content[.userDescription] = userDescriptionText
            }
            GFNetworkServices.patchUserProfile(content: content) { (success, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    guard error == nil else {
                        let alert = UIAlertController(title: "Error?", message: "error.houston".localized, preferredStyle: .alert)
                        let ok = UIAlertAction(title: "OK", style: .default) { _ in
                            self.navigationController?.popViewController(animated: true)
                        }
                        alert.addAction(ok)
                        self.present(alert, animated: true, completion: nil)
                        return
                    }
                    let alert = UIAlertController(title: "Success", message: "Changes accepted.", preferredStyle: .alert)
                    let ok = UIAlertAction(title: "OK", style: .default) { _ in
                        self.navigationController?.popViewController(animated: true)
                    }
                    alert.addAction(ok)
                    self.present(alert, animated: true, completion: nil)
                    
                }
            }
        default: break
        }
    }
    
    func updateTextFor(_ type: ProfileUpdateDataType, text: String) {
        switch type {
        case .flexterName:
            flexterNameText = text
            if text.count == User.searchOnNumberOfLetters {
                GFNetworkServices.getSearchResults(text) { (success, results, error) in
                    guard error == nil else {
                        return
                    }
                    if results.count > 95 {
                        User.searchOnNumberOfLetters = User.searchOnNumberOfLetters + 1
                    }
                    self.searchResults.append(contentsOf: results)
                    self.searchResults.sort(by: { $0.name?.lowercased() ?? "" < $1.name?.lowercased() ?? "" })
                    
                }
            }
        case .name:
            nameText = text
        case .userDescription:
            userDescriptionText = text
        default: break
        }
    }
}
