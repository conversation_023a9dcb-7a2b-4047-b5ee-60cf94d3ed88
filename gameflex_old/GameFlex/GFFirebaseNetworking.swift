//
//  GFFirebaseNetworking.swift
//  GameFlex
//
//  Created by <PERSON> on 9/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation

class GFFirebaseNetworking {
    
    static let shared = GFFirebaseNetworking()
    
    static let kSlackFlexerFeedbackURL = "*********************************************************************************"
    
    
    // MARK: - Firebase upload of media
    static func sendFlexerFeedback(message: String, mediaLink: String?, closure: @escaping (_ success: Bool, _ error: Error? ) -> Void) {
        let url = URL(string: kSlackFlexerFeedbackURL)
        var request = URLRequest(url: url!)
        request.httpMethod = "POST"
        var content = "Fullname: \(User.flexter.name ?? "")\nEmail:\(User.flexter.email ?? "")\nFlexterName: \(User.flexter.flexterName ?? "")\nDevice: \(User.device ?? "")\niOS: \(User.ios ?? "")\nApp: \(User.app ?? "")\nComment: \(message)"
        
        if let link = mediaLink {
            // attach this movie/picture
            if !link.contains(".png") {
                content = content + "\n<\(link)|Movie>"
            }
            var imageBlock = Dictionary<String, String>()
            imageBlock["alt_text"] = "Another amazing GameFlex feedback"
            if !link.contains(".png") {
                imageBlock["image_url"] = "https://firebasestorage.googleapis.com/v0/b/gameflex-59ffa.appspot.com/o/flexerImages%2Fplay.png?alt=media&token=f1956ad9-859a-4d72-ad51-350429fef228"
            } else {
                imageBlock["image_url"] = "\(link)"
            }
            imageBlock["type"] = "image"
            var textBlock = Dictionary<String, Any>()
            textBlock["type"] = "section"
            textBlock["text"] = ["text": content, "type": "mrkdwn"]
            request.httpBody = try? JSONSerialization.data(withJSONObject: ["blocks": [imageBlock, textBlock]], options: .prettyPrinted)
        } else {
            request.httpBody = try? JSONSerialization.data(withJSONObject: ["text": content], options: .prettyPrinted)
        }
        
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.addValue("application/json", forHTTPHeaderField: "Accept")
        let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
            if let error = error {
                print ("error 31: \(error.localizedDescription)")
                closure(false, error)
            }
            guard let response = response as? HTTPURLResponse,
                (200...299).contains(response.statusCode) else {
                    closure(false, error)
                    print ("error 37")
                    return
            }
            closure(true, nil)
        })
        task.resume()
    }
}
