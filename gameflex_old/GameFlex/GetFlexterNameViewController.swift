//
//  GetFlexterNameViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 11/14/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class GetFlexterNameViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
        
    weak var delegate: ProfileDelegate?
    
    var nameText = ""
    var flexterNameText = ""
    var userDescriptionText = ""
    var kNameTag = 455
    var kFlexterNameTag = 456
    var searchResults: [SearchResult] = []
    var didTapEditPicture = false
    var isFromProfile = false
    
    var sequenceOfCells:[EditProfileCellType] = [.picture, .name, .flexterName, .userDescription, .button, .none]
    
    // MARK: -
    
    static func storyboardInstance() -> GetFlexterNameViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: GetFlexterNameViewController.self)) as! GetFlexterNameViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: String(describing: UITableViewCell.self))
        tableView.register(UINib(nibName: String(describing: EditProfileTextViewTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileTextViewTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: String(describing: EditProfileTextFieldTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: String(describing: EditProfileButtonTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileButtonTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: EditProfilePictureTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: EditProfilePictureTableViewCell.cellIdentifier)
       tableView.tableFooterView = UIView()
        let tap = UITapGestureRecognizer(target: self, action: #selector(didTapView))
        view.addGestureRecognizer(tap)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Identify Yourself"
        if let flexterId = User.flexter.userId, flexterId != "" {
            Utilities.showSpinner()
            GFNetworkServices.getUserProfile(flexterId) { (success, flexter, error) in
                guard error == nil else {
                    
                    return
                }
                if success {
                    if let flexter = flexter {
                        User.flexter = flexter
                    }
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        self.tableView.reloadData()
                    }
                }
            }
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        didTapEditPicture = false
        tableView.reloadData()
        tabBarController?.tabBar.isHidden = true
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    @IBAction func didTapButton(_ sender: UIButton)  {
    }
    
    func showError() {
        sequenceOfCells = [.picture, .name, .error, .flexterName, .userDescription, .button, .none]
        tableView.reloadData()
    }
    
    func hideError() {
        sequenceOfCells = [.picture, .name, .flexterName, .userDescription, .button, .none]
        tableView.reloadData()
    }
    
    func getNextNames() -> String {
        var short = "Flex\(Int.random(in: 0...1000))"
        if !searchResults.isEmpty {
            short = searchResults.sorted(by: { $0.name?.count ?? 0 < $1.name?.count ?? 0 })[0].name ?? "Flex"
                short = "\(short)\(Int.random(in: 0...1000))"
        }
        let firstName = "\(RandomNames.getRandomName(letters: User.searchOnNumberOfLetters+2, length: User.searchOnNumberOfLetters+4))"
        
        return "\(short) or \(firstName)"
    }
    
}

extension GetFlexterNameViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sequenceOfCells.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch sequenceOfCells[indexPath.row] {
        case .picture:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfilePictureTableViewCell.cellIdentifier, for: indexPath) as? EditProfilePictureTableViewCell else { return EditProfilePictureTableViewCell() }
            cell.delegate = self
            return cell
        
        case .error:
            let cell = tableView.dequeueReusableCell(withIdentifier: String(describing: UITableViewCell.self), for: indexPath)
            cell.backgroundColor = .clear
            cell.selectionStyle = .none
            cell.textLabel?.numberOfLines = 0
            cell.textLabel?.textColor = .gfYellow_F2DE76
            if flexterNameText.replacingOccurrences(of: " ", with: "").count > 0 {
                cell.textLabel?.text = "\(flexterNameText) is taken. Choose another.\nHow about \(getNextNames())?"
            } else {
                cell.textLabel?.text = "Not a valid name. Choose another.\nHow about \(getNextNames())?"
            }
            cell.textLabel?.font = .systemFont(ofSize: 13)
            return cell
        
        case .name:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextFieldTableViewCell else { return EditProfileTextFieldTableViewCell() }
            cell.titleLabel.text = "Full Name"
            if let name = User.flexter.name, name != "" {
                cell.textField.text = name
                nameText = name
            } else if nameText != "" {
                cell.textField.text = nameText
            } else {
                cell.textField.placeholder = "editProfile.name.placeholder".localized
            }
            cell.delegate = self
            cell.selectionStyle = .none
            cell.cellType = .name
            cell.textField.tag = kNameTag
            return cell
        case .flexterName:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextFieldTableViewCell else { return EditProfileTextFieldTableViewCell() }
            cell.titleLabel.text = "Flexter Name"
            if cell.textField.text == "", let flexterName = User.flexter.flexterName, flexterName != "" {
                cell.textField.text = flexterName
                flexterNameText = flexterName
            } else if flexterNameText != "" {
                cell.textField.text = flexterNameText
            } else {
                cell.textField.placeholder = "editProfile.flexterName.placeholder".localized
            }
            cell.delegate = self
            cell.selectionStyle = .none
            cell.cellType = .flexterName
            cell.textField.tag = kFlexterNameTag
            return cell
        case .userDescription:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextViewTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextViewTableViewCell else { return EditProfileTextViewTableViewCell() }
            cell.titleLabel.text = "User Description"
            if let desc = User.flexter.userDescription, desc != "" {
                cell.textView.text = desc
                userDescriptionText = desc
            } else if userDescriptionText != "" {
                cell.textView.text = userDescriptionText
            } else if userDescriptionText == "" {
                cell.textView.text = "editProfile.textView.placeholder".localized
                cell.textView.textColor = .darkGray
            }
            cell.delegate = self
            cell.selectionStyle = .none
            return cell
        case .button:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileButtonTableViewCell.cellIdentifier, for: indexPath) as? EditProfileButtonTableViewCell else { return EditProfileButtonTableViewCell() }
            cell.delegate = self
            cell.selectionStyle = .none
            return cell
        case .none: break
        }
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch sequenceOfCells[indexPath.row] {
        case .error: return 65
        case .name, .flexterName: return 91
        case .userDescription: return 100
        case .button: return 84
        case .picture: return 100
        case .none: return 200
        }
    }
    
}

extension GetFlexterNameViewController: ProfileDelegate {

    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        switch type {
        case .checkForTaken:
            guard searchResults.filter({ $0.name?.lowercased() == flexterNameText.lowercased() }).count == 0 else {
                guard flexterNameText != User.flexter.flexterName else { return }
                self.showError()
                return
            }
            hideError()
        case .makeTheCall:
            if flexterNameText != User.flexter.flexterName {
                guard searchResults.filter({ $0.name?.lowercased() == flexterNameText.lowercased() }).count == 0,
                      flexterNameText.replacingOccurrences(of: " ", with: "").count > 0 else {
                    showError()
                    return
                }
            }
            var content: [ProfileUpdateDataType: String] = [:]
            if nameText != "", nameText != User.flexter.name {
                content[.name] = nameText
            }
            if flexterNameText != "", flexterNameText != User.flexter.flexterName {
                content[.flexterName] = flexterNameText
            }
            if userDescriptionText != "", userDescriptionText != "editProfile.textView.placeholder".localized, userDescriptionText != User.flexter.userDescription {
                content[.userDescription] = userDescriptionText
            }
            if content.count > 0 {
                Utilities.showSpinner()
                GFNetworkServices.patchUserProfile(content: content) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        guard error == nil else {
                            let alert = UIAlertController(title: "Error?", message: "error.houston".localized, preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default) { _ in
                                self.navigationController?.popViewController(animated: true)
                            }
                            alert.addAction(ok)
                            self.present(alert, animated: true, completion: nil)
                            return
                        }
                        let alert = UIAlertController(title: "Success", message: "Changes accepted.", preferredStyle: .alert)
                        let ok = UIAlertAction(title: "OK", style: .default) { _ in
                            if self.isFromProfile {
                                self.isFromProfile = false
                                self.navigationController?.popViewController(animated: true)
                            } else {
                                self.isFromProfile = false
                                OnboardingStateMachine.didComplete(this: .flexterName)
                            }
                        }
                        alert.addAction(ok)
                        self.present(alert, animated: true, completion: nil)
                    }
                }                
            } else {
                if self.isFromProfile {
                    self.isFromProfile = false
                    self.navigationController?.popViewController(animated: true)
                } else {
                    OnboardingStateMachine.didComplete(this: .flexterName)
                }
            }
        case .photo:
            didTapEditPicture = true
            DispatchQueue.main.async {
                let pvc = PortraitViewController.storyboardInstance()
                self.navigationController?.pushViewController(pvc, animated: true)
                return
            }
        default: break
        }
    }
    
    func updateTextFor(_ type: ProfileUpdateDataType, text: String) {
        switch type {
        case .flexterName:
            flexterNameText = text
            if text.count == User.searchOnNumberOfLetters {
                GFNetworkServices.getSearchResults(text) { (success, results, error) in
                    guard error == nil else {
                        return
                    }
                    if results.count > 95 {
                        User.searchOnNumberOfLetters = User.searchOnNumberOfLetters + 1
                    }
                    self.searchResults.append(contentsOf: results)
                    self.searchResults.sort(by: { $0.name?.lowercased() ?? "" < $1.name?.lowercased() ?? "" })
                    
                }
            }
        case .name:
            nameText = text
        case .userDescription:
            userDescriptionText = text
        default: break
        }
    }
}
