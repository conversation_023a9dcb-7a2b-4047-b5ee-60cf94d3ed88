//
//  CreateChannelViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 11/14/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class CreateChannelViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
        
    weak var delegate: ProfileDelegate?
    
    var nameText = ""
    var flexterNameText = ""
    var userDescriptionText = ""
    var kNameTag = 455
    var kFlexterNameTag = 456
    var searchResults: [SearchResult] = []
    var didTapEditPicture = false
    var color = CreateChannelViewModel.randomChannelColor
    var isModal = false
    var isOwner = false
    
    var sequenceOfCells:[EditProfileCellType] = [.picture, .flexterName, .userDescription, .button, .none]
    
    // MARK: - LifeCycle
    
    static func storyboardInstance() -> CreateChannelViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: CreateChannelViewController.self)) as! CreateChannelViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: String(describing: UITableViewCell.self))
        tableView.register(UINib(nibName: String(describing: EditProfileTextViewTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileTextViewTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: String(describing: EditProfileTextFieldTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: String(describing: EditProfileButtonTableViewCell.self), bundle: nil), forCellReuseIdentifier: EditProfileButtonTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: CreateChannelPictureTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: CreateChannelPictureTableViewCell.cellIdentifier)
       tableView.tableFooterView = UIView()
        let tap = UITapGestureRecognizer(target: self, action: #selector(didTapView))
        view.addGestureRecognizer(tap)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Channel Details"
        if isModal {
            addCancelToNavBar()
        } else {
            addSettingsToNavBar()
        }
        if !User.isLoggedIn {
            tabBarController?.selectedIndex = 0
        }
        if let channelDetail = FeedViewModel.channelDetail {
            flexterNameText = channelDetail.channelName ?? ""
            userDescriptionText = channelDetail.channelDescription ?? ""
            if User.userId == channelDetail.channelOwner?.channelId {
                isOwner = true
            } else {
                isOwner = false
            }
        }
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        didTapEditPicture = false
        tableView.reloadData()
        tabBarController?.tabBar.isHidden = true
        if CreateChannelViewModel.profileImage != nil {
            tableView.reloadData()
        }
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        tabBarController?.tabBar.isHidden = false
        title = ""
    }
    
    // MARK: - content editing controls
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    @IBAction func didTapButton(_ sender: UIButton)  {
    }
    
    func showError() {
        sequenceOfCells = [.picture, .error, .flexterName, .userDescription, .button, .none]
        tableView.reloadData()
    }
    
    func hideError() {
        sequenceOfCells = [.picture, .flexterName, .userDescription, .button, .none]
        tableView.reloadData()
    }
    
    func getNextNames() -> String {
        var short = "Flex\(Int.random(in: 0...1000))"
        if !searchResults.isEmpty {
            short = searchResults.sorted(by: { $0.name?.count ?? 0 < $1.name?.count ?? 0 })[0].name ?? "Flex"
                short = "\(short)\(Int.random(in: 0...1000))"
        }
        let firstName = "\(RandomNames.getRandomName(letters: User.searchOnNumberOfLetters+2, length: User.searchOnNumberOfLetters+4))"
        
        return "\(short) or \(firstName)"
    }
    
}

// MARK: - UITableView Delegate and DataSource protocols
extension CreateChannelViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sequenceOfCells.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        switch sequenceOfCells[indexPath.row] {
        case .picture:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: CreateChannelPictureTableViewCell.cellIdentifier, for: indexPath) as? CreateChannelPictureTableViewCell else { return CreateChannelPictureTableViewCell() }
            if FeedViewModel.channelDetail != nil {
                cell.delegate = self
                if cell.profileImageView != nil, let urlString = FeedViewModel.channelDetail?.channelImage {
                    cell.profileImageView.clipsToBounds = true
                    let image = UIImage(named: "bigG")
                    let url = URL(string: urlString)
                    let processor = DownsamplingImageProcessor(size: cell.profileImageView.bounds.size)
                    cell.profileImageView?.kf.setImage(
                        with: url,
                        placeholder: image,
                        options: [
                            .processor(processor),
                            .scaleFactor(UIScreen.main.scale),
                            .transition(.fade(1)),
                            .cacheOriginalImage
                        ], completionHandler:
                            {
                                result in
                                switch result {
                                case .success:
                                    print("success channel image downloaded")
                                case .failure(let error):
                                    print("Job failed: \(error.localizedDescription)")
                                    cell.profileImageView?.image = image
                                }
                            })
                }
                if isOwner {
                    cell.changeColorButton.isHidden = false
                    cell.editButton.isHidden = false
                    cell.editLabelOnlyButton.isHidden = false
                } else {
                    cell.changeColorButton.isHidden = true
                    cell.editButton.isHidden = true
                    cell.editLabelOnlyButton.isHidden = true
                }
                
                return cell
            }
            cell.profileImageView.backgroundColor = self.color
            cell.delegate = self
            if CreateChannelViewModel.profileImage != nil {
                cell.profileImageView.image = CreateChannelViewModel.profileImage
            }
            return cell
        
        case .error:
            let cell = tableView.dequeueReusableCell(withIdentifier: String(describing: UITableViewCell.self), for: indexPath)
            cell.backgroundColor = .clear
            cell.selectionStyle = .none
            cell.textLabel?.numberOfLines = 0
            cell.textLabel?.textColor = .gfYellow_F2DE76
            if flexterNameText.replacingOccurrences(of: " ", with: "").count > 0 {
                cell.textLabel?.text = "\(flexterNameText) is taken. Choose another.\nHow about \(getNextNames())?"
            } else {
                cell.textLabel?.text = "Not a valid name. Choose another.\nHow about \(getNextNames())?"
            }
            cell.textLabel?.font = .systemFont(ofSize: 13)
            return cell
        
        case .name: // not used
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextFieldTableViewCell else { return EditProfileTextFieldTableViewCell() }
            cell.titleLabel.text = "Channel Full Name"
            if nameText != "" {
                cell.textField.text = nameText
            } else {
                cell.textField.placeholder = "editChannel.name.placeholder".localized
            }
            cell.delegate = self
            cell.selectionStyle = .none
            cell.cellType = .name
            cell.textField.tag = kNameTag
            return cell
        case .flexterName:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextFieldTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextFieldTableViewCell else { return EditProfileTextFieldTableViewCell() }
            cell.titleLabel.text = "Channel Name"
            if flexterNameText != "" {
                cell.textField.text = flexterNameText
            } else {
                cell.textField.placeholder = "editChannel.flexterName.placeholder".localized
            }
            cell.delegate = self
            cell.selectionStyle = .none
            cell.cellType = .flexterName
            cell.textField.tag = kFlexterNameTag
            if isOwner {
                cell.textField.isUserInteractionEnabled = true
            } else {
                cell.textField.isUserInteractionEnabled = false
            }
            return cell
        case .userDescription:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileTextViewTableViewCell.cellIdentifier, for: indexPath) as? EditProfileTextViewTableViewCell else { return EditProfileTextViewTableViewCell() }
            cell.editingChannel = true
            cell.titleLabel.text = "Channel Mission"
            if userDescriptionText != "" {
                cell.textView.text = userDescriptionText
            } else if userDescriptionText == "" {
                cell.textView.text = "editChannel.textView.placeholder".localized
                cell.textView.textColor = .darkGray
            }
            cell.delegate = self
            cell.selectionStyle = .none
            if isOwner {
                cell.textView.isSelectable = true
                cell.textView.isEditable = true
                cell.textView.isUserInteractionEnabled = true
            } else {
                cell.textView.isSelectable = false
                cell.textView.isEditable = false
                cell.textView.isUserInteractionEnabled = false
            }
            
            return cell
        case .button:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EditProfileButtonTableViewCell.cellIdentifier, for: indexPath) as? EditProfileButtonTableViewCell else { return EditProfileButtonTableViewCell() }
            cell.delegate = self
            cell.selectionStyle = .none
            if isOwner {
                cell.button.isHidden = false
            } else {
                cell.button.isHidden = true
            }
            return cell
        case .none: break
        }
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        switch sequenceOfCells[indexPath.row] {
        case .error: return 65
        case .name, .flexterName: return 91
        case .userDescription: return 100
        case .button: return 84
        case .picture: return 100
        case .none: return 200
        }
    }
    
}

// MARK: - Profile Delegate service
extension CreateChannelViewController: ProfileDelegate {

    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        switch type {
        case .checkForTaken:
            guard searchResults.filter({ $0.name?.lowercased() == flexterNameText.lowercased() }).count == 0 else {
                guard flexterNameText != User.flexter.flexterName else { return }
                self.showError()
                return
            }
            hideError()
        case .makeTheCall:
            if flexterNameText != User.flexter.flexterName {
                guard searchResults.filter({ $0.name?.lowercased() == flexterNameText.lowercased() }).count == 0,
                      flexterNameText.replacingOccurrences(of: " ", with: "").count > 0 else {
                    showError()
                    return
                }
            }
            Utilities.showSpinner()
            
            
            var content: [ProfileUpdateDataType: String] = [:]
            if nameText != "" {
                content[.name] = nameText
            }
            if flexterNameText != "", flexterNameText != User.flexter.flexterName {
                // NOTE: this is what the channel API expects, this is the specific unique key in the database
                content[.name] = flexterNameText
            }
            if userDescriptionText != "", userDescriptionText != "editChannel.textView.placeholder".localized {
                content[.description] = userDescriptionText
            }
            CreateChannelViewModel.createChannelParameters = content
            // upload image from camera/library or the channelMask
            let im = UIImageView(image: #imageLiteral(resourceName: "channelMask"))
            im.backgroundColor = color
            let imaged = im.asImage()
            let imageToPass = CreateChannelViewModel.profileImage != nil ? CreateChannelViewModel.profileImage : imaged // #imageLiteral(resourceName: "channelMask")
            if let image = imageToPass {
                Utilities.showSpinner()
                GFNetworkServices.createChannel(image: image) { (success, channel, error) in
                    if success, let channelId = channel?.channelId {
                        GFNetworkServices.getChannelDetailFor(channelId: channelId) { (success, details, error) in
                            guard error == nil else {
                                DispatchQueue.main.async {
                                    Utilities.hideSpinner()
                                    let alert = UIAlertController(title: "Error?", message: "error.houston".localized, preferredStyle: .alert)
                                    let ok = UIAlertAction(title: "OK", style: .default) { _ in
                                        self.navigationController?.popViewController(animated: true)
                                    }
                                    alert.addAction(ok)
                                    self.present(alert, animated: true, completion: nil)
                                }
                                return
                            }
                            DispatchQueue.main.async {
                                Utilities.hideSpinner()
                                let alert = UIAlertController(title: "Success", message: "Channel created. Next, invite your friends to join your newest channel.", preferredStyle: .alert)
                                let ok = UIAlertAction(title: "OK", style: .default) { _ in
                                    CreateChannelViewModel.createChannelParameters = nil
                                    CreateChannelViewModel.profileImage = nil
                                    let invite = InviteViewController.storyboardInstance()
                                    invite.channelDetail = details
                                    self.navigationController?.pushViewController(invite, animated: true)
                                }
                                alert.addAction(ok)
                                self.present(alert, animated: true, completion: nil)
                            }
                        }
                        return
                    }
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        guard error == nil else {
                            let alert = UIAlertController(title: "Error?", message: "error.houston".localized, preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default) { _ in
                                self.navigationController?.popViewController(animated: true)
                            }
                            alert.addAction(ok)
                            self.present(alert, animated: true, completion: nil)
                            return
                        }
                    }
                }
            }
            
        case .photo:
            didTapEditPicture = true
            DispatchQueue.main.async {
                let pvc = PortraitViewController.storyboardInstance()
                pvc.isFromChannels = true
                self.navigationController?.pushViewController(pvc, animated: true)
                return
            }
        case .changeBackgroundChannelColor:
            self.color = CreateChannelViewModel.randomChannelColor
            tableView.reloadRows(at: [IndexPath(row: 0, section: 0)], with: .fade)
        default: break
        }
    }
    
    func updateTextFor(_ type: ProfileUpdateDataType, text: String) {
        switch type {
        case .flexterName:
            flexterNameText = text
            if text.count == User.searchOnNumberOfLetters {
                GFNetworkServices.getSearchResults(text) { (success, results, error) in
                    guard error == nil else {
                        return
                    }
                    if results.count > 95 {
                        User.searchOnNumberOfLetters = User.searchOnNumberOfLetters + 1
                    }
                    self.searchResults.append(contentsOf: results)
                    self.searchResults.sort(by: { $0.name?.lowercased() ?? "" < $1.name?.lowercased() ?? "" })
                    
                }
            }
        case .name:
            nameText = text
        case .userDescription:
            userDescriptionText = text
        default: break
        }
    }
}
