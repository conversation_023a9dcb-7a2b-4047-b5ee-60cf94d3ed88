//
//  RootViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 11/19/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class RootViewController: UIViewController {
    
    /// enables navigational and presentation override of onboarding viewControllers
    /// transition to the LandingPageViewController on onboarding service completion
    func transition(to: UIViewController) {
        if !children.isEmpty {
            let from = children[0]
            from.willMove(toParent: nil)
            self.addChild(to)
            to.view.alpha = 0.0
            self.transition(from: from, to: to, duration: self.transitionCoordinator?.transitionDuration ?? 0.4, options: [], animations: {
                to.view.alpha = 1.0
            }) { (finished) in
                from.removeFromParent()
                to.didMove(toParent: self)
            }
        } else {
            self.addChild(to)
            self.view.addSubview(to.view)
            to.view.alpha = 0.0
            UIView.animate(withDuration: 0.5) {
                to.view.alpha = 1.0
            } completion: { (_) in
                to.didMove(toParent: self)
            }
        }
    }
}

//@IBAction func unwindToRootViewController(_ segue: UIStoryboardSegue) {
//
//    let storyboard = UIStoryboard(name: "Main", bundle: Bundle(for: type(of: self)))
//
//    if let registerViewController = self.children.first(where: { $0 is RegisterViewController }), let mainViewController = storyboard.instantiateInitialViewController() {
//    self.transition(from: registerViewController, to: mainViewController)
//    }
//}
