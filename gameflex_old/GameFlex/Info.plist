<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BGTaskSchedulerPermittedIdentifiers</key>
	<array>
		<string>io.gameflex.getflexes</string>
	</array>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>$(PRODUCT_BUNDLE_PACKAGE_TYPE)</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.887909800618-jd43qgpot418568k6c5pa9qo1frir8om</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>io.gameflex.gameflex</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.social-networking</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppleMusicUsageDescription</key>
	<string>Allow access to your library so your media can be uploaded drectly into the GameFlex app.</string>
	<key>NSCameraUsageDescription</key>
	<string>Allow access so that you can take photos of your epic moments right from the GameFlex app.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Allow access to your microphone so you can share sound in your movies.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow access to your library so your photos can be uploaded drectly into the GameFlex app.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Allow access to your library so your photos can be uploaded drectly into the GameFlex app.</string>
	<key>UIAppFonts</key>
	<array>
		<string>PressStart2P-Regular.ttf</string>
		<string>IMFellDoublePica-Regular.ttf</string>
		<string>IndieFlower-Regular.ttf</string>
		<string>PermanentMarker-Regular.ttf</string>
		<string>Roboto-Medium.ttf</string>
		<string>JollyLodger-Regular.ttf</string>
		<string>LuckiestGuy-Regular.ttf</string>
		<string>ZillaSlabHighlight-Bold.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>Launch</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Dark</string>
</dict>
</plist>
