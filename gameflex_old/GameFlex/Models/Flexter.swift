//
//  Flexter.swift
//  GameFlex
//
//  Created by <PERSON> on 10/2/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//
// This is the generic data object for a person. The singleton User has a flexter node.

import Foundation
import SwiftyJSON

struct Flexter: Equatable, Comparable {
    
    static func < (lhs: <PERSON>lex<PERSON>, rhs: <PERSON>lex<PERSON>) -> Bool {
        return lhs.flexterName ?? "a" < rhs.flexterName ?? "b"
    }
    // 19 parameters
    
    var blocked: [Flexter]?
    var channelsOwned: [Channel]?
    var channelsParticipated: [Channel]?
    var channelsFollowed: [Channel]?
    var city: String?
    var country: String?
    var email: String?
    var flexCount: Int?
    var flexterName: String?
    var followerCount: Int?
    var follower: [Channel]?
    var following: [Channel]?
    var friends: [Channel]?
    var interests: [String]?
    var joinedDate: String?
    var likesCount: Int?
    var name: String?
    var rank: String?
    var rankColor: UIColor?
    var recentHashtags: [String]?
    var reflexCount: Int?
    var savedFlexes: [String]?
    var userId: String?
    var userDescription: String?
    var notificationSubscribed: [Channel]?
    var profileImage: String?
    
    static func ==(lhs: Flexter, rhs: Flexter) -> Bool {
        if lhs.userId != nil, rhs.userId != nil, lhs.email != nil, rhs.email != nil {
            return lhs.userId == rhs.userId && lhs.email == rhs.email
        }
        return false
    }
    
    static func parseTheFlexter(json: JSON) -> Flexter {
        var user = Flexter()
        user.userId = json["userId"].string ?? ""
        user.flexCount = json["flexCount"].intValue
        user.reflexCount = json["reflexCount"].intValue
        user.city = json["city"].string ?? ""
        user.flexCount = json["flexCount"].intValue
        user.followerCount = json["followerCount"].intValue
        user.flexterName = json["flexterName"].string ?? ""
        user.joinedDate = json["joinedDate"].string ?? ""
        user.likesCount = json["likesCount"].intValue
        user.profileImage = json["profileImage"].string ?? ""
        if user.profileImage == "" {
            user.profileImage = json["flexterImage"].string ?? ""
        }
        user.country = json["country"].string ?? ""
        user.name = json["name"].string ?? ""
        user.rank = json["rank"].string ?? "Flexter"
        user.rankColor = UIColor(hex: json["rankColor"].string ?? "#8FAABD")
        user.userDescription = json["userDescription"].string ?? ""
        // recent hashtags
        var hash: [String] = []
        for str in (json["recentHashtags"].array ?? []) {
            hash.append(str.string!)
        }
        user.recentHashtags = hash
        
        // friends
        var friends: [Channel] = []
        for fr in (json["friends"].array ?? []) {
            let fr = Channel.parseTheSubChannel(fr)
            friends.append(fr)
        }
        friends = friends.sorted()
        user.friends = friends
        
        // interests
        var ints: [String] = []
        for str in (json["interests"].array ?? []) {
            ints.append(str.string!)
        }
        ints = ints.sorted()
        user.interests = ints
        
        // following
        var fols: [Channel] = []
        for str in (json["following"].array ?? []) {
            let ch = Channel.parseTheSubChannel(str)
            fols.append(ch)
        }
        fols = fols.sorted()
        user.following = fols
        
        // channelsParticipated
        var channels: [Channel] = []
        for jso in json["channels"]["channelsParticipated"].array ?? [] {
            if jso.dictionary != nil {
                let ch = Channel.parseTheSubChannel(jso)
                channels.append(ch)
            }
        }
        channels = channels.sorted()
        user.channelsParticipated = channels
        
        // channelsFollowed
        var chans: [Channel] = []
        for jso in json["channels"]["channelsFollowed"].array ?? [] {
            if jso.dictionary != nil {
                let ch = Channel.parseTheSubChannel(jso)
                chans.append(ch)
            }
        }
        chans = chans.sorted()
        user.channelsFollowed = chans

        // blocked
        var blocked: [Flexter] = []
        for use in (json["blocked"].array ?? []) {
            if use.dictionary != nil {
                var blockedUser = Flexter()
                blockedUser.flexterName = use["flexterName"].string
                blockedUser.userId = use["userId"].string
                blocked.append(blockedUser)
            }
        }
        blocked = blocked.sorted()
        user.blocked = blocked
        
        // channelsOwned
        var owned: [Channel] = []
        for channel in json["channels"]["channelsOwned"].array ?? [] {
            if channel.dictionary != nil {
                let ch = Channel.parseTheSubChannel(channel)
                owned.append(ch)
            }
        }
        owned = owned.sorted()
        user.channelsOwned = owned
        
        // notification subscriptions
        var notifications: [Channel] = []
        for ch in json["notificationSubscribed"].array ?? [] {
            if ch.dictionary != nil {
                let cha = Channel.parseTheSubChannel(ch)
                notifications.append(cha)
            }
        }
        user.notificationSubscribed = notifications
        
        return user
    }
}
