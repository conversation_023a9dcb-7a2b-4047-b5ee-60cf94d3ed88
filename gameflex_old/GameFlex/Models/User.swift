//
//  User.swift
//  GameFlex
//
//  Created by <PERSON> on 7/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Foundation
import SwiftyJSON

enum GameFlexAuthenticationService: String {
    case none, email, google, apple
}

struct User {
    
    static let shared = User()
    
    static var shouldDismissOnPop = false
    static var shouldGoToLoginOnPop = false
    static var shouldShowSwords = true

    static var isLoggedIn: Bool { return refreshToken != nil }
    
    static var authenticationService: GameFlexAuthenticationService {
        get { return GFDefaults.authenticationService }
        set { GFDefaults.authenticationService = newValue }
    }
    
    static var fcmToken: String? {
        get { return GFDefaults.fcmToken }
        set {
            if let nv = newValue, nv != fcmToken {
                if User.isLoggedIn {
                    GFNetworkServices.patchUserProfile(content: [.fcmToken: "\(nv)"]) { (success, error) in
                        if let error = error {
                            print("\("error with updating fcmToken - \(error)")")
                        }
                        if success {
                            GFDefaults.fcmToken = nv
                        }
                    }
                }
            }
        }
    }
    
    static var flexter: Flexter = Flexter()
    static var isEmailVerified: Bool? {
        get { return GFDefaults.isEmailVerified }
        set {
            if newValue != nil {
                GFDefaults.isEmailVerified = newValue!
            }
        }
    }
    static var likedFlexes: [String: [String]] {
        get { return GFDefaults.likedFlexes }
        set { GFDefaults.likedFlexes = newValue }
    }
    static var parentsWithReflexesSeen: [String: [String]] {
        get { return GFDefaults.parentsWithReflexesSeen }
        set { GFDefaults.parentsWithReflexesSeen = newValue }
    }
    
    // this is returned in the loginAtGameFlex/registerAtGameFlex API
    // consumed in APIs once logged in
    static var refreshToken: String? {
        get { return GFDefaults.refreshToken }
        set {
            if newValue != nil {
                GFDefaults.refreshToken = newValue!
            }
        }
    }
    
    static var userId: String? {
        get { return GFDefaults.userId }
        set { GFDefaults.userId = newValue! }
    }
    
    // Users are only asked for month and year of birth, assumes the first of the month
    static var dateOfBirth: Date? {
        get { return GFDefaults.dateOfBirth }
        set {
            if newValue != nil {
                GFDefaults.dateOfBirth = newValue!
            }
        }
    }
    
    static var lastVSeen: Int {
        get { return GFDefaults.lastVSeen }
        set { GFDefaults.lastVSeen = newValue }
    }
    
    static var searchOnNumberOfLetters: Int {
        get { return GFDefaults.searchOnNumberOfLetters > 1 ? GFDefaults.searchOnNumberOfLetters : 1 }
        set { GFDefaults.searchOnNumberOfLetters = newValue }
    }
    
    // MARK: - from device
    static var app: String? = Bundle.main.infoDictionary?["CFBundleVersion"] as? String
    static var ios: String? = UIDevice.current.systemVersion
    static var device: String? = UIDevice.modelName
    
    static func logout() { // 20 parameters
        authenticationService = .none
        refreshToken = nil
        self.updateTheUser(Flexter())
    }
    
    // assigns user properties to the User singleton's flexter node
    static func updateTheUser(_ user: Flexter) { // 19 parameters
        flexter.blocked = user.blocked ?? []
        flexter.channelsOwned = user.channelsOwned ?? []
        flexter.channelsParticipated = user.channelsParticipated ?? []
        flexter.channelsFollowed = user.channelsFollowed ?? []
        flexter.city = user.city ?? ""
        flexter.country = user.country ?? "USA"
        flexter.email = user.email ?? ""
        flexter.flexCount = user.flexCount ?? 0
        flexter.flexterName = user.flexterName ?? ""
        flexter.followerCount = user.followerCount ?? 0
        flexter.following = user.following ?? []
        flexter.friends = user.friends ?? []
        flexter.interests = user.interests ?? []
        flexter.joinedDate = user.joinedDate ?? ""
        flexter.name = user.name ?? ""
        flexter.rank = user.rank ?? ""
        flexter.rankColor = user.rankColor ?? UIColor.gfTextLight_8FAABD
        flexter.reflexCount = user.reflexCount ?? 0
        flexter.savedFlexes = user.savedFlexes ?? []
        flexter.userId = user.userId ?? ""
        flexter.userDescription = user.userDescription ?? ""
        flexter.notificationSubscribed = user.notificationSubscribed ?? []
        flexter.profileImage = user.profileImage ?? ""
        
        if let userID = flexter.userId, userID != "" {
            userId = userID
        }
    }
    
}
