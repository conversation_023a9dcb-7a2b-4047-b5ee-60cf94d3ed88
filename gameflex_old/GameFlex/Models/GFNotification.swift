//
//  GFNotification.swift
//  GameFlex
//
//  Created by <PERSON> on 11/8/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import SwiftyJSON

enum GFNotificationType: String {
    case like, follow, invite, invite_accepted, comment, reflex, deleteChannel
}

struct GFNotification {
    
    var notificationId: String?
    var notificationType: GFNotificationType?
    var flexId: String?
    var flexMedia: [String]?
    var source: Flexter?
    var timeStamp: String?
    var date: Date?
    var channel: Channel?
    /*
                "id": "8c76de4a-f3e6-456d-98b5-10b1ed88d92a",
      "reactionType": "like",
            "flexId": "Flexter1603119905814",
      “flexMediaUrl”: [ url, url, url…],
      "reactionUser": {
                "userId": "787bd6e0-7e68-424e-9bd6-363a3ed45842",
                "flexterName": "Flexter1603119905814",
                "profileImage": "url",
                }
    */

    static func parse(_ jsons: [JSON]) -> [GFNotification] {
        var sum: [GFNotification] = []
        for json in jsons {
            var note = GFNotification()
            note.notificationId = json["id"].stringValue
            note.notificationType = GFNotificationType(rawValue: json["reactionType"].stringValue)
            note.flexId = json["flexId"].stringValue
            note.timeStamp = json["ts"].string
            if let str = json["ts"].string {
                note.date = Utilities.cleanUpServerDate(str)
            }
            var arr: [String] = []
            for ni in json["flexMediaUrl"].arrayValue {
                arr.append(ni.stringValue)
            }
            note.flexMedia = arr
            note.channel = Channel.parseTheChannel(json["channel"])
            note.channel?.type = .channel
            
            var flexter = Flexter()
            flexter.flexterName = json["reactionUser"]["flexterName"].stringValue
            flexter.userId = json["reactionUser"]["userId"].stringValue
            flexter.profileImage = json["reactionUser"]["profileImage"].stringValue
            note.source = flexter
            sum.append(note)
        }
        return sum
    }
}

