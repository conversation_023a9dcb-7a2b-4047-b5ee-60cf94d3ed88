//
//  SideBarObject.swift
//  GameFlex
//
//  Created by <PERSON> on 10/23/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation

struct SideBarObject {
    
    var buttonType: ChannelAction = .like
    var buttonCount: Int = 0
    var isHidden: Bool = false
    var isHighlighted: Bool = false
    var commentData: [CommentObject]?
    var captionData: CaptionObject?
    var restartTimers: Bool = false  // for dismissing comments and restarting the reflex timer
    var row: Int = 0
}

struct CommentObject {
    var commentSourceId: String?
    var commentFlexterName: String?
    var commentFlexter: Flexter?
    var comment: String?
    var stats: String?
}

struct CaptionObject {
    var captionSourceId: String?
    var captionFlexterName: String?
    var captionFlexter: Flexter?
    var profileImage: String?
    var caption: String?
    var stats: String?
    var numberOfComments: Int = 0
    var numberOfReflexes: Int = 0
}
