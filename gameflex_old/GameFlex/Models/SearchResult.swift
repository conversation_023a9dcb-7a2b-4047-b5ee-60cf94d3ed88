//
//  SearchResult.swift
//  GameFlex
//
//  Created by <PERSON> on 10/8/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import SwiftyJSON

enum SearchResultType: String {
    case user, channel, system, unknown
}

struct SearchResult: Equatable, Hashable  {
    
    var city: String?
    var country: String?
    var name: String?
    var searchName: String?
    var profileImage: String?
    var searchId: String?
    var type: SearchResultType?
    
    static func parse(_ json: JSON) -> SearchResult {
        var sr = SearchResult()
        sr.searchId = json["id"].string
        sr.type = SearchResultType(rawValue: json["type"].string ?? "unknown")
        sr.name = json["name"].string
        sr.searchName = sr.name?.lowercased()
        sr.profileImage = json["profileimage"].string
        sr.country = json["country"].string
        sr.city = json["city"].string
        
        return sr
    }
    
    static func ==(lhs: SearchResult, rhs: SearchResult) -> <PERSON><PERSON> {
        return lhs.searchId == rhs.searchId
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(searchId)
    }
}
