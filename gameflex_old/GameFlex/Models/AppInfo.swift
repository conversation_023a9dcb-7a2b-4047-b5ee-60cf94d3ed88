//
//  AppInfo.swift
//  GameFlex
//
//  Created by <PERSON> on 9/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation

struct AppInfo {
    
    // these constants are meant to be server controlled
    static let maxFileSizekb: Int = 768
    static let childAge: Double = 13
    static let teenAge: Double = 16
    static let stickerBundles: [String] = []
    static let minVersion: Int = 13
    static let recommendedVersion: Int = 13
    static let mainChannelTime: Double = 5.0
    static let reflexTime: Double = 5.0
    static let channelsAreActive = false
    static let safety: Double = 0.75
    
    static let overrideModerationLabels: [String] = ["Graphic Violence or Gore", "Weapons", "Violence", "Physical Violence", "Weapon Violence", "Emaciated Bodies", "Corpses", "Hanging", "Suggestive", "Revealing Clothes", "Alcoholic Beverages", "Alcohol"]
}
