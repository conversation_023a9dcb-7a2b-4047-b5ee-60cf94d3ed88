//
//  Flex.swift
//  GameFlex
//
//  Created by <PERSON> on 7/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import SwiftyJSON

struct Flex {
    
    // from server
    var caption: String?
    var channel: Channel?
    var comments: [String]?
    var createAt: String?
    var createAtDate: Date?
    var flexId: String?
    var flexterName: String?
    var hasReflex: Bool?
    var isAdFlex: Bool?
    var isReflex: Bool?
    var mediaUrl: [String]?
    var numberOfComments: Int?
    var numberOfReflexes: Int?
    var owner: Flexter?
    var parentFlexId: String?
    var reactions: [Reaction]?
    var reactionsUsers: [String]?
    var safetyIssues: [String]?
    var safetyScore: Double = 0.0
    var viewerCount: Int?
    
    var meta: Meta?
    
    // from CameraViewController
    var hashtags: [String]?
    var timestamp: Date?
    var stickies: [StickerView]?
    var flexNumber: Int?
    var textStickerUsed: Bool?
    // should capture filter, edit selections
    
    static func parse(_ arr: [<PERSON><PERSON><PERSON>]) -> [Flex] {
        var array: [Flex] = []
        for dat in arr {
            if let flex = parseFlex(dat: dat) {
                if flex.flexId != nil,
                // Flex Safety Policy is applied
                FlexManager.passesSafety(flex) {
                    array.append(flex)
                }
            }
        }
        return array
    }
    
    static func parseFlex(dat: JSON) -> Flex? {
        let df = DateFormatter()
        df.dateFormat = "yyyy-MM-dd HH:mm:ss" //2020-09-16T16:34:57.843Z
        var flex = Flex()
        flex.owner = Flexter.parseTheFlexter(json: dat["owner"])
        flex.channel = Channel.parseTheChannel(dat["channel"])
        flex.flexId = dat["flexId"].string
        flex.caption = dat["caption"].string
        flex.safetyScore = dat["safety"].double ?? 0.0
        flex.hasReflex = dat["hasReflex"].boolValue
        flex.isAdFlex = dat["isAdFlex"].boolValue
        flex.isReflex = dat["isReflex"].boolValue
        
        var reactions: [Reaction] = []
        if !(dat["reactions"].array?.isEmpty ?? true) {
            for react in (dat["reactions"].array)! {
                var reaction = Reaction()
                reaction.type = ReactionType(rawValue: react.dictionary?.keys.first ?? "none")
                reaction.number = Int(react.dictionary?.values.first?["number"].string ?? "0") ?? 0
                reactions.append(reaction)
            }
        }
        flex.reactions = reactions
        var rUsers: [String] = []
        if !(dat["reactionsUsers"].array?.isEmpty ?? true) {
            for react in (dat["reactionsUsers"].array)! {
                if react.string != nil {
                    rUsers.append(react.string ?? "")
                }
            }
        }
        flex.reactionsUsers = rUsers
        flex.createAt = dat["createAt"].string
        if let dates = flex.createAt, dates != "" {
            flex.createAtDate = Utilities.cleanUpServerDate(dates)
        } else {
            flex.createAtDate = Date()
        }
        var media: [String] = []
        if !(dat["mediaUrl"].array?.isEmpty ?? true) {
            for med in (dat["mediaUrl"].array)! {
                if med.string != nil {
                    media.append(med.string ?? "")
                }
            }
        }
        flex.mediaUrl = media
        var comments: [String] = []
        if !(dat["comments"].array?.isEmpty ?? true) {
            for comm in (dat["comments"].array)! {
                if comm.string != nil {
                    comments.append(comm.string ?? "")
                }
            }
        }
        flex.comments = comments
        flex.numberOfComments = dat["numberOfComments"].intValue
        flex.viewerCount = dat["viewerCount"].intValue
        flex.flexterName = dat["flexterName"].string
        flex.numberOfReflexes = dat["numberOfReflexes"].intValue
        
        if FlexManager.passesSafety(flex) {
            return flex
        }
        return nil
    }
}

struct Meta {
    var stickers: [String]?
    var textStickerUsed: Bool?
    var drawingsUsed: Bool?
    var userFlexNumber: Int?
    var device: String?
    var appVersion: Int?
    var osVersion: String?
    var userYearOfBirth: String?
}

extension Flex: Equatable {
    
    static func == (rhs: Flex, lhs: Flex) -> Bool {
        if rhs.flexId == lhs.flexId  {
            return true
        }
        return false
    }
}

extension Flex: Hashable {
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(flexId)
        hasher.combine(numberOfComments)
    }
    
}
