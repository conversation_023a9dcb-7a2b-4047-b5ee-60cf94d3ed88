//
//  HashTags.swift
//  GameFlex
//
//  Created by <PERSON> on 10/18/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation

struct HashTags {
    
    static let shared = HashTags()
    
    static var hashTagsArray: [String] = User.flexter.recentHashtags ?? ["#Flexie4Pres", "#Winner", "#GameOver!", "#Flex4Ever", "#FabulousFlex", "#MthrOfAllFlexes", "#BestEver", "#Flexie", "#I\u{1F496}Flexi", "#Flexie4Pres"]
        
    static var mostFrequentHashTags: [(key: String, value: Int)] {
        
        let mappedItems = hashTagsArray.map { ($0, 1) }
        let counts: [String: Int] = Dictionary(mappedItems, uniquingKeysWith: +)
        let finals = counts.sorted{ $0.value > $1.value }

        return finals
    }
    
}
