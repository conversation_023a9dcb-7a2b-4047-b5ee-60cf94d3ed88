//
//  RandomNames.swift
//  GameFlex
//
//  Created by <PERSON> on 10/16/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import SwiftyJSON

struct RandomNames {
    
    static let consonants = ["B", "C", "D", "F", "G", "H", "J", "K", "L", "M", "N", "P", "Q", "R", "S", "T", "V", "W", "X", "Z"]
    static let vowels = ["A", "E", "I", "O", "U", "Y"]
    
    static func getRandomName(letters: Int = 3, length: Int = 4) -> String {
        var str = "\(consonants[Int.random(in: 0..<consonants.count)].uppercased())"
        for index in 1..<letters {
            if index.isMultiple(of: 2) {
                str = "\(str)\(consonants[Int.random(in: 0..<consonants.count)].lowercased())"
            } else {
                str = "\(str)\(vowels[Int.random(in: 0..<vowels.count)].lowercased())"
            }
        }
        let noDigits = length - letters
        if noDigits > 0 {
            for _ in 0..<noDigits {
                str = "\(str)\(Int.random(in: 0..<9))"
            }
        }
        return str
    }
    


}
