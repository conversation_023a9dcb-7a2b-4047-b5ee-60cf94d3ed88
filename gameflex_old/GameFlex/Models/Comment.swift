//
//  Comment.swift
//  GameFlex
//
//  Created by <PERSON> on 10/25/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import SwiftyJSON

struct Comment: Equatable {
    
    var commentorId: String?
    var commentorFlextorName: String?
    var comment: String?
    var timeStamp: String?
    var date: Date?
    var flexId: String?
    
    static func ==(lhs: Comment, rhs: Comment) -> Bool {
        return lhs.commentorId == rhs.commentorId && lhs.comment == rhs.comment && lhs.timeStamp == rhs.timeStamp
    }
    
    static func parse(_ comments: [JSON]) -> [Comment] {
        var array: [Comment] = []
        for comment in comments {
            var comm = Comment()
            comm.timeStamp = comment["ts"].string
            comm.flexId = comment["flexid"].string
            comm.commentorId = comment["userid"].string
            comm.comment = comment["comment"].string
            comm.commentorFlextorName = comment["flexterName"].string
            if let str = comment["ts"].string {
                comm.date = Utilities.cleanUpServerDate(str)
            }
            array.append(comm)
        }
        return array
    }
}

extension Comment: Hashable {
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(flexId)
        hasher.combine(timeStamp)
        hasher.combine(comment)
    }
    
}
