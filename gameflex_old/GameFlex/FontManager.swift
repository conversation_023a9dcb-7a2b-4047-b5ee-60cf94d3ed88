//
//  FontManager.swift
//  GameFlex
//
//  Created by <PERSON> on 7/20/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FontManager {
    
    static var importedNames = ["Roboto", "IM Fell Double Pica", "Indie Flower", "Permanent Marker", "Press Start 2P", "Jolly Lodger", "Luckiest Guy", "Zilla Slab Highlight"]
    static var importedFamilyStyles = ["IM Fell Double Pica": ["IMFellDoublePica-Regular"],
                                       "Indie Flower": ["IndieFlower-Regular"],
                                       "Permanent Marker": ["PermanentMarker-Regular"],
                                       "Press Start 2P": ["PressStart2P-Regular"],
                                       "Roboto": ["Roboto-Medium"],
                                       "Jolly Lodger": ["JollyLodger-Regular"],
                                       "Luckiest Guy": ["LuckiestGuy-Regular"],
                                       "Zilla Slab Highlight": ["ZillaSlabHighlight-Bold"]]
    
    // UIFont.familyNames
    static var familyNames: [String] {
        let badNames = ["Apple Symbols", ".SFUI-Semibold", "Apple Color Emoji", ".SFUI-Regular"]
        var arr: [String] = [] //UIFont.familyNames
        arr.removeAll{ badNames.contains($0)}
        arr.append(contentsOf: importedNames)
        arr = arr.sorted()
        return arr
    }
    
    static var familyStyles: [String: [String]] {
        var arr: [String: [String]] = [:]
        for name in familyNames {
            let innerArray = UIFont.fontNames(forFamilyName: name)
            arr[name] = innerArray
        }
        return arr
    }
    
    static var flatFontsArray: [String] {
        var arr: [String] = []
        for name in familyNames {
            if familyStyles[name] != nil {
                arr.append(contentsOf: familyStyles[name]!)
            }
        }
        return arr
    }
    
}
