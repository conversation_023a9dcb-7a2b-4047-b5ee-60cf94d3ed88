//
//  Utilities.swift
//  GameFlex
//
//  Created by <PERSON> on 8/23/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import CryptoKit
import LocalAuthentication
import AVKit

struct Utilities {
    
    // Adapted from https://auth0.com/docs/api-auth/tutorials/nonce#generate-a-cryptographically-random-nonce
    static func randomNonceString(length: Int = 32) -> String {
      precondition(length > 0)
      let charset: Array<Character> =
          Array("0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._")
      var result = ""
      var remainingLength = length

      while remainingLength > 0 {
        let randoms: [UInt8] = (0 ..< 16).map { _ in
          var random: UInt8 = 0
          let errorCode = SecRandomCopyBytes(kSecRandomDefault, 1, &random)
          if errorCode != errSecSuccess {
            fatalError("Unable to generate nonce. SecRandomCopyBytes failed with OSStatus \(errorCode)")
          }
          return random
        }

        randoms.forEach { random in
          if remainingLength == 0 {
            return
          }

          if random < charset.count {
            result.append(charset[Int(random)])
            remainingLength -= 1
          }
        }
      }

      return result
    }

    @available(iOS 13, *)
    static func sha256(_ input: String) -> String {
      let inputData = Data(input.utf8)
      let hashedData = SHA256.hash(data: inputData)
      let hashString = hashedData.compactMap {
        return String(format: "%02x", $0)
      }.joined()

      return hashString
    }

    static func isValidEmail(_ emailText: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: emailText)
    }
    
    static func isValidPassword(_ passwordText: String) -> Bool {
        let pwRegEx = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d]{8,}$"
        let pwPred = NSPredicate(format: "SELF MATCHES %@", pwRegEx)
        return pwPred.evaluate(with: passwordText)
    }

    static func cleanUpServerDate(_ stringDate: String) -> Date {
        let df = DateFormatter()
        df.dateFormat = "yyyy-MM-dd HH:mm:ss" //2020-09-16T16:34:57.843Z
        df.timeZone = TimeZone(abbreviation: "UTC")
        let dated = stringDate.replacingOccurrences(of: "T", with: " ")
        return df.date(from: String(dated.dropLast(5))) ?? Date()
    }
    
    // used in ProfileBottomTableViewCell's collectionView cell sizing dataSource func and CommentsViewController
    static var miniFlexDisplaySize: CGSize {
        let width = (UIApplication.shared.keyWindow?.frame.size.width ?? 300) / 3 - 8
        let height = width * 1.32
        return CGSize(width: width, height: height)
    }

    static func relativeTime(date: Date?) -> String {
        var dateText = ""
        if let date = date {
            if (Date().timeIntervalSinceReferenceDate - date.timeIntervalSinceReferenceDate)/(3600 * 24 * 90) < 1 {
                
                dateText = date.timeAgoSinceDate(numericDates: false)
                if dateText != "date.justNow".localized || dateText.count < 5 {
                    dateText = dateText.replacingOccurrences(of: " ", with: "")
                }
            } else {
                dateText = date.toLocalTime().toFormat("MMM d")
            }
        } else {
            dateText = "N/A"
        }
        return dateText
    }
    
    static func showSpinner() {
        GFSpinnerView.showIn(view: UIApplication.shared.keyWindow!)
    }
    
    static func hideSpinner() {
        GFSpinnerView.hideIn(view: UIApplication.shared.keyWindow!)
    }
    
    static func isFaceIDCapable() -> Bool {
        let authContext = LAContext()
        let _ = authContext.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil)
        switch(authContext.biometryType) {
        case .none:
            return false
        case .touchID:
            return false
        case .faceID:
            return true
        @unknown default:
            return false
        }
    }
    
    static func configure(followButton: UIButton, following: Bool = false) {
        followButton.layer.cornerRadius = followButton.frame.size.height/2
        followButton.applyPrimaryGFButtonStandards()
        if following {
            followButton.setTitle("comment.followButton.following".localized, for: .normal)
            followButton.backgroundColor = .clear
            followButton.setTitleColor(.gfGreen, for: .normal)
            followButton.layer.borderWidth = 1
            followButton.layer.borderColor = UIColor.gfGreen.cgColor
        } else {
            followButton.setTitle("comment.followButton.follow".localized, for: .normal)
            followButton.backgroundColor = .gfGreen
            followButton.setTitleColor(.black, for: .normal)
            followButton.layer.borderColor = UIColor.gfGreen.cgColor
        }
    }
    
    static func configureForInvite(followButton: UIButton, invite: Bool = true) {
        followButton.layer.cornerRadius = followButton.frame.size.height/2
        followButton.applyPrimaryGFButtonStandards()
        if !invite {
            followButton.setTitle("comment.followButton.accepted".localized, for: .normal)
            followButton.backgroundColor = .clear
            followButton.setTitleColor(.gfGreen, for: .normal)
            followButton.layer.borderWidth = 1
            followButton.layer.borderColor = UIColor.gfGreen.cgColor
        } else {
            followButton.setTitle("comment.followButton.invite".localized, for: .normal)
            followButton.backgroundColor = .gfGreen
            followButton.setTitleColor(.black, for: .normal)
            followButton.layer.borderColor = UIColor.gfGreen.cgColor
        }
    }
    
    
    static func getSettings(camera: AVCaptureDevice, flashMode: FlashMode) -> AVCapturePhotoSettings {
        let settings = AVCapturePhotoSettings()

        if camera.hasFlash {
            switch flashMode {
               case .auto: settings.flashMode = .auto
               case .on: settings.flashMode = .on
               default: settings.flashMode = .off
            }
        }
        return settings
    }

}
