//
//  AlertViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class AlertViewController: GFViewController {
        
    @IBOutlet weak var tableView: UITableView!
    
    var alertsDictionary: [String: [GFNotification]] = [:]
    var alertsArray: [GFNotification] = []
    var refreshControl: UIRefreshControl!

    static func storyboardInstance() -> AlertViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: AlertViewController.self)) as! AlertViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView()
        tableView.separatorStyle = .none
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "cell")
        tableView.register(UINib(nibName: AlertTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: AlertTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: AlertButtonTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: AlertButtonTableViewCell.cellIdentifier)

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Alerts"
        alertsArray = []
        getNotifications()
        if refreshControl == nil {
            refreshControl = UIRefreshControl()
            refreshControl.tintColor = .white
            tableView.addSubview(refreshControl!)
            refreshControl?.addTarget(self, action: #selector(doRefreshTable), for: .valueChanged)
        }
    }
    
    @objc func doRefreshTable() {
        DispatchQueue.main.async {
            guard self.view != nil else {
                self.perform(#selector(self.doRefreshTable), with: nil, afterDelay: 0.5)
                return
            }
            self.refreshControl?.endRefreshing()
            if !self.tableView.subviews.contains(self.refreshControl) {
                self.tableView.addSubview(self.refreshControl)
                self.refreshControl?.addTarget(self, action: #selector(self.doRefreshTable), for: .valueChanged)
            }
            self.getNotifications()
        }
        return
    }

    func getNotifications() {
        if User.isLoggedIn {
            
            tabBarController?.tabBar.isHidden = false
            GFNetworkServices.getNotifications { (success, results, error) in
                DispatchQueue.main.async {
                    guard error == nil else {
                        let alert = UIAlertController(title: "Error?", message: "Something unexpected happened.\n\n\(error?.localizedDescription ?? "38")", preferredStyle: .alert)
                        let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                        alert.addAction(ok)
                        self.present(alert, animated: true)
                        return
                    }
                    self.alertsDictionary = ["Today": results]
                    var arr: [GFNotification] = []
                    let df = DateFormatter()
                    df.dateFormat = "MM-dd-yyyy"
                    let dated = df.date(from: "12-14-2020")
                    for res in results {
                        if let day1 = res.date?.timeIntervalSince1970, let day2 = dated?.timeIntervalSince1970 {
                            if  day1 > day2 {
                                arr.append(res)
                            }
                        }
                    }
                    self.alertsDictionary = ["Today": arr]
                    self.alertsArray = arr
                    self.tableView.reloadData()
                }
            }
        }

    }
}

extension AlertViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1//alertsDictionary.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return alertsArray.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard alertsArray.count > indexPath.row else { return UITableViewCell() }
        let alert = alertsArray[indexPath.row]
        let buttonTypes: [GFNotificationType] = [.invite, .invite_accepted, .follow]
        if let type = alert.notificationType, buttonTypes.contains(type) {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: AlertButtonTableViewCell.cellIdentifier, for: indexPath) as? AlertButtonTableViewCell else { return AlertButtonTableViewCell() }
            cell.configureCell(alert)
            if type == .follow {
                let following = (User.flexter.following?.filter({$0.channelId == alert.source?.userId}).count ?? 0) > 0
                cell.configureCell(following: following)
            } else if alert.notificationType == .invite || alert.notificationType == .invite_accepted {
                cell.configureCellForInvite()
            }
            cell.followButton.isHidden = false
            cell.delegate = self
            return cell
        }

        guard let cell = tableView.dequeueReusableCell(withIdentifier: AlertTableViewCell.cellIdentifier, for: indexPath) as? AlertTableViewCell else { return AlertTableViewCell() }
        cell.delegate = self
        cell.configureCell(alert)
        cell.flexImageView.isHidden = false

        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 72
    }
    
}

extension AlertViewController: HomeDelegate {
    
    func didTapForProfileAction(flexterId: String) {
        var flexter = Flexter()
        flexter.userId = flexterId
        
        let pvc = ProfileViewController.storyboardInstance()
        pvc.flexter = flexter
        navigationController?.pushViewController(pvc, animated: true)

    }
    
    func didTapToFollow(_ sender: UIButton, _ tableViewCell: UITableViewCell?) {
        if let cell = tableViewCell as? AlertButtonTableViewCell, let row = tableView.indexPath(for: cell)?.row, let sourceId = alertsArray[row].source?.userId {
            Utilities.showSpinner()
            if sender.backgroundColor == .gfGreen {
                GFNetworkServices.followThis([sourceId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            cell.configureCell(following: true)
                        }
                    }
                }
            } else {
                GFNetworkServices.unFollowThis([sourceId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            cell.configureCell(following: false)
                        }
                    }
                }
            }
        }

    }
    
    func didTapToAcceptInvitation(_ notificationId: String) {
        Utilities.showSpinner()
        GFNetworkServices.acceptThis(notificationId) { (success, error) in
            DispatchQueue.main.async {
                Utilities.hideSpinner()
            }
        }
    }
}
