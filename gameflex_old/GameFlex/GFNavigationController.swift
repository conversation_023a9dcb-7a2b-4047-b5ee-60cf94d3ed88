//
//  GFNavigationController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class GFNavigationController: UINavigationController {
    
    override func popViewController(animated: Bo<PERSON>) -> UIViewController? {
        if let cvc = viewControllers.filter({ $0.isKind(of: CameraViewController.self)}).first as? CameraViewController {
            if CameraViewModel.state == .text {
                cvc.view.endEditing(true)
                cvc.verticalSlider.isHidden = true
                cvc.didTapForCameraAction(.flare)
                let tap = UITapGestureRecognizer(target: cvc.view, action: #selector(cvc.detectBackgroundTap))
                cvc.flexBackgroundView.addGestureRecognizer(tap)

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
                    cvc.moveTheTray(up: false, fromCreateSticker: CameraViewModel.stickersInProgress.count > 0)
                }
                cvc.imageEnhancementControlsView.didTapButton(sender: cvc.imageEnhancementControlsView.flareButton)
                return cvc
            }
            Utilities.hideSpinner()
            
            viewControllers.removeAll(where: {$0.isKind(of: FinalizeViewController.self)})
            tabBarController?.selectedIndex = 0
            
            // TODO: be sure to present the following section or targetted channel
            return super.popViewController(animated: animated)
        } else if let invite = viewControllers.filter({ $0.isKind(of: InviteViewController.self)}).first as? InviteViewController {
            invite.navigationController?.popToRootViewController(animated: true)
        }
        return super.popViewController(animated: animated)
    }
}
