//
//  GFViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 7/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import StoreKit
import FirebaseAuth
import AVKit
import Photos

class GFViewController: UIViewController {
    
    override var title: String? {
        didSet{
            tabBarItem.title = ""
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .black
        // Make the navigation bar background clear
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        navigationController?.navigationBar.tintColor = .white
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = ""
    }
    
    func setBackgroundGradient() {
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = self.view.bounds
        gradientLayer.colors = [UIColor.gfGreen.cgColor, UIColor.gfDarkGrayMask.cgColor]
        self.view.layer.insertSublayer(gradientLayer, at: 0)
    }
    
    // MARK: - NavBar handling
    
    func showCancelAsLeftNavBarButton(_ show: Bool = true) {
        if show {
            self.navigationController?.navigationItem.hidesBackButton = true
            let cancel = UIButton()
            cancel.setImage(#imageLiteral(resourceName: "cancel"), for: .normal)
            if #available(iOS 13.0, *) {
                cancel.setImage(#imageLiteral(resourceName: "cancel").withTintColor(.white, renderingMode: .alwaysTemplate), for: .normal)
            } else {
                // Fallback on earlier versions
                cancel.setImage(#imageLiteral(resourceName: "cancel").withRenderingMode(.alwaysTemplate), for: .normal)
                cancel.tintColor = .white
            }
            cancel.addTarget(self, action: #selector(dismissMe), for: .touchUpInside)
            self.navigationItem.leftBarButtonItem = UIBarButtonItem(customView: cancel)
        } else {
            self.navigationItem.leftBarButtonItems = []
            self.navigationController?.navigationItem.hidesBackButton = false
        }
    }
    
    func addCancelToNavBar() {
        let cancel = UIButton()
        cancel.setImage(#imageLiteral(resourceName: "cancel"), for: .normal)
        if #available(iOS 13.0, *) {
            cancel.setImage(#imageLiteral(resourceName: "cancel").withTintColor(.black, renderingMode: .alwaysTemplate), for: .normal)
        } else {
            // Fallback on earlier versions
            cancel.setImage(#imageLiteral(resourceName: "cancel").withRenderingMode(.alwaysTemplate), for: .normal)
            cancel.tintColor = .black
        }
        cancel.addTarget(self, action: #selector(dismissMe), for: .touchUpInside)
        self.navigationItem.rightBarButtonItem = UIBarButtonItem(customView: cancel)
    }
    
    func addNextToNavBar() {
        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        self.navigationItem.rightBarButtonItems  = [next]
    }
    
    func addFlexToNavBar(_ isEnabled: Bool = true) {
        if isEnabled {
            let next = UIBarButtonItem(title: "flex".localized, style: .done, target: self, action: #selector(didTapNext))
            next.tintColor = .gfGreen
            next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
            self.navigationItem.rightBarButtonItems  = [next]
        } else {
            let next = UIBarButtonItem(title: "flex".localized, style: .done, target: self, action: nil)
            next.tintColor = .gfDarkBackground100
            self.navigationItem.rightBarButtonItems  = [next]
        }
    }
    
    func addInviteToNavBar() {
        let invite = UIBarButtonItem(title: "invite".localized, style: .done, target: self, action: #selector(didTapNext))
        invite.tintColor = .gfGreen
        invite.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        self.navigationItem.rightBarButtonItem  = invite
    }
    
    func addReflexToNavBar(_ isEnabled: Bool = true) {
        if isEnabled {
            let next = UIBarButtonItem(title: "reflex".localized, style: .done, target: self, action: #selector(didTapNext))
            next.tintColor = .gfGreen
            next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
            self.navigationItem.rightBarButtonItems  = [next]
        } else {
            let next = UIBarButtonItem(title: "reflex".localized, style: .done, target: self, action: nil)
            next.tintColor = .gfDarkBackground100
            next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfDarkBackground100], for: .normal)
            self.navigationItem.rightBarButtonItems  = [next]
        }
    }
    
    func addEditToNavBar() {
        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        let editButton = UIButton()
        editButton.setImage(#imageLiteral(resourceName: "edit"), for: .normal)
        editButton.addTarget(self, action: #selector(tapForEdit), for: .touchUpInside)
        self.navigationItem.rightBarButtonItems  = [next, UIBarButtonItem(customView: editButton)]
    }
    
    func addActiveEditToNavBar() {
        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        let editButton = UIButton()
        editButton.setImage(#imageLiteral(resourceName: "editSelected"), for: .normal)
        editButton.addTarget(self, action: #selector(tapForEdit), for: .touchUpInside)
        self.navigationItem.rightBarButtonItems  = [next, UIBarButtonItem(customView: editButton)]
    }
    
    func addDrawButtonToNavBar() {
        var image = #imageLiteral(resourceName: "opacity100")
        switch CameraViewModel.opacitySetting {
        case 0.25: image = #imageLiteral(resourceName: "opacity25")
        case 0.50: image = #imageLiteral(resourceName: "opacity50")
        case 0.75: image = #imageLiteral(resourceName: "opacity75")
        default: break
        }
        let opButton = UIBarButtonItem(image: image, style: .plain, target: self, action: #selector(tapForOpacity))
        opButton.tintColor = .white

        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        self.navigationItem.rightBarButtonItems = [next, opButton]
        self.title = "Flex"
    }
    
    func addDrawButtonsToNavBar(_ isEraseEnabled: Bool = false) {
        var image = #imageLiteral(resourceName: "opacity100")
        switch CameraViewModel.opacitySetting {
        case 0.25: image = #imageLiteral(resourceName: "opacity25")
        case 0.50: image = #imageLiteral(resourceName: "opacity50")
        case 0.75: image = #imageLiteral(resourceName: "opacity75")
        default: break
        }
        let opButton = UIBarButtonItem(image: image, style: .plain, target: self, action: #selector(tapForOpacity))
        opButton.tintColor = .white

        let undoButton = UIBarButtonItem(image: UIImage(systemName: "arrow.uturn.backward.circle"), style: .plain, target: self, action: #selector(undoDraw))
        undoButton.tintColor = .white

        /*var eraseButton = UIBarButtonItem(image: UIImage(#imageLiteral(resourceName: "eraser")), style: .plain, target: self, action: #selector(tapForErase))
        if isEraseEnabled {
            eraseButton = UIBarButtonItem(image: UIImage(#imageLiteral(resourceName: "eraserSelected")), style: .plain, target: self, action: #selector(tapForErase))
        }
        eraseButton.tintColor = .white
         */
        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        self.navigationItem.rightBarButtonItems = [next, opButton, undoButton /*, eraseButton*/]
        self.title = "Flex"
    }
        
    func addFlipAndNextToNavBar() {
        let flipButton = UIBarButtonItem(image: #imageLiteral(resourceName: "flip"), style: .plain, target: self, action: #selector(flip))
        flipButton.tintColor = .white
        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        self.navigationItem.rightBarButtonItems = [next, flipButton]
        self.title = "Flex"
    }
    
    func addTextStickerButtonsToNavBar(_ nextNotDone: Bool = false) {
        let colorButton = UIButton()
        colorButton.setImage(UIImage( #imageLiteral(resourceName:"color")), for: .normal)
        colorButton.addTarget(self, action: #selector(tapForPalette), for: .touchUpInside)
        
        let fontButton = UIBarButtonItem(image: #imageLiteral(resourceName:"fontButton"), style: .plain, target: self, action: #selector(tapForFonts))
        let textAlign = UIBarButtonItem(image: #imageLiteral(resourceName: "textAlignment"), style: .plain, target: self, action: #selector(advanceTextAlignment))
        
        var next = UIBarButtonItem(title: "done".localized, style: .done, target: self, action: #selector(dismissKeyboard))
        if nextNotDone {
            next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        }
        next.tintColor = .gfGreen
        next.setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGreen], for: .normal)
        self.navigationItem.rightBarButtonItems = [next, fontButton, UIBarButtonItem(customView: colorButton), textAlign]
        self.title = ""
    }
    
    func disableTextStickerButtonsInNavBar() {
        let colorButton = UIButton()
        colorButton.setImage(UIImage( #imageLiteral(resourceName:"color")), for: .normal)
        colorButton.alpha = 0.4
        
        let fontButton = UIBarButtonItem(image: #imageLiteral(resourceName:"fontButton"), style: .plain, target: self, action: nil)
        fontButton.tintColor = UIColor(white: 1.0, alpha: 0.4)
        let textAlign = UIBarButtonItem(image: #imageLiteral(resourceName: "textAlignment"), style: .plain, target: self, action: nil)
        textAlign.tintColor = UIColor(white: 1.0, alpha: 0.4)
        
        let next = UIBarButtonItem(title: "next".localized, style: .done, target: self, action: #selector(didTapNext))
        next.tintColor = .gfGreen
        self.navigationItem.rightBarButtonItems = [next, fontButton, UIBarButtonItem(customView: colorButton), textAlign]
        self.title = ""
    }

    func addSettingsToNavBar() {
        let settings = UIBarButtonItem(image: #imageLiteral(resourceName: "elipsisSelected"), style: .done, target: self, action: #selector(didTapSettings))
        settings.tintColor = UIColor.gfGreen
        self.navigationItem.rightBarButtonItems  = [settings]
    }
    
    @objc func advanceTextAlignment() {
        /* left empty for local handling in CameraViewController */
    }
    
    @objc func didTapNext() {
        /* left empty for local handling */
    }
    
    @objc func didTapSettings() {
        /* left empty for local handling in AlertViewController */
    }
    
    @objc func dismissMe() {
        if let vc = self as? SignUpViewController {
            dismiss(animated: false) {
                // dismiss and move to
                DispatchQueue.main.asyncAfter(deadline: .now()) {
                    vc.tab?.selectedIndex = (self.tabBarController as? LandingPageViewController)?.flexIndex ?? 2
                }
            }
        } else if let vc = self as? CameraViewController {
            vc.didTapForCameraAction(.cancelText)
        } else {
            dismiss(animated: true, completion: nil)
        }
    }
    
    @objc func flip() {
        /* left empty for local handling in CameraViewController */
    }

    @objc func tapForColorSwitching() {
        /* left empty for local handling in CameraViewController. Was originally a context button, but used on tapForPallette on every other tap. */
    }
    
    @objc func tapForEdit() {
        /* left empty for local handling in CameraViewController*/
    }
    
    @objc func tapForErase() {
        /* left empty for local handling in CameraViewController */
    }
    
    @objc func tapForFonts() {
        /* left empty for local handling in CameraViewController */
    }
        
    @objc func tapForOpacity() {
        /* left empty for local handling in CameraViewController */
    }
    
    @objc func tapForPalette() {
        /* left empty for local handling in CameraViewController */
    }
    
    @objc func undoDraw() {
        /* left empty for local handling in CameraViewController */
    }
    
    // MARK: - 
    
    func getDocumentsDirectory() -> URL {
        let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
        return paths[0]
    }
    
    func updateFlexCount() {
        // If the count has not yet been stored, this will return 0
        var count = UserDefaults.standard.integer(forKey: GFDefaults.kFlexCompletedCountKey)
        count += 1
        GFDefaults.completedFlexCount = count
        UserDefaults.standard.set(count, forKey: GFDefaults.kFlexCompletedCountKey)

        print("Process completed \(count) time(s)")

        // Get the current bundle version for the app
        let infoDictionaryKey = kCFBundleVersionKey as String
        guard let currentVersion = Bundle.main.object(forInfoDictionaryKey: infoDictionaryKey) as? String
            else {
                fatalError("Expected to find a bundle version in the info dictionary")
        }

        let lastVersionPromptedForReview = GFDefaults.lastVersionPrompted

        // Has the process been completed several times and the user has not already been prompted for this version?
        if count >= 4 && currentVersion != lastVersionPromptedForReview {
            let twoSecondsFromNow = DispatchTime.now() + 2.0
            DispatchQueue.main.asyncAfter(deadline: twoSecondsFromNow) {
                SKStoreReviewController.requestReview()
                GFDefaults.lastVersionPrompted = currentVersion
            }
        }

    }
}

// MARK: - funcs for handling email and password users authentication
extension GFViewController {
    
    // for email password users
    func sendVerificationEmail() {
        Auth.auth().currentUser?.sendEmailVerification { (error) in
            guard error == nil else {
                return
            }
            DispatchQueue.main.async {
                let alert = UIAlertController(title: "email.validationTitle".localized, message: "email.validationMessage".localized, preferredStyle: .alert)
                let ok = UIAlertAction(title: "OK", style: .default) { (action) in
                    User.shouldGoToLoginOnPop = true
                    self.navigationController?.popViewController(animated: true)
                }
                alert.addAction(ok)
                Utilities.hideSpinner()
                self.present(alert, animated: true, completion: nil)
            }
        }
    }
    
    // for email password users
    func createFirebaseUser(email: String, password: String, closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        Auth.auth().createUser(withEmail: email, password: password) { authResult, error in
            guard error == nil else {
                if AuthErrorCode(rawValue: error!._code) == AuthErrorCode(rawValue: 17007) { // can't create since account already exists
                    Auth.auth().signIn(withEmail: email, password: password) { (authResult, error) in
                        guard error == nil else {
                            Utilities.hideSpinner()
                            if AuthErrorCode(rawValue: error!._code) == AuthErrorCode(rawValue: 17009) { // account exists but bad password
                                // wipe clean and give unexpected error message
                                closure(false, error)
                            }
                            return
                        }
                        self.handleAuthResult(authResult: authResult)
                        return
                    }
                }
                return
            }
            self.handleAuthResult(authResult: authResult)
            return
        }

    }
    
    // post-authentication handling for email password users
    func handleAuthResult(authResult: AuthDataResult?) {
        if let use = authResult?.user {
            User.flexter.flexterName = use.displayName ?? ""
            User.userId = use.uid
            User.flexter.email = use.email ?? ""
            User.isEmailVerified = use.isEmailVerified
            if use.photoURL?.absoluteString != nil {
                User.flexter.profileImage = use.photoURL?.absoluteString ?? ""
            }
            if !use.isEmailVerified {
                self.sendVerificationEmail()
            } else {
                // is logged in and has verified email, get this to GameFlex
                if let email = User.flexter.email, email != "" {
                    GFNetworkServices.loginAtGameFlexWithEmail(email) { (success, error) in
                        guard error == nil else {
                            DispatchQueue.main.async {
                                let alert = UIAlertController(title: "Error",
                                                              message: "Something unexpected happened. Try again.",
                                                              preferredStyle: .alert)
                                let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
                                alert.addAction(ok)
                                self.present(alert, animated: true, completion: nil)
                                return
                            }
                            return
                        }
                        if success, let userId = User.userId {
                            GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                                if success {
                                    if let flexter = flexter {
                                        User.updateTheUser(flexter)
                                    }
                                    DispatchQueue.main.async {
                                        Utilities.hideSpinner()
                                        let alert = UIAlertController(title: "Success",
                                                                      message: "",
                                                                      preferredStyle: .alert)
                                        switch User.authenticationService {
                                        case .apple: alert.message = "register.successApple".localized
                                        case .google: alert.message = "register.successGoogle".localized
                                        case .email: alert.message = "register.successEmail".localized
                                        case .none: alert.message = "register.successNone".localized
                                        }
                                        let ok = UIAlertAction(title: "OK", style: .default, handler: { _ in
                                            User.shouldDismissOnPop = true
                                            self.navigationController?.popViewController(animated: true)
                                        })
                                        alert.addAction(ok)
                                        self.present(alert, animated: true, completion: nil)
                                    }
                                }
                            }
                            
                        }
                    }
                }
            }
        }
    }
    
}

// MARK: - on the spot permission checks
extension GFViewController {
    
    func checkAuthorizationStatus(_ service: PrivacyPermissionsOption, closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        switch service {
        case .camera:
            switch AVCaptureDevice.authorizationStatus(for: AVMediaType.video) {
                case .authorized: // The user has previously granted access to the camera.
                    closure(true, nil)
                case .notDetermined: // The user has not yet been asked for camera access.
                    AVCaptureDevice.requestAccess(for: .video) { granted in
                        if granted {
                            closure(true, nil)
                        } else {
                            closure(false, nil)
                        }
                    }

                case .denied: // The user has previously denied access.
                    closure(false, nil)
                case .restricted: // The user can't grant access due to restrictions.
                    closure(false, nil)
            @unknown default:
                print("unexpected authorization status result.")
                closure(false, nil)
            }

        case .library:
            switch PHPhotoLibrary.authorizationStatus() {
            case PHAuthorizationStatus.authorized:
                closure(true, nil)
            case PHAuthorizationStatus.notDetermined:
                PHPhotoLibrary.requestAuthorization { ( result ) in
                    if result == PHAuthorizationStatus.authorized {
                        closure(true, nil)
                    } else {
                        closure(false, nil)
                    }
                }
            case PHAuthorizationStatus.denied:
                closure(false, nil)
            case .restricted:
                closure(false, nil)
            default:
                print("unexpected authorization status result.")
                closure(false, nil)

            }
        case .microphone:
            switch AVAudioSession.sharedInstance().recordPermission {
            case AVAudioSessionRecordPermission.granted:
                closure(true, nil)
            case AVAudioSessionRecordPermission.denied:
                closure(false, nil)
            case AVAudioSessionRecordPermission.undetermined:
                AVAudioSession.sharedInstance().requestRecordPermission({ (granted) in
                    if granted {
                            closure(true, nil)
                    } else {
                        closure(false, nil)
                    }
                })
            default:
                closure(false, nil)
            }

        case .notifications:
            let current = UNUserNotificationCenter.current()
            current.getNotificationSettings(completionHandler: { (settings) in
                if settings.authorizationStatus == .notDetermined {
                    // Notification permission has not been asked yet, go for it!
                    UNUserNotificationCenter.current().requestAuthorization(options: [.badge, .sound, .alert]) { (result, error) in
                        if result {
                            closure(true, nil)
                        } else {
                            closure(false, nil)
                        }
                    }
                    
                } else if settings.authorizationStatus == .denied {
                    // Notification permission was previously denied, go to settings & privacy to re-enable
                    closure(false, nil)
                    
                } else if settings.authorizationStatus == .authorized {
                    // Notification permission was already granted
                    closure(true, nil)
                }
            })
            
        }
    }
    
    func showGoToSettingsAlert(_ types: [PrivacyPermissionsOption]) {
        let alert = UIAlertController(title: "Go To Settings", message: "You have previously disallowed Push Notifications. Go to Settings > GameFlex > Push Notifications to allow.", preferredStyle: .alert)

        if types.count == 1 {
            switch types[0] {
            
            case .library:
                alert.message = "You have previously disallowed Photo Library access. Go to Settings > GameFlex > Photo Library to allow."
            case .camera:
                alert.message = "You have previously disallowed Camera access. Go to Settings > GameFlex > Camera to allow."
            case .microphone:
                alert.message = "You have previously disallowed Microphone access. Go to Settings > GameFlex > Microphone to allow."
            case .notifications:
                break
            }
        } else {
            var mess = ""
            for type in types {
                if type == types.first {
                    mess = "\(type)"
                } else if type == types.last {
                    mess = "\(mess) and \(type.rawValue). Go to Settings > GameFlex to allow."
                } else {
                    mess = "\(mess), \(type.rawValue)"
                }
            }
            
            alert.message = "You have previously diallowed these required services: \(mess)"
        }
        let action = UIAlertAction(title: "Go To Settings", style: .default) { (action) in
            if let settingsUrl = URL(string: UIApplication.openSettingsURLString) {
               UIApplication.shared.open(settingsUrl)
             }
        }
        let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
        alert.addAction(action)
        alert.addAction(ok)
        self.present(alert, animated: true, completion: nil)

    }

}
