//
//  BreadcrumbTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 01/14/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class BreadcrumbTableViewCell: UITableViewCell {
    
    @IBOutlet weak var profileImageViewWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var blurView: UIView!
    @IBOutlet weak var commentTextView: UITextView!
    @IBOutlet weak var flexterName: UILabel!
    @IBOutlet var profileImageView: UIImageView!
    @IBOutlet weak var profileButton: UIButton!
    @IBOutlet weak var statsLabel: UILabel!
            
    weak var delegate: ProfileDelegate?
    
    static var cellIdentifier = String(describing: BreadcrumbTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageView.layer.borderWidth = 1
        commentTextView.layer.shadowColor = UIColor.black.cgColor
        commentTextView.layer.shadowOpacity = 1.0
        commentTextView.layer.shadowOffset = CGSize(width: 2.0, height: 2.0)
        commentTextView.layer.shadowRadius = 2.0
        blurView.layer.cornerRadius = blurView.frame.height/2
        blurView.clipsToBounds = true
        if blurView.subviews.filter({ $0.tag == 9922 }).count == 0 {
            let blurEffect = UIBlurEffect(style: UIBlurEffect.Style.systemMaterialDark)
            let blurEffectView = UIVisualEffectView(effect: blurEffect)
            blurEffectView.frame = blurView.bounds
            blurEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
            blurEffectView.tag = 9922
            blurView.addSubview(blurEffectView)
            blurView.sendSubviewToBack(blurEffectView)
        }
    }
    
    func configureCell(following: Bool = false, urlString: String?, flexterId: String?) {
        if profileImageView != nil, let urlString = urlString, let flexterId = flexterId {
            profileImageView.clipsToBounds = true
            let image = UIImage(named: FlexManager.randomImagePlaceholder(flexterId))
            let url = URL(string: urlString)
            let processor = DownsamplingImageProcessor(size: profileImageView.bounds.size)
            profileImageView?.kf.setImage(
                with: url,
                placeholder: image,
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success:
                            self.profileImageViewWidthConstraint.constant = 40
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.profileImageView?.image = image
                        }
                    })
        }
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageViewWidthConstraint.constant = 40
    }
        
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImageView.image = nil
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageView.layer.borderWidth = 1
        profileImageView?.layer.borderColor = UIColor.clear.cgColor
        
    }
    
    @IBAction func didTap(_ sender: UIButton) {
        delegate?.didTapForProfileAction(.goToProfile, nil)
    }
}
