<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="BreadcrumbChannelTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="484" height="48"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZoG-wD-gjb">
                    <rect key="frame" x="295.5" y="4" width="154" height="44"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GmR-sb-FjR">
                            <rect key="frame" x="2" y="2" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="40" id="09Y-vz-yv8"/>
                                <constraint firstAttribute="height" constant="40" id="sP9-Zo-WPi"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="20"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="clipsToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M5A-cO-fQh">
                            <rect key="frame" x="2" y="2" width="40" height="40"/>
                            <connections>
                                <action selector="didTap:" destination="iN0-l3-epB" eventType="touchUpInside" id="7Oi-bm-kGF"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="GameFlexter" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Mzq-HE-kyv">
                            <rect key="frame" x="50" y="13.5" width="88" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="6xb-yO-nxe"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qb1-Xb-dfb">
                            <rect key="frame" x="50" y="29" width="24" height="14"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="2lw-Yh-Toq"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="GmR-sb-FjR" firstAttribute="centerY" secondItem="ZoG-wD-gjb" secondAttribute="centerY" id="0g2-s8-dR4"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="top" secondItem="GmR-sb-FjR" secondAttribute="top" id="1dh-AR-GFs"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="trailing" secondItem="GmR-sb-FjR" secondAttribute="trailing" id="IGW-EE-Xdo"/>
                        <constraint firstItem="Mzq-HE-kyv" firstAttribute="centerY" secondItem="ZoG-wD-gjb" secondAttribute="centerY" id="TTp-eK-n7U"/>
                        <constraint firstItem="Mzq-HE-kyv" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="trailing" constant="8" id="f7C-Qv-jCJ"/>
                        <constraint firstItem="GmR-sb-FjR" firstAttribute="leading" secondItem="ZoG-wD-gjb" secondAttribute="leading" constant="2" id="jft-Bg-nMo"/>
                        <constraint firstItem="qb1-Xb-dfb" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="trailing" constant="8" id="nxv-hw-RU3"/>
                        <constraint firstAttribute="height" constant="44" id="rDq-q0-fnA"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="bottom" secondItem="GmR-sb-FjR" secondAttribute="bottom" id="upR-Xf-4Ue"/>
                        <constraint firstItem="M5A-cO-fQh" firstAttribute="leading" secondItem="GmR-sb-FjR" secondAttribute="leading" id="wPQ-wd-ReY"/>
                        <constraint firstItem="qb1-Xb-dfb" firstAttribute="top" secondItem="Mzq-HE-kyv" secondAttribute="bottom" constant="-1.5" id="wju-RF-yk4"/>
                        <constraint firstAttribute="trailing" secondItem="Mzq-HE-kyv" secondAttribute="trailing" constant="16" id="zmv-mG-LrA"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="y5x-iY-HDe">
                    <rect key="frame" x="58" y="4" width="233.5" height="44"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pJi-Fn-Q12">
                            <rect key="frame" x="2" y="2" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="40" id="cRW-i5-S18"/>
                                <constraint firstAttribute="height" constant="40" id="qRU-tW-EK2"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="20"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="boolean" keyPath="clipsToBounds" value="YES"/>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gzT-na-e9y">
                            <rect key="frame" x="2" y="2" width="40" height="40"/>
                            <connections>
                                <action selector="didTap:" destination="iN0-l3-epB" eventType="touchUpInside" id="Efh-V1-VyA"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Channel With long name" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iNd-4I-wWZ">
                            <rect key="frame" x="50" y="13.5" width="167.5" height="17"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="SGi-Y5-Dxb"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="m1n-1j-5CB">
                            <rect key="frame" x="50" y="29" width="24" height="14"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="CME-Ig-cSn"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="44" id="12Q-uR-WqF"/>
                        <constraint firstItem="m1n-1j-5CB" firstAttribute="top" secondItem="iNd-4I-wWZ" secondAttribute="bottom" constant="-1.5" id="2Gf-kC-Orn"/>
                        <constraint firstItem="m1n-1j-5CB" firstAttribute="leading" secondItem="pJi-Fn-Q12" secondAttribute="trailing" constant="8" id="570-5y-8KE"/>
                        <constraint firstItem="gzT-na-e9y" firstAttribute="top" secondItem="pJi-Fn-Q12" secondAttribute="top" id="E12-rm-kx2"/>
                        <constraint firstItem="iNd-4I-wWZ" firstAttribute="leading" secondItem="pJi-Fn-Q12" secondAttribute="trailing" constant="8" id="H3T-Sc-ydq"/>
                        <constraint firstItem="pJi-Fn-Q12" firstAttribute="centerY" secondItem="y5x-iY-HDe" secondAttribute="centerY" id="KiX-vt-XE0"/>
                        <constraint firstItem="gzT-na-e9y" firstAttribute="leading" secondItem="pJi-Fn-Q12" secondAttribute="leading" id="OPf-i6-UaQ"/>
                        <constraint firstAttribute="trailing" secondItem="iNd-4I-wWZ" secondAttribute="trailing" constant="16" id="RNC-Ex-6g7"/>
                        <constraint firstItem="pJi-Fn-Q12" firstAttribute="leading" secondItem="y5x-iY-HDe" secondAttribute="leading" constant="2" id="Tet-nk-hFw"/>
                        <constraint firstItem="gzT-na-e9y" firstAttribute="bottom" secondItem="pJi-Fn-Q12" secondAttribute="bottom" id="UWV-vN-ShY"/>
                        <constraint firstItem="gzT-na-e9y" firstAttribute="trailing" secondItem="pJi-Fn-Q12" secondAttribute="trailing" id="Ynw-gu-Pak"/>
                        <constraint firstItem="iNd-4I-wWZ" firstAttribute="centerY" secondItem="y5x-iY-HDe" secondAttribute="centerY" id="rwZ-3M-uXV"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bvD-ya-Es5">
                    <rect key="frame" x="10" y="4" width="44" height="44"/>
                    <subviews>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="b59-dx-2Rt">
                            <rect key="frame" x="7" y="7" width="30" height="30"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="30" id="FBI-Ch-JnZ"/>
                                <constraint firstAttribute="height" constant="30" id="taN-lB-usa"/>
                            </constraints>
                            <state key="normal" image="cancelWhite"/>
                            <connections>
                                <action selector="didTap:" destination="iN0-l3-epB" eventType="touchUpInside" id="C2z-JL-jdY"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="b59-dx-2Rt" firstAttribute="centerX" secondItem="bvD-ya-Es5" secondAttribute="centerX" id="80j-M3-k5J"/>
                        <constraint firstItem="b59-dx-2Rt" firstAttribute="centerY" secondItem="bvD-ya-Es5" secondAttribute="centerY" id="HWL-P6-eoP"/>
                        <constraint firstAttribute="height" constant="44" id="meV-Ne-EI8"/>
                        <constraint firstAttribute="width" constant="44" id="sW0-Cm-681"/>
                    </constraints>
                </view>
                <textView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" editable="NO" text="Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu," selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6Yo-0B-bS1">
                    <rect key="frame" x="58" y="42" width="415" height="45"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="45" id="AZe-ID-kSh"/>
                    </constraints>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                </textView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="leading" secondItem="y5x-iY-HDe" secondAttribute="leading" id="2DB-bh-zqf"/>
                <constraint firstItem="6Yo-0B-bS1" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="42" id="BUU-5k-Z3o"/>
                <constraint firstItem="bvD-ya-Es5" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="Bfc-tQ-Vl0"/>
                <constraint firstItem="bvD-ya-Es5" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="10" id="C7C-OL-zyE"/>
                <constraint firstItem="ZoG-wD-gjb" firstAttribute="leading" secondItem="y5x-iY-HDe" secondAttribute="trailing" constant="4" id="GN9-ca-mox"/>
                <constraint firstItem="y5x-iY-HDe" firstAttribute="leading" secondItem="bvD-ya-Es5" secondAttribute="trailing" constant="4" id="QfF-yE-w6Q"/>
                <constraint firstAttribute="trailing" secondItem="6Yo-0B-bS1" secondAttribute="trailing" constant="11" id="aR6-V2-IH7"/>
                <constraint firstItem="ZoG-wD-gjb" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="aXx-Jn-HlH"/>
                <constraint firstItem="y5x-iY-HDe" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="b86-Qk-KZj"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="blurView" destination="ZoG-wD-gjb" id="cjR-gH-Xlb"/>
                <outlet property="cancelButton" destination="b59-dx-2Rt" id="WtA-wu-NtY"/>
                <outlet property="cancelView" destination="bvD-ya-Es5" id="M8x-56-isy"/>
                <outlet property="channelButton" destination="gzT-na-e9y" id="omD-Wi-WtW"/>
                <outlet property="channelImageView" destination="pJi-Fn-Q12" id="B14-Uh-tdb"/>
                <outlet property="channelImageViewWidthConstraint" destination="cRW-i5-S18" id="hOD-Sb-tem"/>
                <outlet property="channelName" destination="iNd-4I-wWZ" id="pl5-fJ-F56"/>
                <outlet property="channelStatsLabel" destination="m1n-1j-5CB" id="bE0-ST-ciH"/>
                <outlet property="channelView" destination="y5x-iY-HDe" id="KLB-41-GnN"/>
                <outlet property="commentTextView" destination="6Yo-0B-bS1" id="5Hl-Bd-fdd"/>
                <outlet property="flexterName" destination="Mzq-HE-kyv" id="xlK-YM-6Ve"/>
                <outlet property="profileButton" destination="M5A-cO-fQh" id="tev-d2-mHD"/>
                <outlet property="profileImageView" destination="GmR-sb-FjR" id="pjW-ZC-x5h"/>
                <outlet property="profileImageViewWidthConstraint" destination="09Y-vz-yv8" id="zqY-hX-Tv7"/>
                <outlet property="statsLabel" destination="qb1-Xb-dfb" id="boD-sM-31P"/>
            </connections>
            <point key="canvasLocation" x="-207.24637681159422" y="-182.14285714285714"/>
        </view>
    </objects>
    <resources>
        <image name="cancelWhite" width="14" height="14"/>
    </resources>
</document>
