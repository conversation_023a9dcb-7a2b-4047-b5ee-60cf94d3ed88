//
//  FavoriteChannelTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/21/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FavoriteChannelTableViewCell: UITableViewCell {
    
    @IBOutlet weak var collectionView: UICollectionView!
    
    var channels: [Channel] = []
    
    static var cellIdentifier = String(describing: FavoriteChannelTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UINib(nibName: FavoriteChannelCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FavoriteChannelCollectionViewCell.cellIdentifier)
    }
    
    
}

extension FavoriteChannelTableViewCell: UICollectionViewDataSource, UICollectionViewDelegate,     UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FavoriteChannelCollectionViewCell.cellIdentifier, for: indexPath) as? FavoriteChannelCollectionViewCell else { return FavoriteChannelCollectionViewCell() }
        cell.channel = channels[indexPath.row]
        cell.configureCell()
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 80.0, height: 87.0)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
    }
    
}
