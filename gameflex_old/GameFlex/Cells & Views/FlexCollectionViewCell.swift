//
//  FlexCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class FlexCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var flexImageView: UIImageView!
    @IBOutlet weak var newButton: UIButton!
        
    static var cellIdentifier = String(describing: FlexCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        newButton.setTitleColor(.black, for: .normal)
        newButton.backgroundColor = .gfGreen
        newButton.layer.cornerRadius = newButton.frame.size.height/2
        showAsNew()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        flexImageView.image = nil
        showAsNew()
    }
    
    func showAsNew(_ new: Bool = false) {
        if new {
            newButton.isHidden = false
        } else {
            newButton.isHidden = true
        }
    }
    
    func configureCell(_ flex: Flex) {
        flexImageView.clipsToBounds = true
        let image = #imageLiteral(resourceName: "loadingPlaceholder") //FlexManager.placeholders.randomElement()
        if let urlString = flex.mediaUrl?[0] {
        let url = URL(string: urlString)
        let processor = DownsamplingImageProcessor(size: flexImageView.bounds.size)
            flexImageView?.kf.setImage(
            with: url,
            placeholder: image,
            options: [
                .processor(processor),
                .scaleFactor(UIScreen.main.scale),
                .transition(.fade(1)),
                .cacheOriginalImage
            ], completionHandler:
                {
                    result in
                    switch result {
                    case .success: break
                    case .failure(let error):
                        print("Job failed: \(error.localizedDescription)")
                        self.flexImageView?.image = image
                    }
                })
        } else {
            self.flexImageView?.image = image
        }
    }
}
