//
//  AlertTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/9/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class AlertTableViewCell: UITableViewCell {
    
    @IBOutlet weak var sourceImageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var flexImageView: UIImageView!
    @IBOutlet weak var sourceSuperView: UIView!
    @IBOutlet weak var cellView: UIView!
    @IBOutlet weak var profileButton: UIButton!
    
    weak var delegate: HomeDelegate?
    
    var note: GFNotification?
    
    static var cellIdentifier = String(describing: AlertTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = ""
    }
    
    func configureCell(_ note: GFNotification) {
        cellView.backgroundColor = .gfDarkBackground40
        selectionStyle = .none
        sourceSuperView.layer.cornerRadius = sourceSuperView.frame.size.width/2
        sourceSuperView.clipsToBounds = true

        self.note = note
        flexImageView.layer.borderColor = UIColor.gfGrayText.cgColor
        flexImageView.layer.borderWidth  = 1.0
        
        var titleString = ""
        let source = note.source?.flexterName ?? "Someone"
        let suffix = "\(Utilities.relativeTime(date: note.date))"

        switch note.notificationType { // the other types are handled by AlertButtonTableViewCell
        case .like:
            titleString = "\(source) liked your flex. \(suffix)"
        case .comment:
            titleString = "\(source) commented on your flex. \(suffix)"
        case .deleteChannel:
            let channelName = note.channel?.channelName ?? "unknown"
            titleString = "The channel \(channelName), was deleted. Sigh. \(suffix)"
        case .reflex:
            titleString = "\(source) reflexed your flex. \(suffix)"
        default:
            titleString = "\(source) generated an unknown message. \(suffix)"
        }
        let attributed = NSMutableAttributedString(string: titleString)
        if titleString.contains("channel"), let channel = note.channel?.channelName {
            attributed.addAttribute(NSAttributedString.Key.font, value: UIFont.boldSystemFont(ofSize: 14), range: NSString(string: titleString).range(of: channel) )
        }
        attributed.addAttribute(NSAttributedString.Key.font, value: UIFont.boldSystemFont(ofSize: 14), range: NSRange(location: 0, length: source.count))
        attributed.addAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGrayText,
                                  NSAttributedString.Key.font: UIFont.systemFont(ofSize: 9)], range: NSString(string: titleString).range(of: suffix) )
        titleLabel.attributedText = attributed
        // avatar view
        if let userId = note.source?.userId {
            sourceImageView.image = #imageLiteral(resourceName: FlexManager.randomImagePlaceholder(userId))
        }
        if let pic = note.source?.profileImage, pic != "", let objectId = note.source?.userId {
            let url = URL(string: pic)
            let processor = DownsamplingImageProcessor(size: sourceImageView?.bounds.size ?? CGSize(width: 40, height: 40))
            sourceImageView?.kf.indicatorType = .activity
            sourceImageView?.kf.setImage(
                with: url,
                placeholder: UIImage(named: FlexManager.randomImagePlaceholder(objectId)),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success: break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                        }
                    })
        }
        sourceSuperView.layer.cornerRadius = sourceSuperView.frame.size.width/2
        sourceSuperView.layer.borderColor = UIColor.gfYellow_F2DE76.cgColor
        sourceSuperView.layer.borderWidth  = 1.0
        selectionStyle = .none
        // flex pic
        if note.flexMedia?.count ?? 0 > 0, let pic = note.flexMedia?[0], pic != "" {
            let url = URL(string: pic)
            let processor = DownsamplingImageProcessor(size: flexImageView?.bounds.size ?? CGSize(width: 45.5, height: 60))
            flexImageView?.kf.indicatorType = .activity
            flexImageView?.kf.setImage(
                with: url,
                placeholder: UIImage(named: FlexManager.randomImagePlaceholder(note.channel?.channelId)),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success:
                            self.flexImageView.contentMode = .scaleAspectFill
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                        }
                    })
        }
        if note.notificationType == .deleteChannel {
            flexImageView.isHidden = true
        } else {
            flexImageView.isHidden = false
        }
    }
        
    @IBAction func didTapButton(_ sender: UIButton) {
        if sender == profileButton, let userId = self.note?.source?.userId {
            delegate?.didTapForProfileAction(flexterId: userId)
        }
    }

}
