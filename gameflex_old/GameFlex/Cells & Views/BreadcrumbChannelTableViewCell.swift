//
//  BreadcrumbChannelTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 01/24/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class BreadcrumbChannelTableViewCell: UITableViewCell {
    
    @IBOutlet weak var profileImageViewWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var blurView: UIView!
    @IBOutlet weak var commentTextView: UITextView!
    @IBOutlet weak var flexterName: UILabel!
    @IBOutlet var profileImageView: UIImageView!
    @IBOutlet weak var profileButton: UIButton!
    @IBOutlet weak var statsLabel: UILabel!
    @IBOutlet weak var channelImageViewWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var channelView: UIView!
    @IBOutlet weak var channelName: UILabel!
    @IBOutlet var channelImageView: UIImageView!
    @IBOutlet weak var channelButton: UIButton!
    @IBOutlet weak var channelStatsLabel: UILabel!
    @IBOutlet weak var cancelButton: UIButton!
    @IBOutlet weak var cancelView: UIView!
                
    weak var delegate: ProfileDelegate?
    
    static var cellIdentifier = String(describing: BreadcrumbChannelTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        channelImageView.layer.cornerRadius = channelImageView.frame.size.width/2
        commentTextView.layer.shadowColor = UIColor.black.cgColor
        commentTextView.layer.shadowOpacity = 1.0
        commentTextView.layer.shadowOffset = CGSize(width: 2.0, height: 2.0)
        commentTextView.layer.shadowRadius = 2.0
        blurView.layer.cornerRadius = blurView.frame.height/2
        channelView.layer.cornerRadius = channelView.frame.height/2
        channelView.clipsToBounds = true
        blurView.clipsToBounds = true
        if blurView.subviews.filter({ $0.tag == 9922 }).count == 0 {
            doTheBlur(blurView)
        }
        if channelView.subviews.filter({ $0.tag == 9922 }).count == 0 {
            doTheBlur(channelView)
        }
        if cancelView.subviews.filter({ $0.tag == 9922 }).count == 0 {
            doTheBlur(cancelView)
        }
        cancelView.layer.cornerRadius = cancelView.frame.size.width/2
        cancelView.clipsToBounds = true
    }
    
    fileprivate func doTheBlur(_ view: UIView) {
        let blurEffect = UIBlurEffect(style: UIBlurEffect.Style.systemMaterialDark)
        let blurEffectView = UIVisualEffectView(effect: blurEffect)
        blurEffectView.frame = view.bounds
        blurEffectView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        blurEffectView.tag = 9922
        view.addSubview(blurEffectView)
        view.sendSubviewToBack(blurEffectView)
    }
    
    func configureCell(following: Bool = false, urlString: String?, flexterId: String?) {
        if profileImageView != nil, let urlString = urlString, let flexterId = flexterId {
            profileImageView.clipsToBounds = true
            let image = UIImage(named: FlexManager.randomImagePlaceholder(flexterId))
            let url = URL(string: urlString)
            let processor = DownsamplingImageProcessor(size: profileImageView.bounds.size)
            profileImageView?.kf.setImage(
                with: url,
                placeholder: image,
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success:
                            self.profileImageViewWidthConstraint.constant = 40
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.profileImageView?.image = image
                        }
                    })
        }
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageViewWidthConstraint.constant = 40
    }
    
    func configureCellForChannel() {
        if channelImageView != nil, let urlString = FeedViewModel.channelDetail?.channelImage {
            channelImageView.clipsToBounds = true
            let image = UIImage(named: "bigG")
            let url = URL(string: urlString)
            let processor = DownsamplingImageProcessor(size: channelImageView.bounds.size)
            channelImageView?.kf.setImage(
                with: url,
                placeholder: image,
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success:
                            self.profileImageViewWidthConstraint.constant = 40
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.channelImageView?.image = image
                        }
                    })
        }
        channelImageView.layer.cornerRadius = channelImageView.frame.size.width/2
        channelImageViewWidthConstraint.constant = 40
        channelName.text = FeedViewModel.channelDetail?.channelName
        var flex = "flexes/day"
        if FeedViewModel.channelDetail?.flexVelocity == 1 {
            flex = "flex/day"
        }
        channelStatsLabel.text = "\(FeedViewModel.channelDetail?.flexVelocity?.truncate(places: 2) ?? 0) \(flex)"
    }
        
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImageView.image = nil
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        channelImageView.layer.cornerRadius = channelImageView.frame.size.width/2
    }
    
    @IBAction func didTap(_ sender: UIButton) {
        if sender == channelButton {
            delegate?.didTapForProfileAction(.goToChannelReview, nil)
        } else if sender == profileButton {
            delegate?.didTapForProfileAction(.goToProfile, nil)
        } else if sender == cancelButton {
            delegate?.didTapForProfileAction(.goToChannelDirectory, nil)
        }
    }
}
