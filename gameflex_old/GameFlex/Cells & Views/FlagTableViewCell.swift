//
//  FlagTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 1/12/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit

class FlagTableViewCell: UITableViewCell {
    
    @IBOutlet weak var reflexCountView: UIView!
    @IBOutlet weak var commentCountView: UIView!
    @IBOutlet weak var commentCountLabel: UILabel!
    @IBOutlet weak var reflexCountLabel: UILabel!
    @IBOutlet weak var commentCountViewTrailingConstraint: NSLayoutConstraint!
    @IBOutlet weak var reflexCountViewTrailingConstraint: NSLayoutConstraint!
    
    var shouldDisableTouches: Bool = false
    
    static var cellIdentifier = String(describing: FlagTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        let viewArray = [reflexCountView, commentCountView]
        viewArray.forEach({
            $0?.layer.cornerRadius = 17
            $0?.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMinXMinYCorner]
            $0?.backgroundColor = .gfGreen
        })
        commentCountLabel.textColor = .black
        reflexCountLabel.textColor = .black
        commentCountViewTrailingConstraint.constant = -70
        reflexCountViewTrailingConstraint.constant = -70
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        commentCountViewTrailingConstraint.constant = -70
        reflexCountViewTrailingConstraint.constant = -70
    }
    
    func configureCell(numberOfReflexes: Int, numberOfComments: Int) {
        reflexCountLabel.text = "\(numberOfReflexes)"
        commentCountLabel.text = "\(numberOfComments)"
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if !shouldDisableTouches { super.touchesBegan(touches, with: event) }
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if !shouldDisableTouches { super.touchesEnded(touches, with: event) }
    }

    
    func animateTheFlags() {
        commentCountView.isHidden = false
        reflexCountView.isHidden = false
        reflexCountViewTrailingConstraint.constant = 0
        commentCountViewTrailingConstraint.constant = 0
        UIView.animate(withDuration: 0.25, delay: 0.1, options: .curveEaseOut) {
            self.layoutIfNeeded()
        } completion: { (success) in
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                self.reflexCountViewTrailingConstraint.constant = -70
                self.commentCountViewTrailingConstraint.constant = -70
                UIView.animate(withDuration: 0.25, delay: 0.25, options: .curveEaseOut) {
                    self.layoutIfNeeded()
                }
            }
        }
    }
    
}
