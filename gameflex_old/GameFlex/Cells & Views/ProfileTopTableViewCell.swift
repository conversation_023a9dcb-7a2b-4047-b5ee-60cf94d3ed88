//
//  ProfileTopTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 9/29/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class ProfileTopTableViewCell: UITableViewCell {
    
    @IBOutlet weak var editButton: UIButton!
    @IBOutlet weak var rankLabel: UILabel!
    @IBOutlet weak var nameLabel: UILabel!
    @IBOutlet weak var flexterNameLabel: UILabel!
    @IBOutlet weak var personalSummaryTextView: UITextView!
    @IBOutlet weak var recentHashTagsLabel: UILabel!
    @IBOutlet weak var flexingSinceLabel: UILabel!
    @IBOutlet weak var starButton: UIButton!
    @IBOutlet weak var flexterImageView: UIImageView!
    @IBOutlet weak var imageSuperView: UIView!
    @IBOutlet weak var followButton: UIButton!
    
    @IBOutlet weak var channelsParticipatingInLabel: UILabel!
    @IBOutlet weak var recentFlexesCountLabel: UILabel!
    @IBOutlet weak var lifeTimeLikesLabel: UILabel!
    @IBOutlet weak var lifeTimeReflexesLabel: UILabel!
    
    var flexter: Flexter?
    var isProfileViewController = true
    
    weak var delegate: ProfileDelegate?
    weak var homeDelegate: HomeDelegate?

    static let cellIdentifier = String(describing: ProfileTopTableViewCell.self)
        
    override func awakeFromNib() {
        super.awakeFromNib()
        selectionStyle = .none
        // images and font presentations
        imageSuperView.layer.cornerRadius = imageSuperView.frame.size.width/2
        imageSuperView.clipsToBounds = true
        channelsParticipatingInLabel.textColor = .gfGreen
        recentFlexesCountLabel.textColor = .gfGreen
        lifeTimeLikesLabel.textColor = .gfGreen
        lifeTimeReflexesLabel.textColor = .gfGreen
        flexingSinceLabel.textColor = .gfGrayText
        followButton.applyPrimaryGFButtonStandards()
        followButton.backgroundColor = .gfGreen
        followButton.setTitle("comment.followButton.follow".localized, for: .normal)
        configureCell()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        flexterImageView.image = nil
        lifeTimeLikesLabel.text = ""
        recentFlexesCountLabel.text = ""
        channelsParticipatingInLabel.text = ""
        flexingSinceLabel.text = ""
        nameLabel.text = ""
        rankLabel.text = ""
        flexterNameLabel.text = ""
        personalSummaryTextView.text = ""
        recentHashTagsLabel.text = ""
    }
    
    func configureCell(_ flexter: Flexter? = nil, isProfileViewController: Bool = true) {
        self.flexter = flexter
        let isFollowing = User.flexter.following?.filter({$0.channelId == self.flexter?.userId}).first != nil
        Utilities.configure(followButton: followButton, following: isFollowing)
        self.isProfileViewController = isProfileViewController
        let df = DateFormatter()
        df.dateFormat = "MMMM yyyy"
        let flextr = flexter ?? User.flexter
        if let joined = flextr.joinedDate {
            let dated = df.string(from: Utilities.cleanUpServerDate(joined))
            flexingSinceLabel.text = "Flexing since \(dated)"
        }
        if let name = flextr.name {
            nameLabel.text = name
        }
        if flextr == User.flexter {
            nameLabel.textColor = .gfGreen
        } else {
            nameLabel.textColor = .gfOffWhite
        }
        rankLabel.text = "\(flextr.rank ?? "Warrior")"
        starButton.tintColor = flextr.rankColor
        flexterNameLabel.text = flextr.flexterName ?? "Your Flex Identity?"
        personalSummaryTextView.text = flextr.userDescription ?? "Who are you?"
        if flexter != nil, flexter?.userId != User.userId {
            editButton.isHidden = true
            flexterNameLabel.textColor = .gfOffWhite
            followButton.isHidden = false
        } else {
            editButton.isHidden = false
            flexterNameLabel.textColor = .gfGreen
            followButton.isHidden = true
        }
        var image = UIImage(named: "Layer 6")
        if let userId = flextr.userId {
            image = #imageLiteral(resourceName: FlexManager.randomImagePlaceholder(userId))
        }
        // placeholder smilie
        flexterImageView.image = image
        if let pic = flextr.profileImage, flextr.profileImage != "" {
            let url = URL(string: pic)
            let processor = DownsamplingImageProcessor(size: flexterImageView.bounds.size)
            flexterImageView.kf.indicatorType = .activity
            flexterImageView.kf.setImage(
                with: url,
                placeholder: UIImage(named: "Layer 6"), //FlexManager.randomImagePlaceholder()),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success:
                            break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.flexterImageView.image = image
                        }
                    })
        }
        
        imageSuperView.clipsToBounds = true
        
        if let recents = flextr.recentHashtags, recents.count > 0 {
            var hashes = ""
            var three: [String] = []
            if recents.count > 3 {
                three = [recents[0], recents[1], recents[2]]
            } else {
                three = recents
            }
            for str in three {
                if str == three.first {
                    hashes = str
                    continue
                }
                hashes = "\(hashes) \(str)"
            }
            recentHashTagsLabel.text = hashes
        }
        let participate = (flextr.channelsParticipated?.count ?? 0)
        let followed = (flextr.channelsFollowed?.count ?? 0)
        let owned = (flextr.channelsOwned?.count ?? 0)
        let channels = participate + owned + followed
        channelsParticipatingInLabel.text = "\(channels)"
        recentFlexesCountLabel.text = "\(flextr.flexCount ?? 0)"
        lifeTimeLikesLabel.text = "\(flextr.likesCount ?? 0)"
        lifeTimeReflexesLabel.text = "\(flextr.reflexCount ?? 0)"
        
    }
    
    @IBAction func didTapButton(_ sender: UIButton) {
        switch sender {
        case editButton:
            delegate?.didTapForProfileAction(.goToEdit, nil)
        case followButton:
            homeDelegate?.didTapToFollow(followButton, self)
        default: break
        }
    }
}
