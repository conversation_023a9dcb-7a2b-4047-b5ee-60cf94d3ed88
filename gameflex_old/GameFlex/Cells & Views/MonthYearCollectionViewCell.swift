//
//  MonthYearCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 9/15/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class MonthYearCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var label: UILabel!
    @IBOutlet weak var cellBackground:UIView!
    
    static var cellIdentifier = String(describing: MonthYearCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        cellBackground.layer.cornerRadius = 4.0
        label.font = .systemFont(ofSize: 24)
        cellBackground.clipsToBounds = true
        cellBackground.backgroundColor = .gfDarkGrayMask
        cellBackground.layer.borderColor = UIColor.gfGrayText.cgColor
        cellBackground.layer.borderWidth = 1.0
    }
}
