<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FlexCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="180"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="j9G-Ay-4Pv">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="180"/>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="r8d-hq-yNe">
                    <rect key="frame" x="183" y="10" width="48" height="16"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="16" id="1FR-86-Cab"/>
                        <constraint firstAttribute="width" constant="48" id="uqt-hs-1wM"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <state key="normal" title="New">
                        <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                </button>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="j9G-Ay-4Pv" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="B4m-ad-xL6"/>
                <constraint firstItem="j9G-Ay-4Pv" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="J3H-BU-A49"/>
                <constraint firstAttribute="trailing" secondItem="j9G-Ay-4Pv" secondAttribute="trailing" id="RSx-ok-j7I"/>
                <constraint firstAttribute="bottom" secondItem="j9G-Ay-4Pv" secondAttribute="bottom" id="Xfe-2r-yiA"/>
                <constraint firstItem="r8d-hq-yNe" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="dXP-RG-5fh"/>
                <constraint firstItem="r8d-hq-yNe" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="mUJ-pQ-7ko"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="flexImageView" destination="j9G-Ay-4Pv" id="CMk-Er-wVy"/>
                <outlet property="newButton" destination="r8d-hq-yNe" id="SWD-hz-Mfe"/>
            </connections>
            <point key="canvasLocation" x="101.44927536231884" y="-278.57142857142856"/>
        </view>
    </objects>
</document>
