//
//  EditProfileTextFieldTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class EditProfileTextFieldTableViewCell: UITableViewCell {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var textField: UITextField!
    
    var cellType: ProfileUpdateDataType = .name
    
    weak var delegate: ProfileDelegate?
    
    static var cellIdentifier = String(describing: EditProfileTextFieldTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        textField.delegate = self
        textField.tintColor = .gfGreen
        textField.layer.cornerRadius = 4.0
        textField.layer.borderWidth = 1.0
        textField.layer.borderColor = UIColor.gfGrayText.cgColor
        textField.backgroundColor = .gfDarkBackground
        let paddingView = UIView(frame: CGRect(x: 0, y: 0, width: 3, height: textField.frame.height))
        textField.leftView = paddingView
        textField.leftViewMode = UITextField.ViewMode.always
    }
}

extension EditProfileTextFieldTableViewCell: UITextFieldDelegate {
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard string != "\n" else {
            return false
        }
        
        let result = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) ?? string
        delegate?.updateTextFor(cellType, text: result)
        return true
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        if cellType == .flexterName {
            delegate?.didTapForProfileAction(.checkForTaken, nil)
        }
    }
}
