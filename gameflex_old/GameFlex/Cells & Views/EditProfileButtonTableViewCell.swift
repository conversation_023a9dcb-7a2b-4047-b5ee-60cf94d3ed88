//
//  EditProfileButtonTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class EditProfileButtonTableViewCell: UITableViewCell {
    
    @IBOutlet weak var button: UIButton!
    
    weak var delegate: ProfileDelegate?
    
    static var cellIdentifier = String(describing: EditProfileButtonTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        button.setTitle("Submit", for: .normal)
        button.setTitleColor(.black, for: .normal)
        button.backgroundColor = .gfGreen
        button.layer.cornerRadius = 4.0
    }
    
    @IBAction func didTapButton(_ sender: UIButton) {
        delegate?.didTapForProfileAction(.makeTheCall, nil)
    }
    
}
