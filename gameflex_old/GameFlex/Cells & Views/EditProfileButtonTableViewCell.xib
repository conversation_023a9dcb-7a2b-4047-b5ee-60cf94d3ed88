<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="EditProfileButtonTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="101"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IEB-lb-ZEA">
                    <rect key="frame" x="16" y="16" width="382" height="69"/>
                    <state key="normal" title="Button"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="ra3-6P-vvk"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="IEB-lb-ZEA" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="16" id="S5k-X4-izz"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="trailing" secondItem="IEB-lb-ZEA" secondAttribute="trailing" constant="16" id="bPd-fY-fW3"/>
                <constraint firstItem="IEB-lb-ZEA" firstAttribute="leading" secondItem="vUN-kp-3ea" secondAttribute="leading" constant="16" id="bjz-Ua-BZd"/>
                <constraint firstItem="vUN-kp-3ea" firstAttribute="bottom" secondItem="IEB-lb-ZEA" secondAttribute="bottom" constant="16" id="lwz-th-IKy"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="button" destination="IEB-lb-ZEA" id="meE-Sq-CTB"/>
            </connections>
            <point key="canvasLocation" x="343.47826086956525" y="298.99553571428572"/>
        </view>
    </objects>
</document>
