//
//  ChannelTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class ChannelTableViewCell: UITableViewCell {
    
    @IBOutlet weak var backView: UIView!
    @IBOutlet weak var profileImageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var subTitleLabel: UILabel!
    @IBOutlet weak var moreButton: UIButton!
    @IBOutlet weak var flexImageView: UIImageView!
    @IBOutlet weak var favButton: UIButton!
    @IBOutlet weak var memberCountLabel: UILabel!
    @IBOutlet weak var statsLabel: UILabel!
    @IBOutlet weak var subsLabel: UILabel!
    @IBOutlet weak var newButton: UIButton!
    @IBOutlet weak var favButtonWidthConstraint: NSLayoutConstraint!
    @IBOutlet weak var moreButtonWidthConstraint: NSLayoutConstraint!
    
    weak var delegate: ProfileDelegate?
    
    var channel: Channel?
    var channelDetail: ChannelDetail?
    
    static var cellIdentifier = String(describing: ChannelTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        configureCell()
        newButton.isHidden = true
        moreButton.isHidden = false
        favButton.isHidden = false
        moreButton.setImage(#imageLiteral(resourceName: "dotsBlack"), for: .normal)
        moreButton.backgroundColor = .gfGreen
    }
    
    func configureCellForBlank() {
        titleLabel.text = "Create New Channel"
        subTitleLabel.text = "Brief channel description. Why should I follow?"
        statsLabel.text = "-- The channel's flex velocity"
        moreButton.isHidden = true
        favButton.isHidden = true
        moreButtonWidthConstraint.constant = 0
        favButtonWidthConstraint.constant = 0
        profileImageView.image = #imageLiteral(resourceName: "plusCircle")
        flexImageView.addDashedBorder()
        profileImageView.layer.borderColor = UIColor.clear.cgColor
        newButton.isHidden = false
        flexImageView.image = nil
    }
    
    func configureCell() {
        flexImageView.layer.sublayers?.filter({ $0.name == "dashedBorder" }).forEach({ $0.removeFromSuperlayer() })
        titleLabel.text = channel?.channelName ?? ""
        let buttons = [moreButton, favButton]
        buttons.forEach({$0?.layer.cornerRadius = 4 })
        flexImageView.layer.cornerRadius = 4
        flexImageView.layer.borderWidth = 1
        flexImageView.layer.borderColor = UIColor.gfDarkBackground.cgColor
        profileImageView.clipsToBounds = true
        let im = #imageLiteral(resourceName: "channelMask")
        if let urlString = channel?.channelImage, urlString != "" {
            let url = URL(string: urlString)
            let processor = DownsamplingImageProcessor(size: profileImageView.bounds.size)
            profileImageView?.kf.setImage(
                with: url,
                placeholder: im,
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success: break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                            self.profileImageView?.image = im
                        }
                    })
        } else {
            self.profileImageView?.image = im
        }
        profileImageView.layer.cornerRadius = profileImageView.frame.size.width/2
        profileImageView.layer.borderWidth = 1
        profileImageView.layer.borderColor = UIColor.gfBlue_00D5FF.cgColor

        subTitleLabel.text = channel?.channelDescription ?? "Content for the ages"
    }
    
    func favButtonIsSelected(_ selected: Bool = true) {
        if selected {
            favButton.setImage(#imageLiteral(resourceName: "favsGreen"), for: .normal)
            favButton.layer.borderWidth = 1
            favButton.layer.borderColor = UIColor.gfGreen.cgColor
            favButton.backgroundColor = .gfDarkBackground100
        } else {
            favButton.setImage(#imageLiteral(resourceName: "favsBlackBorder"), for: .normal)
            favButton.layer.borderWidth = 0
            favButton.layer.borderColor = UIColor.gfGreen.cgColor
        }
    }
    
    @objc func getlatestFlexFromChannel() {
        if let channelId = channel?.channelId {
            GFNetworkServices.getChannelFlexes(channelId: channelId, top: true, { (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if !result.isEmpty {
                    var flexArray: [Flex] = result
                    flexArray = Array(Set(flexArray))
                    flexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    DispatchQueue.main.async {
                        if let urlString = flexArray.first?.mediaUrl?.first, let flexId = flexArray.first?.flexId {
                            let image = UIImage(named: FlexManager.randomImagePlaceholder(flexId))
                            let url = URL(string: urlString)
                            let processor = DownsamplingImageProcessor(size: self.flexImageView?.bounds.size ?? CGSize(width: 100, height: 132))
                            self.flexImageView?.kf.setImage(
                                with: url,
                                placeholder: image,
                                options: [
                                    .processor(processor),
                                    .scaleFactor(UIScreen.main.scale),
                                    .transition(.fade(1)),
                                    .cacheOriginalImage
                                ], completionHandler:
                                    {
                                        result in
                                        switch result {
                                        case .success: break
                                        case .failure(let error):
                                            print("Job failed: \(error.localizedDescription)")
                                            self.profileImageView?.image = image
                                        }
                                    })
                        }
                    }
                }
            })
        }
    }
    
    @objc func getChannelDetails() {
        if let channelId = channel?.channelId {
            Utilities.showSpinner()
            GFNetworkServices.getChannelDetailFor(channelId: channelId) { (success, detail, error) in
                self.channelDetail = detail
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    var partWords = "contributors"
                    if detail.participants?.count == 1 {
                        partWords = "contributor"
                    }
                    var flex = "flexes/day"
                    if detail.flexVelocity == 1 {
                        flex = "flex/day"
                    }
                    var follow = "subscribers"
                    if detail.followerCount == 1 {
                        follow = "subscriber"
                    }
                    self.memberCountLabel.text = "\((detail.participants?.count ?? 0)) \(partWords)"
                    self.statsLabel.text = "\(detail.flexVelocity?.truncate(places: 2) ?? 0) \(flex)"
                    self.subsLabel.text = "\(detail.followerCount ?? 0) \(follow)"

                    self.subTitleLabel.text = "\(detail.channelDescription ?? "")"
                }
            }
        }
    }

    @IBAction func didTapButton(_ sender: UIButton) {
        switch sender {
        case moreButton:
            delegate?.didTapForProfileAction(.goToInvites, channelDetail as Any)
        case favButton:
            delegate?.didTapForProfileAction(.favThisChannel, channelDetail as Any)
        case newButton:
            delegate?.didTapForProfileAction(.newChannel, nil)
        default: break
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImageView.image = nil
        newButton.isHidden = true
        moreButtonWidthConstraint.constant = 33
        favButtonWidthConstraint.constant = 33
        newButton.isHidden = true
        moreButton.isHidden = false
        favButton.isHidden = false
//        titleLabel.text = ""
//        subTitleLabel.text = ""
    }
}
