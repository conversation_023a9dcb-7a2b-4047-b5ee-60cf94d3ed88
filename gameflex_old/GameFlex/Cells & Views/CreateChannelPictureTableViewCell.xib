<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="CreateChannelPictureTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="155"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="EnK-RL-dIL">
                    <rect key="frame" x="172" y="42.5" width="70" height="70"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Layer 6" translatesAutoresizingMaskIntoConstraints="NO" id="dIr-gK-GMT">
                            <rect key="frame" x="0.0" y="0.0" width="70" height="70"/>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="a4y-9W-3GV">
                            <rect key="frame" x="0.0" y="55" width="70" height="17"/>
                            <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="17" id="F2N-nX-YUf"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                            <state key="normal" title="EDIT">
                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </state>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BT6-BX-xsC">
                            <rect key="frame" x="0.0" y="0.0" width="70" height="72"/>
                            <connections>
                                <action selector="didTap:" destination="iN0-l3-epB" eventType="touchUpInside" id="saL-1L-DlI"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="dIr-gK-GMT" firstAttribute="top" secondItem="EnK-RL-dIL" secondAttribute="top" id="0Yu-Dc-iGe"/>
                        <constraint firstItem="dIr-gK-GMT" firstAttribute="leading" secondItem="EnK-RL-dIL" secondAttribute="leading" id="55Q-JN-7YK"/>
                        <constraint firstItem="a4y-9W-3GV" firstAttribute="leading" secondItem="EnK-RL-dIL" secondAttribute="leading" id="DXR-tX-Ubf"/>
                        <constraint firstItem="BT6-BX-xsC" firstAttribute="leading" secondItem="EnK-RL-dIL" secondAttribute="leading" id="IZb-g6-aY8"/>
                        <constraint firstAttribute="bottom" secondItem="a4y-9W-3GV" secondAttribute="bottom" constant="-2" id="IbF-tp-67U"/>
                        <constraint firstAttribute="trailing" secondItem="a4y-9W-3GV" secondAttribute="trailing" id="Ilm-Vn-2W8"/>
                        <constraint firstAttribute="width" constant="70" id="OJb-fJ-nM9"/>
                        <constraint firstAttribute="bottom" secondItem="dIr-gK-GMT" secondAttribute="bottom" id="Qek-rC-7F1"/>
                        <constraint firstAttribute="bottom" secondItem="BT6-BX-xsC" secondAttribute="bottom" constant="-2" id="gPN-oV-sGK"/>
                        <constraint firstAttribute="height" constant="70" id="mvW-k9-ci7"/>
                        <constraint firstAttribute="trailing" secondItem="BT6-BX-xsC" secondAttribute="trailing" id="nON-GP-Nhr"/>
                        <constraint firstAttribute="trailing" secondItem="dIr-gK-GMT" secondAttribute="trailing" id="rWM-H7-HH2"/>
                        <constraint firstItem="BT6-BX-xsC" firstAttribute="top" secondItem="EnK-RL-dIL" secondAttribute="top" id="uCf-4P-500"/>
                    </constraints>
                </view>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UZT-Wy-tOa">
                    <rect key="frame" x="230" y="87" width="30" height="30"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="30" id="QHQ-GD-2x3"/>
                        <constraint firstAttribute="height" constant="30" id="nWx-5B-qSf"/>
                    </constraints>
                    <state key="normal" image="color"/>
                    <connections>
                        <action selector="didTap:" destination="iN0-l3-epB" eventType="touchUpInside" id="pdc-k1-5AM"/>
                    </connections>
                </button>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="EnK-RL-dIL" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="1oX-hH-QUq"/>
                <constraint firstItem="UZT-Wy-tOa" firstAttribute="top" secondItem="BT6-BX-xsC" secondAttribute="bottom" constant="-27.5" id="NAH-Su-B3j"/>
                <constraint firstItem="UZT-Wy-tOa" firstAttribute="leading" secondItem="BT6-BX-xsC" secondAttribute="trailing" constant="-12" id="NIR-wf-WMM"/>
                <constraint firstItem="EnK-RL-dIL" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="yhb-JT-Vtw"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="changeColorButton" destination="UZT-Wy-tOa" id="ELU-fQ-dYR"/>
                <outlet property="editButton" destination="BT6-BX-xsC" id="QAN-pk-dhg"/>
                <outlet property="editLabelOnlyButton" destination="a4y-9W-3GV" id="pcQ-E1-bbr"/>
                <outlet property="imageSuperView" destination="EnK-RL-dIL" id="Rix-kv-ldO"/>
                <outlet property="profileImageView" destination="dIr-gK-GMT" id="JMi-fX-YtB"/>
            </connections>
            <point key="canvasLocation" x="-171.01449275362319" y="-244.75446428571428"/>
        </view>
    </objects>
    <resources>
        <image name="Layer 6" width="269" height="309"/>
        <image name="color" width="30" height="30"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
