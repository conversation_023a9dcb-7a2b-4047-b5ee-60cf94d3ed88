//
//  SelectedFilterView.swift
//  GameFlex
//
//  Created by <PERSON> on 7/13/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

let pi: CGFloat = .pi

class SelectedFilterView: UIView {

    // Section array contains the percentages of sections to add colors for, remainder will be gray
    var sectionArray: [(CGFloat, UIColor)] = [(0.0625, .clear), (0.125, .gfGreen), (0.125, .clear), (0.125, .gfGreen), (0.125, .clear), (0.125, .gfGreen), (0.125, .clear), (0.125, .gfGreen), (0.0625, .clear)]

    /// The width of the arc
    var arcWidth: CGFloat = 3

    /// Whether the endpoints of the section are rounded
    var hasRoundedLineCap = false
    var chartCenter: CGPoint { return CGPoint(x: bounds.width/2, y: bounds.height/2) }
    var radius: CGFloat { return max(bounds.width, bounds.height)/2 }

    func fill(sectionPercentsAndColors: [(CGFloat, UIColor)]) {
        sectionArray = sectionPercentsAndColors

        var percentSum: CGFloat = 0
        for (percent, _) in sectionArray {
            percentSum += percent
        }
        if sectionArray.isEmpty {
            sectionArray.append((0.5, UIColor(red: 92/255.0, green: 92/255.0, blue: 92/255.0, alpha: 1)))
            sectionArray.append((0.2, UIColor(red: 131/255.0, green: 131/255.0, blue: 131/255.0, alpha: 1)))
            sectionArray.append((0.18, UIColor(red: 203/255.0, green: 203/255.0, blue: 203/255.0, alpha: 1)))
            sectionArray.append((0.12, UIColor(red: 225/255.0, green: 225/255.0, blue: 225/255.0, alpha: 1)))
        } else if percentSum < 1 {
            sectionArray.append((1-percentSum, UIColor(red: 134/255.0, green: 134/255.0, blue: 134/255.0, alpha: 1)))
        }

        self.setNeedsDisplay()
    }

    override func draw(_ rect: CGRect) {

        var startAngle: CGFloat = 3 * pi / 2
        var endAngle: CGFloat = startAngle

        for (percent, color) in sectionArray {
            startAngle = endAngle
            // Length of section in radians is 2pi*percent
            endAngle = startAngle + 2 * pi * percent

            let path = UIBezierPath(arcCenter: chartCenter,
                                    radius: radius - arcWidth/2,
                                    startAngle: startAngle,
                                    endAngle: endAngle,
                                    clockwise: true)

            path.lineWidth = arcWidth
            path.lineCapStyle = hasRoundedLineCap ? .round : .butt
            color.setStroke()
            path.stroke()
        }
    }
}
