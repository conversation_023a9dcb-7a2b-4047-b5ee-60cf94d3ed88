<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="SideBarView" customModule="GameFlex" customModuleProvider="target">
            <connections>
                <outlet property="commentBacking" destination="hh3-gb-I20" id="JGd-ZY-2DE"/>
                <outlet property="commentButton" destination="6c3-9u-01i" id="HuV-09-Rng"/>
                <outlet property="commentIcon" destination="zGD-Fk-CG5" id="FoY-sh-eWl"/>
                <outlet property="commentLabel" destination="xPJ-Th-9W1" id="Wi9-FJ-xW5"/>
                <outlet property="commentView" destination="wsS-Mu-sKe" id="mnc-uJ-kvD"/>
                <outlet property="likeBacking" destination="7NA-G1-hZU" id="Mzi-gn-Ygy"/>
                <outlet property="likeButton" destination="drC-pK-9DR" id="XJX-za-DZy"/>
                <outlet property="likeIcon" destination="eeF-1F-ycp" id="vmq-Qv-jS7"/>
                <outlet property="likeLabel" destination="WB0-7d-l2R" id="gHd-6S-VBh"/>
                <outlet property="likeView" destination="DpM-ZJ-qAf" id="GUl-3D-Sb8"/>
                <outlet property="playBacking" destination="Dju-eX-Woc" id="cZo-WG-akc"/>
                <outlet property="playButton" destination="ccB-4W-cro" id="0Ky-2k-W47"/>
                <outlet property="playIcon" destination="npI-7F-h6D" id="5HS-OG-Lr4"/>
                <outlet property="playLabel" destination="38I-Kt-25A" id="PPw-mr-Sb2"/>
                <outlet property="playView" destination="dCs-yJ-dYK" id="0vZ-hO-h9U"/>
                <outlet property="reflexBacking" destination="pOr-Hh-3Ze" id="kCS-ez-W5Z"/>
                <outlet property="reflexButton" destination="36y-Nb-VXE" id="hPS-EL-g3P"/>
                <outlet property="reflexIcon" destination="iw0-ha-OPi" id="B2X-d6-Rs7"/>
                <outlet property="reflexLabel" destination="gHg-7U-qJd" id="8gq-8d-k2g"/>
                <outlet property="reflexPresentLight" destination="nCu-gd-Uzu" id="7fv-M7-tul"/>
                <outlet property="reflexView" destination="7Bl-7Z-uKR" id="f5e-Vo-gUU"/>
                <outlet property="shareBacking" destination="Ra6-SI-eId" id="2bi-GL-Nnd"/>
                <outlet property="shareButton" destination="a9O-Yq-zWv" id="xsH-Mz-Mel"/>
                <outlet property="shareIcon" destination="JQa-5U-C0M" id="utF-r7-Fcf"/>
                <outlet property="shareLabel" destination="ey9-b1-wit" id="Inl-kJ-tHA"/>
                <outlet property="shareView" destination="kEF-Rm-Se2" id="a2D-df-Yt7"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="520" height="63"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dCs-yJ-dYK">
                    <rect key="frame" x="80" y="0.0" width="40" height="54"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Dju-eX-Woc">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="bWn-PG-4xP"/>
                                <constraint firstAttribute="width" constant="40" id="jKV-NV-wcs"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="play" translatesAutoresizingMaskIntoConstraints="NO" id="npI-7F-h6D">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="Xd8-HY-ety"/>
                            </constraints>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nCu-gd-Uzu">
                            <rect key="frame" x="34" y="0.0" width="6" height="6"/>
                            <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="6" id="BcG-w2-8Of"/>
                                <constraint firstAttribute="width" constant="6" id="XtZ-KG-bro"/>
                            </constraints>
                        </view>
                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" insetsLayoutMarginsFromSafeArea="NO" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ccB-4W-cro">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="54"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <connections>
                                <action selector="didTapButton:" destination="-1" eventType="touchUpInside" id="QTW-Ki-nMp"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="38I-Kt-25A">
                            <rect key="frame" x="0.0" y="40" width="40" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="4jd-Th-Qf7"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <color key="shadowColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <size key="shadowOffset" width="1" height="1"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="npI-7F-h6D" firstAttribute="leading" secondItem="dCs-yJ-dYK" secondAttribute="leading" id="0PD-UL-bBD"/>
                        <constraint firstAttribute="width" constant="40" id="CeX-XV-kQL"/>
                        <constraint firstAttribute="height" constant="54" id="Gw9-3g-747"/>
                        <constraint firstItem="npI-7F-h6D" firstAttribute="top" secondItem="dCs-yJ-dYK" secondAttribute="top" id="JMa-it-xN1"/>
                        <constraint firstAttribute="trailing" secondItem="nCu-gd-Uzu" secondAttribute="trailing" id="RyM-EJ-Lss"/>
                        <constraint firstItem="Dju-eX-Woc" firstAttribute="top" secondItem="dCs-yJ-dYK" secondAttribute="top" id="U9N-me-x5z"/>
                        <constraint firstAttribute="trailing" secondItem="npI-7F-h6D" secondAttribute="trailing" id="XX2-Qt-7Go"/>
                        <constraint firstItem="38I-Kt-25A" firstAttribute="leading" secondItem="dCs-yJ-dYK" secondAttribute="leading" id="ZOI-ZX-ML4"/>
                        <constraint firstItem="Dju-eX-Woc" firstAttribute="leading" secondItem="dCs-yJ-dYK" secondAttribute="leading" id="jVb-qs-X3c"/>
                        <constraint firstAttribute="bottom" secondItem="38I-Kt-25A" secondAttribute="bottom" id="oPT-Ls-i9u"/>
                        <constraint firstAttribute="trailing" secondItem="38I-Kt-25A" secondAttribute="trailing" id="p6e-kF-l3Y"/>
                        <constraint firstItem="nCu-gd-Uzu" firstAttribute="top" secondItem="dCs-yJ-dYK" secondAttribute="top" id="y7r-yI-EqR"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kEF-Rm-Se2">
                    <rect key="frame" x="20" y="0.0" width="40" height="54"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ra6-SI-eId">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="40" id="Mfd-xF-WxH"/>
                                <constraint firstAttribute="height" constant="40" id="XOZ-eN-NtD"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dots" translatesAutoresizingMaskIntoConstraints="NO" id="JQa-5U-C0M">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="Dzw-Fq-5n1"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="a9O-Yq-zWv">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="54"/>
                            <connections>
                                <action selector="didTapButton:" destination="-1" eventType="touchUpInside" id="zj6-tm-zDw"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ey9-b1-wit">
                            <rect key="frame" x="0.0" y="40" width="40" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="dER-iE-1tN"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <color key="shadowColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <size key="shadowOffset" width="1" height="1"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="JQa-5U-C0M" firstAttribute="top" secondItem="kEF-Rm-Se2" secondAttribute="top" id="BgF-kN-G4v"/>
                        <constraint firstItem="a9O-Yq-zWv" firstAttribute="leading" secondItem="kEF-Rm-Se2" secondAttribute="leading" id="IAQ-8j-z5u"/>
                        <constraint firstItem="a9O-Yq-zWv" firstAttribute="top" secondItem="kEF-Rm-Se2" secondAttribute="top" id="Jic-Tk-xMR"/>
                        <constraint firstAttribute="trailing" secondItem="a9O-Yq-zWv" secondAttribute="trailing" id="KfB-Kb-QhU"/>
                        <constraint firstAttribute="bottom" secondItem="ey9-b1-wit" secondAttribute="bottom" id="MXe-hA-RIP"/>
                        <constraint firstAttribute="trailing" secondItem="ey9-b1-wit" secondAttribute="trailing" id="Nm3-Yr-hhL"/>
                        <constraint firstItem="JQa-5U-C0M" firstAttribute="leading" secondItem="kEF-Rm-Se2" secondAttribute="leading" id="Tuu-jf-dDa"/>
                        <constraint firstItem="Ra6-SI-eId" firstAttribute="leading" secondItem="kEF-Rm-Se2" secondAttribute="leading" id="XS2-qQ-9hq"/>
                        <constraint firstAttribute="width" constant="40" id="YXC-1i-KH0"/>
                        <constraint firstAttribute="bottom" secondItem="a9O-Yq-zWv" secondAttribute="bottom" id="ZB8-TO-gi5"/>
                        <constraint firstItem="Ra6-SI-eId" firstAttribute="top" secondItem="kEF-Rm-Se2" secondAttribute="top" id="jgZ-FV-u3i"/>
                        <constraint firstAttribute="trailing" secondItem="JQa-5U-C0M" secondAttribute="trailing" id="l0u-Xe-nfy"/>
                        <constraint firstItem="ey9-b1-wit" firstAttribute="leading" secondItem="kEF-Rm-Se2" secondAttribute="leading" id="nW3-sk-kc6"/>
                        <constraint firstAttribute="height" constant="54" id="pgQ-Mm-Do3"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wsS-Mu-sKe">
                    <rect key="frame" x="340" y="0.0" width="40" height="54"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hh3-gb-I20">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="40" id="cuI-o3-3GI"/>
                                <constraint firstAttribute="height" constant="40" id="eyo-61-acY"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="comment" highlightedImage="commentSelected" translatesAutoresizingMaskIntoConstraints="NO" id="zGD-Fk-CG5">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="jHb-vL-FM6"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6c3-9u-01i">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="54"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <connections>
                                <action selector="didTapButton:" destination="-1" eventType="touchUpInside" id="vzD-EQ-Imv"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumFontSize="5" translatesAutoresizingMaskIntoConstraints="NO" id="xPJ-Th-9W1">
                            <rect key="frame" x="0.0" y="40" width="40" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="QYY-dv-l29"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <color key="shadowColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <size key="shadowOffset" width="1" height="1"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="trailing" secondItem="zGD-Fk-CG5" secondAttribute="trailing" id="0ym-UY-hEX"/>
                        <constraint firstItem="xPJ-Th-9W1" firstAttribute="leading" secondItem="wsS-Mu-sKe" secondAttribute="leading" id="74b-jz-hfU"/>
                        <constraint firstAttribute="bottom" secondItem="xPJ-Th-9W1" secondAttribute="bottom" id="Ae6-oE-b3F"/>
                        <constraint firstItem="hh3-gb-I20" firstAttribute="top" secondItem="wsS-Mu-sKe" secondAttribute="top" id="BiI-Ei-aZp"/>
                        <constraint firstItem="zGD-Fk-CG5" firstAttribute="top" secondItem="wsS-Mu-sKe" secondAttribute="top" id="SQ0-f1-k8A"/>
                        <constraint firstItem="zGD-Fk-CG5" firstAttribute="leading" secondItem="wsS-Mu-sKe" secondAttribute="leading" id="YEc-8b-2jb"/>
                        <constraint firstAttribute="height" constant="54" id="aP8-kt-Lcp"/>
                        <constraint firstItem="hh3-gb-I20" firstAttribute="leading" secondItem="wsS-Mu-sKe" secondAttribute="leading" id="dih-cN-MTl"/>
                        <constraint firstAttribute="width" constant="40" id="m6B-6P-UXB"/>
                        <constraint firstAttribute="trailing" secondItem="xPJ-Th-9W1" secondAttribute="trailing" id="oQZ-rd-1xT"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DpM-ZJ-qAf">
                    <rect key="frame" x="400" y="0.0" width="40" height="54"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7NA-G1-hZU">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="D6K-Aq-xw4"/>
                                <constraint firstAttribute="width" constant="40" id="nEz-BJ-iwL"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="like" highlightedImage="likeSelected" translatesAutoresizingMaskIntoConstraints="NO" id="eeF-1F-ycp">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="Ppm-CO-uNv"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="drC-pK-9DR">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="54"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <connections>
                                <action selector="didTapButton:" destination="-1" eventType="touchUpInside" id="6Yl-cs-oky"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WB0-7d-l2R">
                            <rect key="frame" x="0.0" y="40" width="40" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="0pb-Wg-gDI"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <color key="shadowColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <size key="shadowOffset" width="1" height="1"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="7NA-G1-hZU" firstAttribute="top" secondItem="DpM-ZJ-qAf" secondAttribute="top" id="5XF-ex-he9"/>
                        <constraint firstAttribute="bottom" secondItem="WB0-7d-l2R" secondAttribute="bottom" id="H9T-t6-d6r"/>
                        <constraint firstAttribute="width" constant="40" id="J3N-Wi-GE5"/>
                        <constraint firstAttribute="trailing" secondItem="WB0-7d-l2R" secondAttribute="trailing" id="JIv-nx-kG1"/>
                        <constraint firstItem="eeF-1F-ycp" firstAttribute="leading" secondItem="DpM-ZJ-qAf" secondAttribute="leading" id="Je9-eA-8aW"/>
                        <constraint firstItem="7NA-G1-hZU" firstAttribute="leading" secondItem="DpM-ZJ-qAf" secondAttribute="leading" id="aje-Dh-api"/>
                        <constraint firstItem="WB0-7d-l2R" firstAttribute="leading" secondItem="DpM-ZJ-qAf" secondAttribute="leading" id="b6M-xS-52O"/>
                        <constraint firstItem="eeF-1F-ycp" firstAttribute="top" secondItem="DpM-ZJ-qAf" secondAttribute="top" id="dhs-r4-m1E"/>
                        <constraint firstAttribute="height" constant="54" id="v9w-0O-aBe"/>
                        <constraint firstAttribute="trailing" secondItem="eeF-1F-ycp" secondAttribute="trailing" id="wu3-oI-mXR"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7Bl-7Z-uKR">
                    <rect key="frame" x="460" y="0.0" width="40" height="54"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pOr-Hh-3Ze">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="GrP-1D-diq"/>
                                <constraint firstAttribute="width" constant="40" id="knJ-Zg-TvF"/>
                            </constraints>
                        </view>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="center" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="reflex" highlightedImage="reflexSelected" translatesAutoresizingMaskIntoConstraints="NO" id="iw0-ha-OPi">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="40" id="kzW-1r-uAb"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" fixedFrame="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="36y-Nb-VXE">
                            <rect key="frame" x="0.0" y="0.0" width="40" height="54"/>
                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                            <connections>
                                <action selector="didTapButton:" destination="-1" eventType="touchUpInside" id="mkM-nL-Xe6"/>
                            </connections>
                        </button>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gHg-7U-qJd">
                            <rect key="frame" x="0.0" y="40" width="40" height="14"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="14" id="YvZ-h7-yAv"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="9"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                            <color key="shadowColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <size key="shadowOffset" width="1" height="1"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="pOr-Hh-3Ze" firstAttribute="leading" secondItem="7Bl-7Z-uKR" secondAttribute="leading" id="57g-1g-jt4"/>
                        <constraint firstAttribute="trailing" secondItem="iw0-ha-OPi" secondAttribute="trailing" id="5mW-ZA-VTN"/>
                        <constraint firstItem="gHg-7U-qJd" firstAttribute="leading" secondItem="7Bl-7Z-uKR" secondAttribute="leading" id="663-QC-gH8"/>
                        <constraint firstItem="pOr-Hh-3Ze" firstAttribute="top" secondItem="7Bl-7Z-uKR" secondAttribute="top" id="9re-Pi-1YM"/>
                        <constraint firstAttribute="bottom" secondItem="gHg-7U-qJd" secondAttribute="bottom" id="Ka4-A2-X0G"/>
                        <constraint firstItem="iw0-ha-OPi" firstAttribute="top" secondItem="7Bl-7Z-uKR" secondAttribute="top" id="VTh-A8-VFQ"/>
                        <constraint firstAttribute="height" constant="54" id="Vqp-Kr-J7p"/>
                        <constraint firstAttribute="width" constant="40" id="bc7-ko-HgY"/>
                        <constraint firstAttribute="trailing" secondItem="gHg-7U-qJd" secondAttribute="trailing" id="cVZ-hF-JfM"/>
                        <constraint firstItem="iw0-ha-OPi" firstAttribute="leading" secondItem="7Bl-7Z-uKR" secondAttribute="leading" id="hVP-HJ-rH7"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="DpM-ZJ-qAf" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="0vu-6j-QlW"/>
                <constraint firstAttribute="trailing" secondItem="wsS-Mu-sKe" secondAttribute="trailing" constant="140" id="7yd-xu-vIG"/>
                <constraint firstItem="kEF-Rm-Se2" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="R0V-qC-ltv"/>
                <constraint firstItem="7Bl-7Z-uKR" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="SWP-NM-2QK"/>
                <constraint firstAttribute="trailing" secondItem="7Bl-7Z-uKR" secondAttribute="trailing" constant="20" id="VIj-R3-Wuc"/>
                <constraint firstItem="dCs-yJ-dYK" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="80" id="nIq-JN-Thq"/>
                <constraint firstItem="kEF-Rm-Se2" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" id="rTc-Dr-uq6"/>
                <constraint firstAttribute="trailing" secondItem="DpM-ZJ-qAf" secondAttribute="trailing" constant="80" id="wIX-jq-tC4"/>
                <constraint firstItem="wsS-Mu-sKe" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="wVs-s5-BaD"/>
                <constraint firstItem="dCs-yJ-dYK" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="xya-gV-LgK"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="311.59420289855075" y="-329.79910714285711"/>
        </view>
    </objects>
    <resources>
        <image name="comment" width="25" height="24"/>
        <image name="commentSelected" width="25" height="24"/>
        <image name="dots" width="5" height="23"/>
        <image name="like" width="22.5" height="23"/>
        <image name="likeSelected" width="22.5" height="23"/>
        <image name="play" width="11.5" height="15.5"/>
        <image name="reflex" width="26" height="23"/>
        <image name="reflexSelected" width="26" height="23"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
