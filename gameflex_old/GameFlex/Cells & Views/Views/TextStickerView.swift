//
//  TextStickerView.swift
//  GameFlex
//
//  Created by <PERSON> on 7/24/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//
// This is a subclass of StickerView. The textStickerView UITextView is for boxes of text.

import UIKit

class TextStickerView: StickerView {
    
    @IBOutlet weak var textStickerView: UITextView!
        
    override init(frame: CGRect) {
        super.init(frame: frame)
        let nib = UINib(nibName: String(describing: type(of: self)), bundle: Bundle(for: type(of: self)))
        let view = nib.instantiate(withOwner: self, options: nil).first as! UIView
        view.frame = frame
        addSubview(view)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func setupView(_ stickerType: StickerType) {
        viewType = .text
        textStickerView.isScrollEnabled = false
        textStickerView.keyboardAppearance = .dark
        textStickerView.returnKeyType = .default
        textStickerView.autocorrectionType = .no
        textStickerView.layer.cornerRadius = 4
    }
    
}

