<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FlareHeaderView" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bar" translatesAutoresizingMaskIntoConstraints="NO" id="KIT-lQ-CfO">
                    <rect key="frame" x="47" y="12" width="2" height="20"/>
                </imageView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="6e3-lf-0jZ">
                    <rect key="frame" x="15" y="10" width="18" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="fZG-Ym-rmp"/>
                        <constraint firstAttribute="width" constant="18" id="pxU-zb-cXi"/>
                    </constraints>
                    <state key="normal" image="homeSelected"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="BXV-rZ-zqz"/>
                    </connections>
                </button>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gOf-HZ-bjk">
                    <rect key="frame" x="65" y="9.5" width="14" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="kO9-4w-KNR"/>
                        <constraint firstAttribute="width" constant="14" id="vRO-Os-16f"/>
                    </constraints>
                    <state key="normal" image="favs"/>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="a9x-tR-9Gy"/>
                    </connections>
                </button>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="favsSelectedBar" translatesAutoresizingMaskIntoConstraints="NO" id="vgs-B8-6MB">
                    <rect key="frame" x="65" y="32.5" width="14" height="2"/>
                </imageView>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" prefetchingEnabled="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JkD-lS-jRQ">
                    <rect key="frame" x="89" y="0.0" width="325" height="44"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="98R-qD-jiw">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                </collectionView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="JkD-lS-jRQ" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="05R-O8-luT"/>
                <constraint firstItem="JkD-lS-jRQ" firstAttribute="leading" secondItem="gOf-HZ-bjk" secondAttribute="trailing" constant="10" id="CTP-O9-8rg"/>
                <constraint firstItem="6e3-lf-0jZ" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="FQZ-6y-Hpb"/>
                <constraint firstAttribute="trailing" secondItem="JkD-lS-jRQ" secondAttribute="trailing" id="TJ6-ns-gx8"/>
                <constraint firstItem="vgs-B8-6MB" firstAttribute="top" secondItem="gOf-HZ-bjk" secondAttribute="bottom" constant="1" id="aVd-4C-kyW"/>
                <constraint firstItem="KIT-lQ-CfO" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="12" id="bM5-sp-zQ4"/>
                <constraint firstItem="vgs-B8-6MB" firstAttribute="centerX" secondItem="gOf-HZ-bjk" secondAttribute="centerX" id="bfG-60-zby"/>
                <constraint firstItem="gOf-HZ-bjk" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="9.5" id="e0D-3q-qyA"/>
                <constraint firstItem="gOf-HZ-bjk" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="65" id="mck-ov-eyc"/>
                <constraint firstItem="KIT-lQ-CfO" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="47" id="oH9-Sv-dwv"/>
                <constraint firstItem="6e3-lf-0jZ" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="15" id="smF-Yt-PX6"/>
                <constraint firstAttribute="bottom" secondItem="JkD-lS-jRQ" secondAttribute="bottom" id="ttl-Sh-l98"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="collectionView" destination="JkD-lS-jRQ" id="Tqr-KR-aQW"/>
                <outlet property="favsButton" destination="gOf-HZ-bjk" id="yvf-4j-mRq"/>
                <outlet property="favsUnderImageView" destination="vgs-B8-6MB" id="W1F-Lh-kPv"/>
                <outlet property="headButton" destination="6e3-lf-0jZ" id="Syb-Dr-MDL"/>
            </connections>
            <point key="canvasLocation" x="9067" y="137"/>
        </view>
    </objects>
    <resources>
        <image name="bar" width="2" height="20"/>
        <image name="favs" width="14" height="18"/>
        <image name="favsSelectedBar" width="14" height="2"/>
        <image name="homeSelected" width="18" height="18"/>
    </resources>
</document>
