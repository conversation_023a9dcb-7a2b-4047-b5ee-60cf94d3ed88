//
//  DrawView.swift
//  GameFlex
//
//  Created by <PERSON> on 9/5/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class DrawView: UIView {
    
    var expectedLineWidth: CGFloat = 1.0
    var lineWidth: CGFloat = 1.0
    var strokeColor = UIColor.white.cgColor
    var opacity: CGFloat = 1.0

    private var lineArray: [[CGPoint]] = [[CGPoint]]()
    
    weak var delegate: CameraDelegate?
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        delegate?.didStartDrawing()
        self.backgroundColor = .clear
        guard let touch = touches.first else { return }
        let firstPoint = touch.location(in: self)
        lineArray.append([CGPoint]())
        lineArray[lineArray.count - 1].append(firstPoint)
        lineWidth = expectedLineWidth
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let currentPoint = touch.location(in: self)
        lineArray[lineArray.count - 1].append(currentPoint)
        setNeedsDisplay()
        // forceTouch
        if traitCollection.forceTouchCapability == .available {
            lineWidth = touches.first?.force ?? 1.0 < 2.0 ? expectedLineWidth : 2 * expectedLineWidth * (touches.first?.force ?? 1.0) / (touches.first?.maximumPossibleForce ?? 1.0)
            if touches.first?.force == touches.first?.maximumPossibleForce {
                print("max = \(touches.first?.maximumPossibleForce ?? 0.0)")
            }
        }

    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let image = exportDrawing() {
            let iv = UIImageView(frame: self.frame)
            iv.backgroundColor = .clear
            iv.image = image
            iv.tag = CameraViewModel.drawingsTag
            iv.alpha = opacity
            CameraViewModel.drawings.append(iv)
            delegate?.updateDrawingSubviews()
            resetDrawing()
            delegate?.didEndDrawing()
        }
    }
    
    
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        draw(inContext: context)
    }
    
    func draw(inContext context: CGContext) {
        context.setLineWidth(lineWidth)
        let stroke = UIColor(cgColor: strokeColor).withAlphaComponent(opacity).cgColor
        context.setStrokeColor(stroke)
        context.setLineCap(.round)
        
        for line in lineArray {
            guard let firstPoint = line.first else { continue }
            context.beginPath()
            context.move(to: firstPoint)
            
            for point in line.dropFirst() {
                context.addLine(to: point)
            }
            context.strokePath()
        }
    }
    
    func resetDrawing() {
        lineArray = []
        setNeedsDisplay()
    }
    
    func exportDrawing() -> UIImage? {
        UIGraphicsBeginImageContext(frame.size)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        draw(inContext: context)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
    
    func removeLastDrawing() {
        CameraViewModel.drawings.removeLast()
        delegate?.updateDrawingSubviews()
    }
    
}
