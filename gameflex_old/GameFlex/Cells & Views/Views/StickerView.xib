<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="StickerView" customModule="GameFlex" customModuleProvider="target">
            <connections>
                <outlet property="imageView" destination="oBM-pk-eXP" id="sOx-rZ-i5l"/>
                <outlet property="textView" destination="9tC-Cd-tER" id="LdP-Rq-lax"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="414" height="96"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="oBM-pk-eXP">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="96"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </imageView>
                <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9tC-Cd-tER">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="96"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <color key="textColor" systemColor="labelColor"/>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="words" autocorrectionType="yes" returnKeyType="done"/>
                </textView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="oBM-pk-eXP" secondAttribute="trailing" id="09P-wI-TY2"/>
                <constraint firstAttribute="trailing" secondItem="9tC-Cd-tER" secondAttribute="trailing" id="1xe-aW-2Zt"/>
                <constraint firstAttribute="bottom" secondItem="9tC-Cd-tER" secondAttribute="bottom" id="2If-Hj-KWV"/>
                <constraint firstItem="9tC-Cd-tER" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="ANS-Kg-fTG"/>
                <constraint firstItem="oBM-pk-eXP" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="J1q-pk-RaN"/>
                <constraint firstAttribute="bottom" secondItem="oBM-pk-eXP" secondAttribute="bottom" id="cal-xp-3cZ"/>
                <constraint firstItem="9tC-Cd-tER" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="dtL-Ch-gpx"/>
                <constraint firstItem="oBM-pk-eXP" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="fD5-0m-A0r"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <point key="canvasLocation" x="128.98550724637681" y="83.035714285714278"/>
        </view>
    </objects>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
