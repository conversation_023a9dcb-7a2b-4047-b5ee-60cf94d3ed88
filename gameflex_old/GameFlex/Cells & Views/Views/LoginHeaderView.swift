//
//  LoginHeaderView.swift
//  GameFlex
//
//  Created by <PERSON> on 8/23/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class LoginHeaderView: UIView {
    
    @IBOutlet weak var logoImageView: UIImageView!
    @IBOutlet weak var logoLabel: UIImageView!
    
    static var viewIdentifier = String(describing: LoginHeaderView.self)
    
    // MARK: - Lifecycle

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }

    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    func setupView() {
        backgroundColor = .clear
    }

}


