//
//  FilterView.swift
//  GameFlex
//
//  Created by <PERSON> on 7/30/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import UIKit
import CoreImage

// https://developer.apple.com/library/archive/documentation/GraphicsImaging/Reference/CoreImageFilterReference/index.html#//apple_ref/doc/uid/TP40004346
enum FilterType {
    case normal,
    binary /* CIPhotoEffectMono */,
//    circular /* CICircularScreen */,
    boomer /* CIPhotoEffectInstant */,
    chromo /* CIPhotoEffectChrome */,
    alpha /* CIMaskToAlpha */,
    mono /* CISepiaTone */,
    schmuag,
    vibes,
    boost,
    flat,
    dank,
    crank,
    matrix,
    pow
//    sharpen /* CISharpenLuminance */,
//    unsharpenMask /* CIUnsharpMask */,
//    vortex /* CIVortexDistortion */
}

enum SliderFilterType {
    case contrast, brightness, temperature, saturation, shadowRemover
}

/* CICrop crops the image */
/* CIStarShineGenerator - starburst flare */

class FilterView: UIView {
    
    @IBOutlet weak var imageView: UIImageView!
    static var context: CIContext = FlexManager.shared.context
    
    static var viewIdentifier = String(describing: FilterView.self)
    
    static func applyFilter(type: FilterType, image: UIImage, option: Any?) -> UIImage {
        let imageOrientation = image.imageOrientation
        var filteredImage: CIImage? =  CIImage(image: image)

        if filteredImage != nil {
            switch type {
                
            case .normal: break
            case .binary:
                filteredImage = applyBinaryFilter(filteredImage!)
            case .boomer:
                filteredImage = applyBoomerFilter(filteredImage!)
            case .chromo:
                filteredImage = applyChromoFilter(filteredImage!)
            case .alpha:
                filteredImage = applyAlphaFilter(filteredImage!)
            case .mono:
                filteredImage = applyMonoFilter(filteredImage!)
            case .schmuag:
                filteredImage = applySchmuagFilter(filteredImage!)
            case .vibes:
                filteredImage = applyVibesFilter(filteredImage!)
            case .boost:
                filteredImage = applyBoostFilter(filteredImage!)
            case .flat:
                filteredImage = applyFlatFilter(filteredImage!)
            case .dank:
                filteredImage = applyDankFilter(filteredImage!)
            case .crank:
                filteredImage = applyCrankFilter(filteredImage!)
            case .matrix:
                filteredImage = applyMatrixFilter(filteredImage!)
            case .pow:
                filteredImage = applyCrankFilter(filteredImage!)
            }
            if filteredImage != nil {
                let cgImage = context.createCGImage(filteredImage!, from: filteredImage!.extent)
                if cgImage != nil {
                    return UIImage(cgImage: cgImage!, scale: 1.0, orientation: imageOrientation)
                }
            }
        }
        return image
    }
    
    static func applyMatrixFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIColorMatrix")
        filter?.setValue(CIVector(x: 0.85, y: 0, z: 0, w: 0), forKey: "inputRVector")
        filter?.setValue(CIVector(x: 0, y: 0, z: 0, w: 0), forKey: "inputGVector")
        filter?.setValue(CIVector(x: 0, y: 0, z: 0, w: 0), forKey: "inputBVector")
        filter?.setValue(CIVector(x: 0, y: 0, z: 0, w: 0.5), forKey: "inputAVector")
        return filter?.outputImage
    }
    
    static func applyCrankFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIColorControls")
        filter?.setValue(0.5, forKey: kCIInputSaturationKey)
        filter?.setValue(0.05, forKey: kCIInputBrightnessKey)
        filter?.setValue(0.90, forKey: kCIInputContrastKey)
        return filter?.outputImage
    }
    
    static func applyDankFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIGammaAdjust")
        filter?.setValue(0.05, forKey: "inputPower")
        return filter?.outputImage
    }
    
    static func applyBinaryFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIPhotoEffectMono")
        filter?.setValue(input, forKey: kCIInputImageKey)
        return filter?.outputImage
    }
    
    static func applyBoomerFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIPhotoEffectInstant")
        filter?.setValue(input, forKey: kCIInputImageKey)
        return filter?.outputImage
    }
    
    static func applyChromoFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIPhotoEffectChrome")
        filter?.setValue(input, forKey: kCIInputImageKey)
        return filter?.outputImage
    }
    
    static func applyAlphaFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIMaskToAlpha")
        filter?.setValue(input, forKey: kCIInputImageKey)
        return filter?.outputImage
    }
    
    static func applyMonoFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name:"CIColorMonochrome")
        filter?.setValue(input, forKey: kCIInputImageKey)
        filter?.setValue(0.5, forKey: kCIInputIntensityKey)
        filter?.setValue(CIColor(color: UIColor.red), forKey: kCIInputColorKey)
        return filter?.outputImage
    }

    static func applySchmuagFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIWhitePointAdjust")
        filter?.setValue(input, forKey: kCIInputImageKey)
        filter?.setValue(CIColor(color: UIColor.brown), forKey: kCIInputColorKey)
        return filter?.outputImage
    }
    
    static func applyVibesFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIVibrance")
        filter?.setValue(0.85, forKey: "inputAmount")
        return filter?.outputImage
    }
    
    static func applyBoostFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CITemperatureAndTint")
        let ciVector = CIVector(x: CGFloat(6500.0 * 2.0), y: 0.0)
        filter?.setValue(input, forKey: kCIInputImageKey)
        filter?.setValue(ciVector, forKey: "inputNeutral")
        filter?.setValue(CIVector(x: 6500.0, y: 100.0), forKey: "inputTargetNeutral")
        return filter?.outputImage
    }
    
    static func applyFlatFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIHueAdjust")
        filter?.setValue(input, forKey: kCIInputImageKey)
        filter?.setValue(Double.pi/2, forKey: kCIInputAngleKey)
        return filter?.outputImage
    }

    static func applyUnsharpenFilter(_ input: CIImage) -> CIImage? {
        let filter = CIFilter(name: "CIUnsharpMask")
        filter?.setValue(input, forKey: kCIInputImageKey)
        // consumes default values for inputRadius of 2.5 and inputIntensity of 0.5
        return filter?.outputImage
    }
    
    // MARK: - Slider-based Edits
    
    static func applyEditFilter(image: UIImage, filter: SliderFilterType, slider: UISlider) -> UIImage {
        let imageOrientation = image.imageOrientation
        
        var filteredImage: CIImage? =  CIImage(image: image)

        if filteredImage != nil {
            switch filter {
                
            case .contrast:
                let filter = CIFilter(name: "CIColorControls")
                filter?.setValue(filteredImage, forKey: kCIInputImageKey)
                filter?.setValue(slider.value + 0.5, forKey: kCIInputContrastKey)
                filteredImage = filter?.outputImage
            case .brightness:
                let filter = CIFilter(name: "CIColorControls")
                filter?.setValue(filteredImage, forKey: kCIInputImageKey)
                filter?.setValue(slider.value, forKey: kCIInputBrightnessKey)
                filteredImage = filter?.outputImage
            case .temperature:
                let filter = CIFilter(name: "CITemperatureAndTint")
                let ciVector = CIVector(x: CGFloat(6500.0 * slider.value), y: 0.0)
                filter?.setValue(filteredImage, forKey: kCIInputImageKey)
                filter?.setValue(ciVector, forKey: "inputNeutral")
                filter?.setValue(CIVector(x: 6500.0, y: 0.0), forKey: "inputTargetNeutral")
                filteredImage = filter?.outputImage
            case .saturation:
                let filter = CIFilter(name: "CIColorControls")
                filter?.setValue(filteredImage, forKey: kCIInputImageKey)
                filter?.setValue(slider.value, forKey: kCIInputSaturationKey)
                filteredImage = filter?.outputImage
            case .shadowRemover:
                let filter = CIFilter(name: "CIHighlightShadowAdjust")
                filter?.setValue(filteredImage, forKey: kCIInputImageKey)
                filter?.setValue(slider.value, forKey: "inputHighlightAmount")
                filter?.setValue(slider.value, forKey: "inputShadowAmount")
                filteredImage = filter?.outputImage

            }
            if filteredImage != nil {
                let cgImage = context.createCGImage(filteredImage!, from: filteredImage!.extent)
                if cgImage != nil {
                    return UIImage(cgImage: cgImage!, scale: 1.0, orientation: imageOrientation)
                }
            }
        }
        return image

    }
}
