//
//  GFSproketView.swift
//  navTest
//
//  Created by <PERSON> on 10/4/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class GFSproketView: UIView {
    @IBOutlet weak var contentView: UIView!
    @IBOutlet weak var imageView: UIImageView!
    
    let imageArray: [UIImage] = /*[#imageLiteral(resourceName: "GFL_Loading_Anim_2_100.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_101.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_102.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_103.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_104.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_105.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_106.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_107.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_108.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_109.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_110.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_111.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_112.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_113.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_114.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_115.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_116.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_117.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_118.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_119.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_120.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_121.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_122.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_123.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_124.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_125.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_126.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_127.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_128.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_129.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_2_130.png")]
//    let imageArray/*array4_2*/: [UIImage] =*/ [#imageLiteral(resourceName: "GFL_Loading_Anim_4_200.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_201.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_202.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_203.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_204.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_205.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_206.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_207.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_208.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_209.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_210.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_211.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_212.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_213.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_214.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_215.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_216.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_217.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_218.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_219.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_220.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_221.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_222.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_223.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_224.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_225.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_226.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_227.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_228.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_229.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_230.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_231.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_232.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_233.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_234.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_235.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_236.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_237.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_238.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_239.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_240.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_241.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_242.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_243.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_244.png"), #imageLiteral(resourceName: "GFL_Loading_Anim_4_245.png")]
    var index = 0
    var stopped = false
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        configureView()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        configureView()
    }
    
    func configureView() {
        let nib = UINib(nibName: String(describing: GFSproketView.self), bundle: nil)
        nib.instantiate(withOwner: self, options: nil)
        addSubview(contentView)
        contentView.frame = self.bounds
        contentView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        contentView.backgroundColor = .clear
        index = 0
        stopped = false
        showImages()
    }
    
    func showImages() {
        if !stopped {
            if index == imageArray.count { index = 0}
            imageView.image = imageArray[index]
            index += 1
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.045) {
                self.showImages()
            }
        } else {
            stopped = false
        }
    }

}
