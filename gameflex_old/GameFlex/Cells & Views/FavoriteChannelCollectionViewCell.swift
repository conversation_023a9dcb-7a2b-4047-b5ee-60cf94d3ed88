//
//  FavoriteChannelCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/21/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class FavoriteChannelCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var profileSuperView: UIView!
    @IBOutlet weak var profileImage: UIImageView!
    @IBOutlet weak var channelTitleLabel: UILabel!
    
    var channel: Channel?
    
    static let cellIdentifier = String(describing: FavoriteChannelCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        configureCell()
    }
    
    func configureCell() {
        if channel != nil {
            let image = UIImage(named: "layer 6")
            channelTitleLabel.text = "\(channel?.channelName ?? "")"
            // placeholder smilie
            profileImage.image = image
            if let pic = channel?.channelImage, pic != "" {
                let url = URL(string: pic)
                let processor = DownsamplingImageProcessor(size: profileImage.bounds.size)
                profileImage.kf.indicatorType = .activity
                profileImage.kf.setImage(
                    with: url,
                    placeholder: image, //FlexManager.randomImagePlaceholder()),
                    options: [
                        .processor(processor),
                        .scaleFactor(UIScreen.main.scale),
                        .transition(.fade(1)),
                        .cacheOriginalImage
                    ], completionHandler:
                        {
                            result in
                            switch result {
                            case .success:
                                break
                            case .failure(let error):
                                print("Job failed: \(error.localizedDescription)")
                                self.profileImage.image = image
                            }
                        })
            }
        }
        profileSuperView.layer.borderWidth = 1
        profileSuperView.layer.borderColor = UIColor.gfBlue_00D5FF.cgColor
        profileSuperView.clipsToBounds = true
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
    }
}
