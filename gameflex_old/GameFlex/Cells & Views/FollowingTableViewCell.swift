//
//  FollowingTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 11/7/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class FollowingTableViewCell: UITableViewCell {
    
    @IBOutlet weak var profileImage: UIImageView!
    @IBOutlet weak var profileSuperView: UIView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var subTitleLabel: UILabel!
    @IBOutlet weak var profileButton: UIButton!
    @IBOutlet weak var followButton: UIButton!
    
    var flexterId: String?
    
    weak var delegate: ProfileDelegate?
    weak var homeDelegate: HomeDelegate?

    static var cellIdentifier = String(describing: FollowingTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        profileImage.layer.cornerRadius = profileImage.frame.size.height/2
        subTitleLabel.text = ""
   }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        profileImage.image = nil
        titleLabel.text = ""
    }
    
    func configureCell(following: Bool = false, channel: Channel?) {
        if let channel = channel {
            titleLabel.text = channel.channelName
            profileSuperView.layer.cornerRadius = profileSuperView.frame.size.width/2
            profileSuperView.layer.borderColor = UIColor.gfYellow_F2DE76.cgColor
            profileSuperView.layer.borderWidth  = 1.0
            flexterId = channel.channelId
            Utilities.configure(followButton: followButton, following: following)
            if channel.channelId == User.flexter.userId {
                followButton.isHidden = true
            } else {
                followButton.isHidden = false
            }
        }
    }
    
    @IBAction func didTapButton(_ sender: UIButton) {
        if sender == followButton {
            homeDelegate?.didTapToFollow(followButton, self)
        } else if let fid = flexterId {
            delegate?.didTapForProfileAction(.goToProfile, fid)
        }
    }
}
