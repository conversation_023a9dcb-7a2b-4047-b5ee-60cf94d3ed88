<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17701"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="CounterCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="20" height="5"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="cky-lw-KEH">
                    <rect key="frame" x="1" y="4" width="18" height="1"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BKy-yi-Nl7">
                    <rect key="frame" x="1" y="2" width="0.0" height="3"/>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="width" id="QDx-Ya-IJQ"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="BKy-yi-Nl7" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="1" id="8VZ-xM-z6y"/>
                <constraint firstAttribute="trailing" secondItem="cky-lw-KEH" secondAttribute="trailing" constant="1" id="Qec-g4-1l5"/>
                <constraint firstItem="cky-lw-KEH" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="fpu-dI-pSH"/>
                <constraint firstAttribute="bottom" secondItem="cky-lw-KEH" secondAttribute="bottom" id="pBw-Do-Fbj"/>
                <constraint firstAttribute="bottom" secondItem="BKy-yi-Nl7" secondAttribute="bottom" id="w3N-Kg-FO7"/>
                <constraint firstItem="cky-lw-KEH" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="1" id="w5V-mJ-QFN"/>
                <constraint firstItem="BKy-yi-Nl7" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="2" id="xAG-ps-2cW"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="thickLine" destination="BKy-yi-Nl7" id="gqN-PF-Fw4"/>
                <outlet property="thickLineWidthConstraint" destination="QDx-Ya-IJQ" id="2T4-jG-Ebj"/>
                <outlet property="thinLine" destination="cky-lw-KEH" id="O6G-k1-AfH"/>
            </connections>
            <point key="canvasLocation" x="-36.231884057971016" y="116.85267857142857"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
