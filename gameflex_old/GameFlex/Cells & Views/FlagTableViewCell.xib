<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="FlagTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="83"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6BV-BB-lnx">
                    <rect key="frame" x="414" y="42" width="70" height="34"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="commentFlag" translatesAutoresizingMaskIntoConstraints="NO" id="GyT-0b-5A5">
                            <rect key="frame" x="8" y="9" width="19" height="16"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="19" id="XAO-wX-VhG"/>
                                <constraint firstAttribute="height" constant="16" id="eZj-Ol-FAV"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="21" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="AjK-EH-6BQ">
                            <rect key="frame" x="36.5" y="6.5" width="17.5" height="21"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="21" id="IRs-8n-mQS"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="70" id="1C1-CZ-esJ"/>
                        <constraint firstAttribute="trailing" secondItem="AjK-EH-6BQ" secondAttribute="trailing" constant="16" id="EuH-cq-xPt"/>
                        <constraint firstItem="AjK-EH-6BQ" firstAttribute="centerY" secondItem="GyT-0b-5A5" secondAttribute="centerY" id="LjQ-Ke-3C1"/>
                        <constraint firstItem="AjK-EH-6BQ" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="GyT-0b-5A5" secondAttribute="trailing" constant="4" id="P1e-Jb-ahf"/>
                        <constraint firstAttribute="height" constant="34" id="jxy-n4-fJv"/>
                        <constraint firstItem="GyT-0b-5A5" firstAttribute="centerY" secondItem="6BV-BB-lnx" secondAttribute="centerY" id="pwP-6z-w9O"/>
                        <constraint firstItem="GyT-0b-5A5" firstAttribute="leading" secondItem="6BV-BB-lnx" secondAttribute="leading" constant="8" id="qnf-IT-OHD"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KLz-T1-Fie">
                    <rect key="frame" x="414" y="2" width="70" height="34"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="flexFlag" translatesAutoresizingMaskIntoConstraints="NO" id="2If-Mb-r4g">
                            <rect key="frame" x="8" y="9" width="19" height="16"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="19" id="bom-AM-3Y8"/>
                                <constraint firstAttribute="height" constant="16" id="y5S-5T-zEc"/>
                            </constraints>
                        </imageView>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="21" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="plI-l0-FHK">
                            <rect key="frame" x="36.5" y="6.5" width="17.5" height="21"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="21" id="i2p-8N-tn9"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                            <nil key="textColor"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstItem="2If-Mb-r4g" firstAttribute="leading" secondItem="KLz-T1-Fie" secondAttribute="leading" constant="8" id="4ER-cP-Gtj"/>
                        <constraint firstItem="plI-l0-FHK" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="2If-Mb-r4g" secondAttribute="trailing" constant="4" id="Jnb-Yh-tZM"/>
                        <constraint firstAttribute="width" constant="70" id="RKk-6U-0w8"/>
                        <constraint firstAttribute="trailing" secondItem="plI-l0-FHK" secondAttribute="trailing" constant="16" id="cAx-3H-asH"/>
                        <constraint firstItem="2If-Mb-r4g" firstAttribute="centerY" secondItem="KLz-T1-Fie" secondAttribute="centerY" id="ps0-5P-Pr7"/>
                        <constraint firstItem="plI-l0-FHK" firstAttribute="centerY" secondItem="2If-Mb-r4g" secondAttribute="centerY" id="wRd-wV-olm"/>
                        <constraint firstAttribute="height" constant="34" id="yfH-VD-yy5"/>
                    </constraints>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="vUN-kp-3ea"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="KLz-T1-Fie" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="2" id="1U5-eJ-AUc"/>
                <constraint firstAttribute="trailing" secondItem="6BV-BB-lnx" secondAttribute="trailing" constant="-70" id="O83-6y-VRc"/>
                <constraint firstAttribute="trailing" secondItem="KLz-T1-Fie" secondAttribute="trailing" constant="-70" id="eYj-uR-LaD"/>
                <constraint firstItem="6BV-BB-lnx" firstAttribute="top" secondItem="KLz-T1-Fie" secondAttribute="bottom" constant="6" id="kDo-za-vUl"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="commentCountLabel" destination="AjK-EH-6BQ" id="oQx-4H-fMJ"/>
                <outlet property="commentCountView" destination="6BV-BB-lnx" id="f8l-Zn-60h"/>
                <outlet property="commentCountViewTrailingConstraint" destination="O83-6y-VRc" id="fsl-d9-ilK"/>
                <outlet property="reflexCountLabel" destination="plI-l0-FHK" id="0xF-w5-BEK"/>
                <outlet property="reflexCountView" destination="KLz-T1-Fie" id="jgK-8u-H6P"/>
                <outlet property="reflexCountViewTrailingConstraint" destination="eYj-uR-LaD" id="yYL-Lo-DCK"/>
            </connections>
            <point key="canvasLocation" x="-46.376811594202906" y="-295.64732142857139"/>
        </view>
    </objects>
    <resources>
        <image name="commentFlag" width="19" height="18"/>
        <image name="flexFlag" width="20" height="17"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
