//
//  SliderCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/16/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class SliderCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var slider: UISlider!
    @IBOutlet weak var imageView: UIImageView!
    
    weak var delegate: CameraDelegate?
    
    static var cellIdentifier = String(describing: SliderCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        slider.tintColor = .gfGreen
        slider.maximumValue = 1.0
        slider.minimumValue = 0.0
    }
    
    @IBAction func sliderChanged(_ sender: UISlider) {
        delegate?.sliderDidChange(sender)
    }
}
