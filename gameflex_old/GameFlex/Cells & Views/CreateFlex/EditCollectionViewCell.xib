<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="gwr-8F-aQy" customClass="EditCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="103" height="140"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Fo5-tr-1fd">
                    <rect key="frame" x="12" y="36" width="79" height="79"/>
                    <subviews>
                        <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lC7-bK-cGe">
                            <rect key="frame" x="0.0" y="0.0" width="79" height="79"/>
                            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="soo-wE-HJu">
                                <rect key="frame" x="0.0" y="0.0" width="79" height="79"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <visualEffectView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9Ob-1I-dQg">
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="hdx-b6-Qxz">
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        </view>
                                        <vibrancyEffect>
                                            <blurEffect style="regular"/>
                                        </vibrancyEffect>
                                    </visualEffectView>
                                </subviews>
                            </view>
                            <constraints>
                                <constraint firstAttribute="width" constant="79" id="SfX-Cl-n6q"/>
                                <constraint firstAttribute="height" constant="79" id="woj-36-mYD"/>
                            </constraints>
                            <blurEffect style="light"/>
                        </visualEffectView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="lC7-bK-cGe" firstAttribute="leading" secondItem="Fo5-tr-1fd" secondAttribute="leading" id="fLq-qB-Qa6"/>
                        <constraint firstAttribute="width" constant="79" id="gKN-g7-12E"/>
                        <constraint firstAttribute="height" constant="79" id="uj9-xY-Klb"/>
                        <constraint firstItem="lC7-bK-cGe" firstAttribute="centerY" secondItem="Fo5-tr-1fd" secondAttribute="centerY" id="zOd-gD-oUW"/>
                    </constraints>
                </view>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="eRl-0N-L52">
                    <rect key="frame" x="12" y="35.5" width="79" height="79"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="79" id="8yj-LM-XGZ"/>
                        <constraint firstAttribute="width" constant="79" id="f6R-84-2k1"/>
                    </constraints>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YDq-Qi-36f">
                    <rect key="frame" x="36" y="2" width="31" height="24.5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24.5" id="5uF-lK-5uK"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="3aG-t4-Puq">
                    <rect key="frame" x="33" y="56.5" width="37.5" height="37.5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="37.5" id="XjW-kZ-wZx"/>
                        <constraint firstAttribute="width" constant="37.5" id="gP1-px-0vl"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="3aG-t4-Puq" firstAttribute="centerY" secondItem="eRl-0N-L52" secondAttribute="centerY" id="1yH-TU-zXd"/>
                <constraint firstItem="3aG-t4-Puq" firstAttribute="centerX" secondItem="eRl-0N-L52" secondAttribute="centerX" id="6Nk-nA-rMg"/>
                <constraint firstItem="YDq-Qi-36f" firstAttribute="centerX" secondItem="gwr-8F-aQy" secondAttribute="centerX" id="6Wm-cO-xhh"/>
                <constraint firstItem="YDq-Qi-36f" firstAttribute="top" secondItem="gwr-8F-aQy" secondAttribute="top" constant="2" id="J3U-Ru-DCt"/>
                <constraint firstItem="Fo5-tr-1fd" firstAttribute="centerX" secondItem="gwr-8F-aQy" secondAttribute="centerX" id="a2o-qy-eta"/>
                <constraint firstItem="Fo5-tr-1fd" firstAttribute="top" secondItem="YDq-Qi-36f" secondAttribute="bottom" constant="9.5" id="ewz-uX-2dW"/>
                <constraint firstItem="eRl-0N-L52" firstAttribute="top" secondItem="YDq-Qi-36f" secondAttribute="bottom" constant="9" id="or2-Pc-nIc"/>
                <constraint firstItem="eRl-0N-L52" firstAttribute="centerX" secondItem="gwr-8F-aQy" secondAttribute="centerX" id="ygp-eF-lN4"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <viewLayoutGuide key="safeArea" id="HV7-SB-zYu"/>
            <connections>
                <outlet property="backgroundBlur" destination="Fo5-tr-1fd" id="leL-nj-q6i"/>
                <outlet property="backgroundImageView" destination="eRl-0N-L52" id="s0q-O8-jz3"/>
                <outlet property="editImageView" destination="3aG-t4-Puq" id="baq-Fv-MCz"/>
                <outlet property="titleLabel" destination="YDq-Qi-36f" id="fCe-pm-QoO"/>
            </connections>
            <point key="canvasLocation" x="144.20289855072465" y="-206.25"/>
        </view>
    </objects>
</document>
