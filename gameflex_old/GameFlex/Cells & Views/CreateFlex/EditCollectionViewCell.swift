//
//  EditCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/13/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class EditCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var editImageView: UIImageView!
    @IBOutlet weak var backgroundImageView: UIImageView!
    @IBOutlet weak var backgroundBlur: UIView!
            
    weak var delegate: CameraDelegate?
    
    static var cellIdentifier = String(describing: EditCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        editImageView.layer.cornerRadius = editImageView.frame.size.width/2
        backgroundBlur.layer.cornerRadius = backgroundBlur.frame.size.width/2
        backgroundBlur.clipsToBounds = true
        backgroundImageView.isHidden = true
    }
    
}
