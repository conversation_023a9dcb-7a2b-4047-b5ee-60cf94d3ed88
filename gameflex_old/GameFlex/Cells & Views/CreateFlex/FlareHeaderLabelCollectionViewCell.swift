//
//  FlareHeaderLabelCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/18/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FlareHeaderLabelCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var underline: UIView!
    @IBOutlet weak var underlineTopConstraint: NSLayoutConstraint!
    @IBOutlet weak var underlineLeadingConstraint: NSLayoutConstraint!
    @IBOutlet weak var underlineTrailingConstraint: NSLayoutConstraint!
    @IBOutlet weak var titleLabelLeadingConstraint: NSLayoutConstraint!
    
    static var cellIdentifier = String(describing: FlareHeaderLabelCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        showAsSelected()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.textColor = .gfGrayText
        titleLabel.font = .systemFont(ofSize: 14)
        underline.isHidden = true
        backgroundColor = .clear
    }
    
    func showAsSelected(_ selected: Bool = false) {
        if selected {
            underline.isHidden = false
            titleLabel.textColor = .white
            titleLabel.font = .boldSystemFont(ofSize: 14)
        } else {
            titleLabel.textColor = .gfGrayText
            titleLabel.font = .systemFont(ofSize: 14)
            underline.isHidden = true
        }
    }
    
    func isCurrentlySelected() -> Bool {
        return (underline.isHidden == false && (titleLabel.textColor == .white || titleLabel.textColor == .gfGreen) && titleLabel.font == .boldSystemFont(ofSize: 14))
    }
    
    // exclusively for profile
    func showProfileAsSelected(_ selected: Bool = false, title: String) {
        let attr = NSMutableAttributedString(string: title)
        let arr = title.components(separatedBy: " ")
        if arr.count == 2 {
            let subStringRange = title.range(of:arr[0])
            let subStringRange2 = title.range(of: arr[1])
            if let subStringRange = subStringRange, let subStringRange2 = subStringRange2 {
                let nsRange = NSRange(subStringRange, in: title)
                let nsRange2 = NSRange(subStringRange2, in: title)
                if selected {
                    attr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 14),
                                        NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                                       range: nsRange)
                    attr.addAttributes([NSAttributedString.Key.font : UIFont.systemFont(ofSize: 11),
                                        NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                                       range: nsRange2)
                    titleLabel.attributedText = attr
                } else {
                    attr.addAttributes([NSAttributedString.Key.font : UIFont.systemFont(ofSize: 14),
                                        NSAttributedString.Key.foregroundColor: UIColor.gfGrayText],
                                       range: nsRange)
                    attr.addAttributes([NSAttributedString.Key.font : UIFont.systemFont(ofSize: 11),
                                        NSAttributedString.Key.foregroundColor: UIColor.gfGrayText],
                                       range: nsRange2)
                    titleLabel.attributedText = attr
                }
            }
        }
        
        if selected {
            underline.isHidden = false
            underline.backgroundColor = .gfGreen
            backgroundColor = UIColor.gfGreen.withAlphaComponent(0.3)
            underlineTopConstraint.constant = 8
            clipsToBounds = true
        } else {
            titleLabel.textColor = .gfGrayText
            backgroundColor = .clear
        }
    }
}
