<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="StickerCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="129" height="104"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aN3-32-Qvu">
                    <rect key="frame" x="40.5" y="28" width="48" height="48"/>
                    <subviews>
                        <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LbP-ei-qk7">
                            <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="gEU-hm-7mv">
                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <visualEffectView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="a9R-CO-soG">
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="hT5-fQ-L39">
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        </view>
                                        <vibrancyEffect>
                                            <blurEffect style="regular"/>
                                        </vibrancyEffect>
                                    </visualEffectView>
                                </subviews>
                            </view>
                            <blurEffect style="light"/>
                        </visualEffectView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="OwR-BW-8c7">
                            <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="OwR-BW-8c7" secondAttribute="bottom" id="2vS-5L-5h4"/>
                        <constraint firstItem="LbP-ei-qk7" firstAttribute="leading" secondItem="aN3-32-Qvu" secondAttribute="leading" id="7gY-Mn-0uy"/>
                        <constraint firstItem="OwR-BW-8c7" firstAttribute="top" secondItem="aN3-32-Qvu" secondAttribute="top" id="V6p-VB-8Vv"/>
                        <constraint firstAttribute="trailing" secondItem="LbP-ei-qk7" secondAttribute="trailing" id="ZsB-ei-lG3"/>
                        <constraint firstItem="OwR-BW-8c7" firstAttribute="leading" secondItem="aN3-32-Qvu" secondAttribute="leading" id="bay-i6-5LV"/>
                        <constraint firstAttribute="trailing" secondItem="OwR-BW-8c7" secondAttribute="trailing" id="coz-ft-edc"/>
                        <constraint firstAttribute="bottom" secondItem="LbP-ei-qk7" secondAttribute="bottom" id="ghD-2e-BTt"/>
                        <constraint firstAttribute="height" constant="48" id="jTw-vX-wS5"/>
                        <constraint firstItem="LbP-ei-qk7" firstAttribute="top" secondItem="aN3-32-Qvu" secondAttribute="top" id="psG-rY-aDJ"/>
                        <constraint firstAttribute="width" constant="48" id="zw7-1U-3vn"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="aN3-32-Qvu" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="3bG-un-X4x"/>
                <constraint firstItem="aN3-32-Qvu" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="wPA-Ds-Ooy"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="backgrounder" destination="aN3-32-Qvu" id="gPb-97-Vxe"/>
                <outlet property="imageView" destination="OwR-BW-8c7" id="xhp-AN-gnv"/>
            </connections>
            <point key="canvasLocation" x="-15.217391304347828" y="-30.133928571428569"/>
        </view>
    </objects>
</document>
