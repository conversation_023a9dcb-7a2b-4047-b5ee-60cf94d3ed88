<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FontCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="100"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Uy8-a5-khE">
                    <rect key="frame" x="182" y="25" width="50" height="50"/>
                    <subviews>
                        <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7RT-FV-m27">
                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="Xwv-4o-bUB">
                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <visualEffectView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="nWG-mQ-48o">
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="eRf-c6-kx4">
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        </view>
                                        <vibrancyEffect>
                                            <blurEffect style="regular"/>
                                        </vibrancyEffect>
                                    </visualEffectView>
                                </subviews>
                            </view>
                            <constraints>
                                <constraint firstAttribute="width" constant="50" id="0tM-uG-xJ3"/>
                                <constraint firstAttribute="height" constant="50" id="u5s-3p-WLZ"/>
                            </constraints>
                            <blurEffect style="dark"/>
                        </visualEffectView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="7RT-FV-m27" firstAttribute="leading" secondItem="Uy8-a5-khE" secondAttribute="leading" id="1OD-0R-PU3"/>
                        <constraint firstItem="7RT-FV-m27" firstAttribute="centerY" secondItem="Uy8-a5-khE" secondAttribute="centerY" id="P20-qT-myi"/>
                        <constraint firstAttribute="width" constant="50" id="gki-ru-J4F"/>
                        <constraint firstAttribute="height" constant="50" id="pYH-OA-VWj"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tQp-uH-i1l">
                    <rect key="frame" x="182" y="25" width="50" height="50"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="50" id="GXI-Bh-caC"/>
                        <constraint firstAttribute="height" constant="50" id="MYU-vH-QHw"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Uy8-a5-khE" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="Bqa-Ih-yGs"/>
                <constraint firstItem="tQp-uH-i1l" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" id="J0c-yr-vvg"/>
                <constraint firstItem="Uy8-a5-khE" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="rLL-AT-lCS"/>
                <constraint firstItem="tQp-uH-i1l" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="t0U-O9-EIt"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="blurView" destination="Uy8-a5-khE" id="f4g-LI-3SR"/>
                <outlet property="fontView" destination="tQp-uH-i1l" id="uXY-5I-mpi"/>
            </connections>
            <point key="canvasLocation" x="321.73913043478262" y="-195.53571428571428"/>
        </view>
    </objects>
</document>
