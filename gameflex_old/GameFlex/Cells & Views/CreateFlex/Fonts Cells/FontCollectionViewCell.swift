//
//  FontCollectionViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 7/23/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class FontCollectionViewCell: UICollectionViewCell {
    
    @IBOutlet weak var fontView: UIView!
    @IBOutlet weak var blurView: UIView!
    
    var font: UIFont?
    
    static var cellIdentifier = String(describing: FontCollectionViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        blurView.layer.cornerRadius = blurView.frame.width/2
        blurView.clipsToBounds = true
        fontView.layer.cornerRadius = fontView.frame.size.height/2
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        font = .systemFont(ofSize: 17)
    }
    
    func configureLabel(_ isSelected: Bool = false) {
        fontView.subviews.filter({$0.tag == 78844221}).forEach({ $0.removeFromSuperview()})
        let fontLabel = UILabel(frame: CGRect(origin: CGPoint.zero, size: fontView.frame.size))
        fontLabel.text = "fontCell.example".localized
        fontLabel.font = font ?? .systemFont(ofSize: 44)
        fontLabel.textAlignment = .center
        fontLabel.tag = 78844221
        fontView.addSubview(fontLabel)
        
        if isSelected {
            fontView.backgroundColor = .gfGreen
            fontLabel.textColor = .black
        } else {
            fontView.backgroundColor = .gfDarkGrayMask
            fontLabel.textColor = .white
        }
        
    }
}
