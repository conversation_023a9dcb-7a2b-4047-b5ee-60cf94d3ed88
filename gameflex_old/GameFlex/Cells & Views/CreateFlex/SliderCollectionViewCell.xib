<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="16097.2" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="16087"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="SliderCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="222"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <visualEffectView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2bv-XW-a0m">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="222"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="bug-8J-vvC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="222"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    </view>
                    <blurEffect style="dark"/>
                </visualEffectView>
                <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.5" minValue="0.0" maxValue="1" translatesAutoresizingMaskIntoConstraints="NO" id="bVs-gB-3As">
                    <rect key="frame" x="30" y="132" width="354" height="31"/>
                    <connections>
                        <action selector="sliderChanged:" destination="iN0-l3-epB" eventType="valueChanged" id="8Hm-4K-alU"/>
                    </connections>
                </slider>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="hwZ-zs-XX3">
                    <rect key="frame" x="195" y="14" width="24" height="24"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24" id="dDK-KL-uBu"/>
                        <constraint firstAttribute="width" constant="24" id="n8P-xc-Ppj"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="bVs-gB-3As" secondAttribute="bottom" constant="60" id="1OT-ry-qDa"/>
                <constraint firstItem="hwZ-zs-XX3" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="83W-ZD-MsG"/>
                <constraint firstAttribute="trailing" secondItem="bVs-gB-3As" secondAttribute="trailing" constant="32" id="XPH-t4-ABv"/>
                <constraint firstItem="hwZ-zs-XX3" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="14" id="YS0-Io-WUt"/>
                <constraint firstItem="bVs-gB-3As" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="32" id="lA1-JY-LnS"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="imageView" destination="hwZ-zs-XX3" id="pKx-eR-ZuD"/>
                <outlet property="slider" destination="bVs-gB-3As" id="rki-ZB-WuJ"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="122.54464285714285"/>
        </view>
    </objects>
</document>
