//
//  ProfileBottomTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/10/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class ProfileBottomTableViewCell: UITableViewCell {
    
    @IBOutlet weak var collectionView: UICollectionView!
    
    var channelId: String?
    var flexArray: [Flex] = []
    var noMoreData = false
    
    weak var delegate: ProfileDelegate?
    
    static var cellIdentifier = String(describing: ProfileBottomTableViewCell.self)
 
    override func awakeFromNib() {
        super.awakeFromNib()
        selectionStyle = .none
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(UINib(nibName: FlexCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlexCollectionViewCell.cellIdentifier)
    }
    
    func configureCell(_ channelId: String) {
        self.channelId = channelId
        refreshChannel(top: true)
    }
    
    @objc func refreshChannel(top: Bool) {
        if let channelId = channelId {
            GFNetworkServices.getUserFlexes(channelId: channelId, top: top, { (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if !result.isEmpty {
                    if top {
                        self.flexArray = result
                    } else {
                        self.flexArray.append(contentsOf: result)
                    }
                    self.flexArray = Array(Set(self.flexArray))
                    self.flexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    if let channelId = self.flexArray.last?.channel?.channelId, let _ = GFDefaults.channelTimeStamps, let time = self.flexArray.last?.createAt {
                        GFDefaults.channelTimeStamps?[channelId] = time
                    } else if let channelId = self.flexArray.last?.channel?.channelId, let time = self.flexArray.last?.createAt {
                        GFDefaults.channelTimeStamps = [channelId: time]
                    }
                    DispatchQueue.main.async {
                        self.collectionView.reloadData()
                        self.noMoreData = false
                    }
                } else {
                    self.noMoreData = true
                }
            })

        }
    }

}

extension ProfileBottomTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return flexArray.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlexCollectionViewCell.cellIdentifier, for: indexPath) as? FlexCollectionViewCell else { return FlexCollectionViewCell() }
        if flexArray.isEmpty {
            cell.flexImageView.image = FlexManager.placeholders[Int.random(in: 0..<FlexManager.placeholders.count)]
            cell.flexImageView.alpha = 0.6
            let label = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 130))
            label.text = ".. needs content.. "
            label.textColor = .gfYellow_F2DE76
            label.numberOfLines = 0
            label.tag = 972231
            label.layer.shadowColor = UIColor.black.cgColor
            label.layer.shadowOffset = CGSize(width: 1.5, height: 1.5)
            label.font = .systemFont(ofSize: 19, weight: .heavy)
            label.textAlignment = .center
            cell.addSubview(label)
            return cell
        }
        cell.subviews.filter({$0.tag == 972231 }).forEach({ $0.removeFromSuperview() })
        cell.configureCell(flexArray[indexPath.row])
        cell.flexImageView.alpha = 1.0
        
        if !noMoreData, indexPath.row == collectionView.numberOfItems(inSection: 0) - 3 {
            refreshChannel(top: false)
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let detail = ChezLuiDetail(row: indexPath.row, flexArray: flexArray)
        delegate?.didTapForProfileAction(.goToChezLui, detail)
    }
    
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return Utilities.miniFlexDisplaySize // 100:132 but sized for device
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 4.0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0.0, left: 0.0, bottom: 0.0, right: 0.0)
    }
}
