//
//  AlertButtonTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 12/3/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

class AlertButtonTableViewCell: UITableViewCell {
    
    @IBOutlet weak var sourceImageView: UIImageView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var followButton: UIButton!
    @IBOutlet weak var sourceSuperView: UIView!
    @IBOutlet weak var cellView: UIView!
    @IBOutlet weak var profileButton: UIButton!
    
    weak var delegate: HomeDelegate?
    
    var note: GFNotification?
    
    static var cellIdentifier = String(describing: AlertButtonTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = ""
    }
    
    func configureCell(_ note: GFNotification) {
        cellView.backgroundColor = .gfDarkBackground40
        selectionStyle = .none
        sourceSuperView.layer.cornerRadius = sourceSuperView.frame.size.width/2
        sourceSuperView.clipsToBounds = true

        self.note = note
        
        var titleString = ""
        let source = note.source?.flexterName ?? "Someone"
        let suffix = "\(Utilities.relativeTime(date: note.date))"

        switch note.notificationType { // the other types are handled by AlertTableViewCell
        case .invite_accepted:
            let channel = note.channel?.channelName ?? ""
            titleString = "\(source) accepted invitation to participate in \(channel) channel. \(suffix)"
        case .invite:
            let channel = note.channel?.channelName ?? ""
            titleString = "\(source) invited you to participate in \(channel) channel. \(suffix)"
        case .follow:
            titleString = "\(source) started following you. \(suffix)"
        default:
            titleString = "\(source) unknown message. \(suffix)"
        }
        let attributed = NSMutableAttributedString(string: titleString)
        if titleString.contains("channel"), let channel = note.channel?.channelName {
            attributed.addAttribute(NSAttributedString.Key.font, value: UIFont.boldSystemFont(ofSize: 14), range: NSString(string: titleString).range(of: channel) )
        }
        attributed.addAttribute(NSAttributedString.Key.font, value: UIFont.boldSystemFont(ofSize: 14), range: NSRange(location: 0, length: source.count))
        attributed.addAttributes([NSAttributedString.Key.foregroundColor: UIColor.gfGrayText,
                                  NSAttributedString.Key.font: UIFont.systemFont(ofSize: 9)], range: NSString(string: titleString).range(of: suffix) )
        titleLabel.attributedText = attributed
        // avatar view
        if let userId = note.source?.userId {
            sourceImageView.image = #imageLiteral(resourceName: FlexManager.randomImagePlaceholder(userId))
        }
        if let pic = note.source?.profileImage, pic != "", let objectId = note.source?.userId {
            let url = URL(string: pic)
            let processor = DownsamplingImageProcessor(size: sourceImageView?.bounds.size ?? CGSize(width: 40, height: 40))
            sourceImageView?.kf.indicatorType = .activity
            sourceImageView?.kf.setImage(
                with: url,
                placeholder: UIImage(named: FlexManager.randomImagePlaceholder(objectId)),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success: break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                        }
                    })
        }
        sourceSuperView.layer.cornerRadius = sourceSuperView.frame.size.width/2
        sourceSuperView.layer.borderColor = UIColor.gfYellow_F2DE76.cgColor
        sourceSuperView.layer.borderWidth  = 1.0
        selectionStyle = .none
    }
    
    // configures the followButton or shows the flex, triggered from tableView cellAtRow
    func configureCell(following: Bool = false) {
        Utilities.configure(followButton: followButton, following: following)
    }
    
    func configureCellForInvite() {
        var invite = true
        if (User.flexter.channelsParticipated?.filter({ $0.channelId == note?.channel?.channelId }).count ?? 0) > 0 {
            invite = false
        }
        if note?.notificationType == .invite_accepted {
            invite = false
        }
        Utilities.configureForInvite(followButton: followButton, invite: invite)
        if note?.notificationType == .follow {
            followButton.isHidden = true
        } else {
            followButton.isHidden = false
        }
    }
    
    @IBAction func didTapButton(_ sender: UIButton) {
        if sender == followButton {
            if sender.titleLabel?.text == "comment.followButton.invite".localized, let noteId = note?.notificationId  {
                delegate?.didTapToAcceptInvitation(noteId)
                Utilities.configureForInvite(followButton: followButton, invite: false)
                return
            } else if sender.titleLabel?.text == "comment.followButton.accepted".localized {
                return
            }
            delegate?.didTapToFollow(sender, self)
        }
    }

}
