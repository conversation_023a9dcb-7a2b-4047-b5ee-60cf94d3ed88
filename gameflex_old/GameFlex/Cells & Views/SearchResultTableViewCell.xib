<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17506" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17505"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="SearchResultTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="194"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bcm-7V-vnC">
                    <rect key="frame" x="20" y="10" width="32" height="32"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xd4-z3-6ka">
                            <rect key="frame" x="0.0" y="0.0" width="32" height="32"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="32" id="Gyf-WA-DXR"/>
                                <constraint firstAttribute="width" constant="32" id="lyn-Gt-qYv"/>
                            </constraints>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="xd4-z3-6ka" firstAttribute="centerY" secondItem="bcm-7V-vnC" secondAttribute="centerY" id="QDZ-hC-3BE"/>
                        <constraint firstAttribute="width" constant="32" id="Rvu-6g-XuN"/>
                        <constraint firstItem="xd4-z3-6ka" firstAttribute="centerX" secondItem="bcm-7V-vnC" secondAttribute="centerX" id="lDM-Tp-OW5"/>
                        <constraint firstAttribute="height" constant="32" id="lq4-s1-oXb"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="SjI-Ld-wo9">
                    <rect key="frame" x="68" y="10" width="47" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="gCl-Za-4IE"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="18"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="ZEq-GU-cNM">
                    <rect key="frame" x="68" y="31" width="31" height="15"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="15" id="F65-FT-QTY"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" red="0.95686274510000002" green="0.95686274510000002" blue="0.95686274510000002" alpha="0.30356378420000002" colorSpace="calibratedRGB"/>
                    <nil key="highlightedColor"/>
                </label>
                <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="TOp-1c-IeJ">
                    <rect key="frame" x="0.0" y="55" width="414" height="132"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="132" id="ufO-rB-0l7"/>
                    </constraints>
                    <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="BJN-dg-lNe">
                        <size key="itemSize" width="128" height="128"/>
                        <size key="headerReferenceSize" width="0.0" height="0.0"/>
                        <size key="footerReferenceSize" width="0.0" height="0.0"/>
                        <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                    </collectionViewFlowLayout>
                </collectionView>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pK9-08-SLr">
                    <rect key="frame" x="339" y="10" width="65" height="24"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="24" id="VCA-Il-F0U"/>
                    </constraints>
                    <color key="tintColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <state key="normal" title="Following">
                        <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </state>
                    <connections>
                        <action selector="didTapButton:" destination="iN0-l3-epB" eventType="touchUpInside" id="gqt-WO-HVY"/>
                    </connections>
                </button>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="SjI-Ld-wo9" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="4lZ-Yk-n0C"/>
                <constraint firstAttribute="trailing" secondItem="pK9-08-SLr" secondAttribute="trailing" constant="10" id="6w6-hy-5qV"/>
                <constraint firstItem="pK9-08-SLr" firstAttribute="top" secondItem="SjI-Ld-wo9" secondAttribute="top" id="Goq-92-sjr"/>
                <constraint firstItem="TOp-1c-IeJ" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="Lbh-Ff-3YH"/>
                <constraint firstItem="TOp-1c-IeJ" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="55" id="NDd-LF-GoF"/>
                <constraint firstItem="bcm-7V-vnC" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="10" id="VSh-ey-RUT"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="SjI-Ld-wo9" secondAttribute="trailing" constant="10" id="Zsk-4Y-9o9"/>
                <constraint firstItem="ZEq-GU-cNM" firstAttribute="top" secondItem="SjI-Ld-wo9" secondAttribute="bottom" constant="-1" id="iWc-B5-YHa"/>
                <constraint firstItem="ZEq-GU-cNM" firstAttribute="leading" secondItem="bcm-7V-vnC" secondAttribute="trailing" constant="16" id="u13-uc-U3V"/>
                <constraint firstItem="SjI-Ld-wo9" firstAttribute="leading" secondItem="bcm-7V-vnC" secondAttribute="trailing" constant="16" id="xI9-2s-1gL"/>
                <constraint firstItem="bcm-7V-vnC" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" symbolic="YES" id="xIE-cW-Mha"/>
                <constraint firstAttribute="trailing" secondItem="TOp-1c-IeJ" secondAttribute="trailing" id="yFG-Si-h0O"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="collectionView" destination="TOp-1c-IeJ" id="vXC-N6-NqV"/>
                <outlet property="followButton" destination="pK9-08-SLr" id="pgF-tC-J3E"/>
                <outlet property="profileImage" destination="xd4-z3-6ka" id="w5h-zZ-9z4"/>
                <outlet property="profileSuperView" destination="bcm-7V-vnC" id="ORj-LX-mnw"/>
                <outlet property="subTitleLabel" destination="ZEq-GU-cNM" id="kQR-19-b6C"/>
                <outlet property="titleLabel" destination="SjI-Ld-wo9" id="R43-eh-jI8"/>
                <outlet property="titleLabelTrailingConstraint" destination="Zsk-4Y-9o9" id="OnM-Aj-nTK"/>
            </connections>
            <point key="canvasLocation" x="40.579710144927539" y="-163.05803571428569"/>
        </view>
    </objects>
</document>
