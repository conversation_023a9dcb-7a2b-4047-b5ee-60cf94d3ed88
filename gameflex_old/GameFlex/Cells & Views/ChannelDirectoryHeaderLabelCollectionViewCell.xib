<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="ChannelDirectoryHeaderLabelCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="All" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7r3-nv-vB0">
                    <rect key="frame" x="0.0" y="10.5" width="414" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="S7Q-2F-J60"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <nil key="textColor"/>
                    <nil key="highlightedColor"/>
                </label>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="o6r-Il-Mv6">
                    <rect key="frame" x="0.0" y="31.5" width="414" height="2"/>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="2" id="X8m-ly-g0D"/>
                    </constraints>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="7r3-nv-vB0" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-0.5" id="1FJ-Hi-fY2"/>
                <constraint firstItem="o6r-Il-Mv6" firstAttribute="trailing" secondItem="7r3-nv-vB0" secondAttribute="trailing" id="5Qg-ew-GNR"/>
                <constraint firstAttribute="trailing" secondItem="7r3-nv-vB0" secondAttribute="trailing" id="F5X-Bu-4yW"/>
                <constraint firstItem="o6r-Il-Mv6" firstAttribute="leading" secondItem="7r3-nv-vB0" secondAttribute="leading" id="OUt-Br-MS0"/>
                <constraint firstItem="7r3-nv-vB0" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="e3n-KD-MNG"/>
                <constraint firstItem="o6r-Il-Mv6" firstAttribute="top" secondItem="7r3-nv-vB0" secondAttribute="bottom" constant="-1" id="jfd-TF-ZcR"/>
            </constraints>
            <nil key="simulatedTopBarMetrics"/>
            <nil key="simulatedBottomBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="titleLabel" destination="7r3-nv-vB0" id="CKq-e0-lQC"/>
                <outlet property="underline" destination="o6r-Il-Mv6" id="9AM-tl-nnH"/>
                <outlet property="underlineLeadingConstraint" destination="OUt-Br-MS0" id="mq0-zb-abM"/>
                <outlet property="underlineTopConstraint" destination="jfd-TF-ZcR" id="6aO-bg-drc"/>
                <outlet property="underlineTrailingConstraint" destination="5Qg-ew-GNR" id="TxE-5J-re2"/>
            </connections>
            <point key="canvasLocation" x="137.68115942028987" y="-113.83928571428571"/>
        </view>
    </objects>
</document>
