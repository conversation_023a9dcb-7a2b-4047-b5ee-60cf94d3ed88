//
//  ProfileMiddleTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/10/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum ProfileMiddleCellType {
    case flexes, following, followers, channels
    case owner, contributor, subscribed, all
}

class ProfileMiddleTableViewCell: UITableViewCell {
    
    @IBOutlet weak var collectionView: UICollectionView!
    var selection: ProfileMiddleCellType = .flexes
    var isProfile: Bool = true
    
    var sequenceOfCells: [ProfileMiddleCellType] = [.flexes, .following, .followers]//, .channels]
    var titlesDictionary: [ProfileMiddleCellType: String] = [.flexes: "\("profile.flexes".localized) (123)",
                                                             .following: "\("profile.following".localized) (45)",
                                                             .followers: "\("profile.follower".localized) (6)",
                                                             .channels: "\("profile.channels".localized) (6)",
                                                             .owner: "Owner",
                                                             .contributor: "Contributor",
                                                             .subscribed: "Subscribed",
                                                             .all: "All"]
    var tableViewWidth: CGFloat? // used only in ChannelDirectoryViewController
    
    weak var delegate: ProfileDelegate?
    
    static var cellIdentifier = String(describing: ProfileMiddleTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        selectionStyle = .none
        collectionView.register(UINib(nibName: FlareHeaderLabelCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlareHeaderLabelCollectionViewCell.cellIdentifier)
        collectionView.register(UINib(nibName: ChannelDirectoryHeaderLabelCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: ChannelDirectoryHeaderLabelCollectionViewCell.cellIdentifier)
        collectionView.delegate = self
        collectionView.dataSource = self
    }
    
    func configureCell(flexter: Flexter) {
        let flexes = flexter.flexCount ?? 0
        let following = flexter.following?.count ?? 0
        let participate = (flexter.channelsParticipated?.count ?? 0)
        let favorite = (flexter.channelsFollowed?.count ?? 0)
        let owned = (flexter.channelsOwned?.count ?? 0)
        let followerCount = (flexter.followerCount ?? 0)
        let channels = participate + owned + favorite
        titlesDictionary[.flexes] = "\("profile.flexes".localized) (\(flexes))"
        titlesDictionary[.following] = "\("profile.following".localized) (\(following))"
        titlesDictionary[.channels] = "\("profile.channels".localized) (\(channels))"
        titlesDictionary[.followers] = "\("profile.follower".localized) (\(followerCount))"
        collectionView.reloadData()
   }
    
    func configureCellForChannelDirectory() {
        isProfile = false
        if selection == .flexes {
            selection = .owner
        }
        sequenceOfCells = [.owner, .contributor, .subscribed, .all]
        collectionView.reloadData()
    }
}

extension ProfileMiddleTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return sequenceOfCells.count + 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if tableViewWidth != nil {
            guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ChannelDirectoryHeaderLabelCollectionViewCell.cellIdentifier, for: indexPath) as? ChannelDirectoryHeaderLabelCollectionViewCell else { return ChannelDirectoryHeaderLabelCollectionViewCell() }
            guard indexPath.row < sequenceOfCells.count else {
                cell.titleLabel.text = ""
                return cell
            }
            let cellType = sequenceOfCells[indexPath.row]
            cell.titleLabel.text = titlesDictionary[cellType]
            if isProfile {
                cell.showProfileAsSelected(cellType == selection, title: titlesDictionary[cellType] ?? "")
            } else {
                cell.showChannelAsSelected(cellType == selection, title: titlesDictionary[cellType] ?? "")
            }
            return cell
        }
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlareHeaderLabelCollectionViewCell.cellIdentifier, for: indexPath) as? FlareHeaderLabelCollectionViewCell else { return FlareHeaderLabelCollectionViewCell() }
        guard indexPath.row < sequenceOfCells.count else {
            cell.titleLabel.text = ""
            return cell
        }
        let cellType = sequenceOfCells[indexPath.row]
        cell.titleLabel.text = titlesDictionary[cellType]
        
        cell.showProfileAsSelected(cellType == selection, title: titlesDictionary[cellType] ?? "")
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if let wide = tableViewWidth {
            let width = wide/CGFloat(sequenceOfCells.count) > 80 ? wide/CGFloat(sequenceOfCells.count) : 95
            return CGSize(width: width , height: 45.0)
        }
        
        if indexPath.row == sequenceOfCells.count { // last item is a spacer
            return CGSize(width: 20.0, height: 33.0)
        }
        let tempLabel = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 100))
        tempLabel.text = titlesDictionary[sequenceOfCells[indexPath.row]]
        let width = tempLabel.width(.boldSystemFont(ofSize: 13))
        return CGSize(width: width + 25, height: 45.0)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let cellType = sequenceOfCells[indexPath.row]
        selection = cellType
        
        switch cellType {
        case .flexes:
            delegate?.didTapForProfileAction(.flexes, nil)
        case .following:
            delegate?.didTapForProfileAction(.following, nil)
        case .channels:
            delegate?.didTapForProfileAction(.channels, nil)
        case .followers:
            delegate?.didTapForProfileAction(.follower, nil)
        case .owner:
            delegate?.didTapForProfileAction(.owner, nil)
        case .contributor:
            delegate?.didTapForProfileAction(.contributor, nil)
        case .subscribed:
            delegate?.didTapForProfileAction(.subscribed, nil)
        case .all:
            delegate?.didTapForProfileAction(.all, nil)
        }
        collectionView.reloadData()
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        return UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }

}
