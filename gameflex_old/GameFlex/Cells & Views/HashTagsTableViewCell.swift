//
//  HashTagsTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/18/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum HashTagCellType {
    case frequent, recent
}

class HashTagsTableViewCell: UITableViewCell {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var collectionView: UICollectionView!
    
    weak var delegate: FinalizeDelegate?
    
    var cellType: HashTagCellType = .frequent
    var mostFrequentHashTags: [(key: String, value: Int)] = HashTags.mostFrequentHashTags
    
    static var cellIdentifier = String(describing: HashTagsTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        collectionView.register(UINib(nibName: FlareHeaderLabelCollectionViewCell.cellIdentifier, bundle: nil), forCellWithReuseIdentifier: FlareHeaderLabelCollectionViewCell.cellIdentifier)
        collectionView.delegate = self
        collectionView.dataSource = self
        titleLabel.text = cellType == .frequent ? "hashTags.frequentlyUsedTitle".localized : "hashTags.recentlyUsedTitle".localized
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
    }
}

extension HashTagsTableViewCell: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        switch cellType {
        case .frequent: return mostFrequentHashTags.count > 11 ? 11 : mostFrequentHashTags.count + 1
        case .recent: return HashTags.hashTagsArray.count > 11 ? 11 : HashTags.hashTagsArray.count + 1
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: FlareHeaderLabelCollectionViewCell.cellIdentifier, for: indexPath) as? FlareHeaderLabelCollectionViewCell else { return FlareHeaderLabelCollectionViewCell() }
        if indexPath.row == collectionView.numberOfItems(inSection: 0) - 1 { // last item is a spacer
            cell.titleLabel.text = ""
            return cell
        }
        cell.titleLabel.text = cellType == .frequent ? mostFrequentHashTags[indexPath.row].key : HashTags.hashTagsArray[indexPath.row]
        cell.titleLabel.font = .boldSystemFont(ofSize: 14)
        cell.titleLabel.textColor = .gfOffWhite
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if indexPath.row == collectionView.numberOfItems(inSection: 0) - 1 { // last item is a spacer
            return CGSize(width: 20.0, height: 44.0)
        }
        let tempLabel = UILabel(frame: CGRect(x: 0, y: 0, width: 100, height: 22))
        switch cellType {
        case .frequent: tempLabel.text = mostFrequentHashTags[indexPath.row].key
        case .recent: tempLabel.text = HashTags.hashTagsArray[indexPath.row]
        }
        let width = tempLabel.width(.boldSystemFont(ofSize: 14))
        return CGSize(width: width + 8.0, height: 44.0)
    }
    
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        switch cellType {
        case .frequent:
            guard indexPath.row < mostFrequentHashTags.count else { return }
        case .recent:
            guard indexPath.row < HashTags.hashTagsArray.count else { return }
        }
        let hashTag = cellType == .frequent ? mostFrequentHashTags[indexPath.row].key : HashTags.hashTagsArray[indexPath.row]
        
        if FinalizeViewModel.selectedHashTags.filter({$0 == hashTag }).count > 0 {
            FinalizeViewModel.selectedHashTags.removeAll(where: { $0 == hashTag })
        } else {
            FinalizeViewModel.selectedHashTags.append(hashTag)
        }
        
        FinalizeViewModel.updateCaption()
        delegate?.updatetheTextView()
    }
}
