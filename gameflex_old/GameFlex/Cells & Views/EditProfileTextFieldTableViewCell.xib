<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17156" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17125"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="EditProfileTextFieldTableViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="91"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Y2R-3c-hLe">
                    <rect key="frame" x="20" y="8" width="45" height="21"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="21" id="KIP-QX-hcm"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="BXN-Xh-xEd">
                    <rect key="frame" x="20" y="37" width="384" height="34"/>
                    <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="34" id="tth-Yk-KgE"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="14"/>
                    <textInputTraits key="textInputTraits" autocapitalizationType="words" autocorrectionType="no" spellCheckingType="no" keyboardAppearance="alert"/>
                </textField>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="Y2R-3c-hLe" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="8" id="5ck-cu-hQy"/>
                <constraint firstItem="Y2R-3c-hLe" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" id="7lL-YZ-4h7"/>
                <constraint firstAttribute="trailing" secondItem="BXN-Xh-xEd" secondAttribute="trailing" constant="10" id="HMp-vc-9oR"/>
                <constraint firstItem="BXN-Xh-xEd" firstAttribute="top" secondItem="Y2R-3c-hLe" secondAttribute="bottom" constant="8" id="gqj-ao-ELe"/>
                <constraint firstItem="BXN-Xh-xEd" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="20" symbolic="YES" id="zge-cq-v4u"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="textField" destination="BXN-Xh-xEd" id="T3o-y4-PZh"/>
                <outlet property="titleLabel" destination="Y2R-3c-hLe" id="wws-xY-bUA"/>
            </connections>
            <point key="canvasLocation" x="356.52173913043481" y="-103.45982142857143"/>
        </view>
    </objects>
</document>
