<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17506" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17505"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="FavoriteChannelCollectionViewCell" customModule="GameFlex" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="80" height="81"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lMt-Hg-OOB">
                    <rect key="frame" x="12" y="4" width="56" height="56"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Dtf-OP-ul0">
                            <rect key="frame" x="0.0" y="0.0" width="56" height="56"/>
                        </imageView>
                    </subviews>
                    <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="56" id="3cY-lU-jxK"/>
                        <constraint firstItem="Dtf-OP-ul0" firstAttribute="leading" secondItem="lMt-Hg-OOB" secondAttribute="leading" id="3cf-hs-9nK"/>
                        <constraint firstAttribute="trailing" secondItem="Dtf-OP-ul0" secondAttribute="trailing" id="AyQ-Fd-KKB"/>
                        <constraint firstAttribute="width" constant="56" id="JkE-SH-BBN"/>
                        <constraint firstItem="Dtf-OP-ul0" firstAttribute="top" secondItem="lMt-Hg-OOB" secondAttribute="top" id="SMF-JZ-nre"/>
                        <constraint firstAttribute="bottom" secondItem="Dtf-OP-ul0" secondAttribute="bottom" id="q0Y-nf-OCO"/>
                    </constraints>
                </view>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Fww-3N-Gey">
                    <rect key="frame" x="24.5" y="62" width="31" height="15"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="15" id="w5O-6x-hzs"/>
                    </constraints>
                    <fontDescription key="fontDescription" type="system" pointSize="12"/>
                    <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="lMt-Hg-OOB" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" constant="4" id="5xJ-hv-SPd"/>
                <constraint firstItem="Fww-3N-Gey" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="8J5-fc-Bi5"/>
                <constraint firstItem="Fww-3N-Gey" firstAttribute="top" secondItem="lMt-Hg-OOB" secondAttribute="bottom" constant="2" id="Pl6-ED-lF9"/>
                <constraint firstItem="lMt-Hg-OOB" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="SQu-Kk-fsT"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="channelTitleLabel" destination="Fww-3N-Gey" id="9DH-r5-KTB"/>
                <outlet property="profileImage" destination="Dtf-OP-ul0" id="5en-rb-TlY"/>
                <outlet property="profileSuperView" destination="lMt-Hg-OOB" id="Yi2-Bi-tir"/>
            </connections>
            <point key="canvasLocation" x="-18.840579710144929" y="-94.084821428571431"/>
        </view>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
