//
//  EditProfileTextViewTableViewCell.swift
//  GameFlex
//
//  Created by <PERSON> on 10/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

class EditProfileTextViewTableViewCell: UITableViewCell {
    
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var textView: UITextView!
    @IBOutlet weak var characterCountLabel: UILabel!

    weak var delegate: ProfileDelegate?
    var editingProfile: Bool = true
    var editingChannel: Bool = false

    static var cellIdentifier = String(describing: EditProfileTextViewTableViewCell.self)
    
    override func awakeFromNib() {
        super.awakeFromNib()
        textView.delegate = self
        textView.tintColor = .gfGreen
        if editingProfile {
            textView.text = User.flexter.userDescription
        }
        characterCountLabel.text = "\(textView.text.count)"
        textView.layer.cornerRadius = 4.0
        textView.layer.borderWidth = 1.0
        textView.layer.borderColor = UIColor.gfGrayText.cgColor
        textView.backgroundColor = .gfDarkBackground
    }
    
    func configureCell() {
        if editingProfile {
            textView.text = User.flexter.userDescription
        }
        characterCountLabel.text = "\(textView.text.count)"
    }
    
}

extension EditProfileTextViewTableViewCell: UITextViewDelegate {
    
    func textViewShouldBeginEditing(_ textView: UITextView) -> Bool {
        delegate?.didBeginEditingTextView()
        return true
    }
    
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.text == "editProfile.textView.placeholder".localized || textView.text == "editChannel.textView.placeholder".localized, editingProfile {
            textView.text = ""
            textView.textColor = .white
        }
    }
    
    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text == "", editingProfile {
            textView.text = editingChannel ? "editChannel.textView.placeholder".localized : "editProfile.textView.placeholder".localized
            textView.textColor = .darkGray
        }
    }
    
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        guard text != "\n" else {
            return false
        }
        let result = (textView.text as NSString?)?.replacingCharacters(in: range, with: text) ?? text
        characterCountLabel.text = "\(result.count)"
        delegate?.updateTextFor(.userDescription, text: result)

        return true
    }
}
