//
//  ChannelReviewViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 1/26/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit

class ChannelReviewViewController: GFChannelViewController {
    
    @IBOutlet weak var tableView: UITableView!
    
    static func storyboardInstance() -> ChannelReviewViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(identifier: String(describing: ChannelReviewViewController.self))
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
    }

}

extension ChannelReviewViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        return UITableViewCell()
    }
    
    
}
