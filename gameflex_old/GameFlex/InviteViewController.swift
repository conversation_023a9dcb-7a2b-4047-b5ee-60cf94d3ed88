//
//  InviteViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 11/27/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

enum OpsType {
    case invite, unInvite
}

class InviteViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
    
    var channelDetail: ChannelDetail?
    var tableType: ChannelListType = .owner
    
    var available: [Channel] = []
    var pending: [Channel] = []
    var invited: [Channel] = []
    var subscribed: [Channel] = []
    
    var inviteeIndexPaths: [IndexPath] = []
    
    var opsQueue = OperationQueue()
    var opsInProgress: [OpsType] = []
    
    static func storyboardInstance() -> InviteViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(identifier: String(describing: InviteViewController.self)) as! InviteViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView()
        
        tableView.register(UINib(nibName: FollowingTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FollowingTableViewCell.cellIdentifier)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        if let name = channelDetail?.channelName {
            title = "\(name) Membership"
        } else {
            title = "Membership"
        }
        getFollowers(for: channelDetail?.channelId)
        prepareTheData()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if tableType == .owner {
            addInviteToNavBar()
        }
    }
        
    private func prepareTheData() {
        if let arr = channelDetail?.participants {
            invited = arr
        }
        if let barr = channelDetail?.pendingInvitees {
            pending = barr
        }
        if let carr = channelDetail?.followers {
            subscribed = carr
        }
        if let friends = User.flexter.friends, tableType == .owner {
            available = []
            if invited.isEmpty {
                available = friends
            } else {
                available = friends.filter{!invited.contains($0)}
            }
        }
        // update channelDetail
        tableView.reloadData()
    }
    
    // this intercepts the 'Update' nav bar button
    override func didTapNext() {
        // start the spinner
        Utilities.showSpinner()
        let deInvites = inviteeIndexPaths.filter({ $0.section != 0 })
        var arr: [String] = []
        for deInvite in deInvites {
            if let uid = invited[deInvite.row].channelId {
                arr.append(uid)
            }
        }
        if arr.count > 0, let channelId = channelDetail?.channelId {
            if !opsInProgress.contains(.unInvite) {
                opsInProgress.append(.unInvite)
            }
            GFNetworkServices.handleInvitesUninvites(invite: false, participants: arr, channelId: channelId, { (success, error) in
                self.checkTheOpsArray(.unInvite)
                guard error == nil else {
                    // handle uninvite error here
                    return
                }
            })
        }
        let invites = inviteeIndexPaths.filter({ $0.section == 0 })
        var arri: [String] = []
        for invite in invites {
            if let uid = available[invite.row].channelId {
                arri.append(uid)
            }
        }
        if arri.count > 0, let channelId = channelDetail?.channelId {
            if !opsInProgress.contains(.invite) {
                opsInProgress.append(.invite)
            }
            GFNetworkServices.handleInvitesUninvites(invite: true, participants: arri, channelId: channelId, { (success, error) in
                self.checkTheOpsArray(.invite)
                guard error == nil else {
                    // handle uninvite error here
                    return
                }
            })
        }
        checkTheOpsArray(nil)
    }
    
    func getFollowers(for channelId: String?) {
        if let channelId = channelId {
            Utilities.showSpinner()
            GFNetworkServices.getChannelFollowers(for: channelId) { (success, results, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    if success {
                        self.subscribed = results
                        self.channelDetail?.followers = self.subscribed
                        self.tableView.reloadData()
                    }
                }
            }
        }
    }
    
    private func checkTheOpsArray( _ opsType: OpsType?) {
        
        if let opsType = opsType, let index = opsInProgress.firstIndex(of: opsType) {
            opsInProgress.remove(at: index)
        }
        if opsInProgress.isEmpty {
            // go reload user profile
            self.didFinishWithUpdate()
        }
    }
    
    private func didFinishWithUpdate() {
        if let channelId = channelDetail?.channelId {
            GFNetworkServices.getChannelDetailFor(channelId: channelId) { (success, detail, error) in
                self.channelDetail = detail
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    self.prepareTheData()
                }
            }
        }
    }
    
    @objc func deleteThisChannel() {
        let alert = UIAlertController(title: "Delete this Channel?", message: "This removes this channel and content from GameFlex. This is not undo-able.", preferredStyle: .alert)
        let cancel = UIAlertAction(title: "Cancel", style: .cancel, handler: nil)
        let ok = UIAlertAction(title: "Delete", style: .destructive) { (action) in
            Utilities.showSpinner()
            if let cid = self.channelDetail?.channelId {
                GFNetworkServices.deleteChannel(channelId: cid) { (success, error) in
                    DispatchQueue.main.async {
                        guard error == nil else {
                            Utilities.hideSpinner()
                            self.showErrorAlert()
                            return
                        }
                    }
                    if success {
                        if let userId = User.userId, userId != "" {
                            DispatchQueue.main.async {
                                Utilities.showSpinner()
                            }
                            GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                                DispatchQueue.main.async {
                                    Utilities.hideSpinner()
                                }
                                guard error == nil else {
                                    return
                                }
                                if success {
                                    if let flexter = flexter {
                                        User.updateTheUser(flexter)
                                    }
                                    DispatchQueue.main.async {
                                        self.navigationController?.popViewController(animated: true)
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                self.showErrorAlert()
            }
        }
        alert.addAction(ok)
        alert.addAction(cancel)
        self.present(alert, animated: true, completion: nil)
    }
    
    private func showErrorAlert() {
        DispatchQueue.main.async {
            Utilities.hideSpinner()
            let alert = UIAlertController(title: "Error",
                                          message: "Something unexpected happened. Try again later.",
                                          preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
            alert.addAction(ok)
            self.present(alert, animated: true)
        }
    }
    
    private func isFollowing(_ flexterId: String?) -> Bool {
        if let flexterId = flexterId {
            let sum = (User.flexter.friends ?? []) + (User.flexter.following ?? [])
            return sum.filter({ $0.channelId == flexterId }).count > 0
        }
        return false
    }
    
}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension InviteViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        if tableType == .owner {
            return 6
        }
        return 5
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
//        switch tableType {
//        case .owner:
            switch section {
            case 0: return 1
            case 1: return available.count
            case 2: return pending.count
            case 3: return invited.count
            case 4: return subscribed.count
            default: return 0
//            }
//        default: return 0
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        guard let cell = tableView.dequeueReusableCell(withIdentifier: FollowingTableViewCell.cellIdentifier,
                                                       for: indexPath) as? FollowingTableViewCell else { return FollowingTableViewCell() }
        switch indexPath.section {
        case 0:
            if let owner = channelDetail?.channelOwner {
                cell.configureCell(following: isFollowing(owner.channelId), channel: owner)
                if let pic = owner.channelImage, pic != "" {
                    process(pic:pic, placeholder: FlexManager.randomImagePlaceholder(owner.channelId), cell: cell)
                } else {
                    cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(owner.channelId))
                    cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
                }
            }
        case 1:
            let friend = available[indexPath.row]
            cell.configureCell(following: true, channel: friend)
            if let pic = friend.channelImage, pic != "" {
                process(pic:pic, placeholder: FlexManager.randomImagePlaceholder(friend.channelId), cell: cell)
            } else {
                cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(friend.channelId))
                cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
            }
        case 2:
            let friend = pending[indexPath.row]
            cell.configureCell(following: isFollowing(friend.channelId), channel: friend)
            if let pic = friend.channelImage, pic != "" {
                process(pic:pic, placeholder: FlexManager.randomImagePlaceholder(friend.channelId), cell: cell)
            } else {
                cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(friend.channelId))
                cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
            }
        case 3:
            let channel = invited[indexPath.row]
            cell.configureCell(following: isFollowing(channel.channelId), channel: channel)
            if let pic = channel.channelImage, pic != "" {
                process(pic:pic, placeholder: FlexManager.randomImagePlaceholder(channel.channelId), cell: cell)
            } else {
                cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(channel.channelId))
                cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
            }
        case 4:
            let channel = subscribed[indexPath.row]
            cell.configureCell(following: isFollowing(channel.channelId), channel: channel)
            if let pic = channel.channelImage, pic != "" {
                process(pic:pic, placeholder: FlexManager.randomImagePlaceholder(channel.channelId), cell: cell)
            } else {
                cell.profileImage.image = UIImage(named: FlexManager.randomImagePlaceholder(channel.channelId))
                cell.profileImage.transform = CGAffineTransform(a: 0.5, b: 0, c: 0, d: 0.5, tx: 0, ty: 0)
            }

        default: break
        }
        cell.delegate = self
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let header = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 44))
        let label = UILabel(frame: CGRect(x: 16, y: 0, width: header.frame.size.width, height: 44))
        label.font = .boldSystemFont(ofSize: 13)
        label.numberOfLines = 0
        label.textColor = .gfGrayText
        header.addSubview(label)
        let spacer = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 44))
        spacer.backgroundColor = .gfDarkBackground40
        header.addSubview(spacer)
        let bottomLine = UIView(frame: CGRect(x: 0, y: 43, width: header.frame.size.width, height: 1.0))
        bottomLine.backgroundColor = .gfDarkGrayMask
        header.addSubview(bottomLine)
        header.clipsToBounds = true
        header.backgroundColor = .black
        label.backgroundColor = .gfDarkBackground40


        switch section {
        case 0:
            label.text = "Owner"
        case 1:
            label.text = "Invite these friends (They follow you, you follow them)"
        case 2:
            label.text = "Pending: invitee's haven't yet accepted (Remind them?)"
        case 3:
            label.text = "Remove Contributors"
        case 4:
            label.text = "Subscribers"
        default:
            label.text = "Delete this Channel"
            let butt = UIButton(frame: CGRect(x: view.frame.size.width - 100, y: 7.0, width: 90, height: 30))
            butt.setTitle("Delete", for: .normal)
            butt.titleLabel?.font = UIFont.boldSystemFont(ofSize: 17)
            butt.setTitleColor(.white, for: .normal)
            butt.backgroundColor = .red
            butt.layer.cornerRadius = 15.0
            butt.addTarget(self, action: #selector(deleteThisChannel), for: .touchUpInside)
            header.addSubview(butt)
        }
        return header
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        
        if inviteeIndexPaths.contains(indexPath) {
            inviteeIndexPaths.removeAll(where: {$0 == indexPath})
        } else {
            inviteeIndexPaths.append(indexPath)
        }
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 44
    }
    
    func process(pic: String, placeholder: String, cell: FollowingTableViewCell) {
        let url = URL(string: pic)
        let processor = DownsamplingImageProcessor(size: cell.profileImage?.bounds.size ?? CGSize(width: 40, height: 40))
        cell.profileImage?.kf.indicatorType = .activity
        cell.profileImage?.kf.setImage(
            with: url,
            placeholder: UIImage(named: placeholder),
            options: [
                .processor(processor),
                .scaleFactor(UIScreen.main.scale),
                .transition(.fade(1)),
                .cacheOriginalImage
            ], completionHandler:
                {
                    result in
                    switch result {
                    case .success: break
                    case .failure(let error):
                        print("Job failed: \(error.localizedDescription)")
                    }
                })
    }
}

extension InviteViewController: ProfileDelegate {
    
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        switch type {
        case .goToProfile:
            if let flexterId = object as? String {
                var flextr = Flexter()
                flextr.userId = flexterId
                let pvc = ProfileViewController.storyboardInstance()
                pvc.flexter = flextr
                title = "" // hides the 'back'
                navigationController?.pushViewController(pvc, animated: true)
            }

        default: break
        }
    }

}
