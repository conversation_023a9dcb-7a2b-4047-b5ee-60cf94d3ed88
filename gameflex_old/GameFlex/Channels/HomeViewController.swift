//
//  HomeViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 10/21/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import King<PERSON>er
import LocalAuthentication

enum HomeTableViewCells {
    case counter, caption
}

enum FeedType {
    case main, channels, myFeed
}

protocol HomeDelegate: AnyObject {
    func updateSideBar(objects: [SideBarObject])
    func didTapForProfileAction(flexterId: String)
    func didTapToFollow(_ sender: UIButton, _ tableViewCell: UITableViewCell?)
    func didTapToAcceptInvitation(_ notificationId: String)
}

extension HomeDelegate {
    func updateSideBar(objects: [SideBarObject]) { /* */ }
    func didTapForProfileAction(flexterId: String) { /* */ }
    func didTapToAcceptInvitation(_ notificationId: String) { /* */ }
}

class HomeViewController: GFChannelViewController {
        
    @IBOutlet weak var mainContainerView: UIView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var tableViewHeightConstraint: NSLayoutConstraint!
    @IBOutlet weak var flagTableView: UITableView!
    
    var commentArray: [CommentObject]?
    var caption: CaptionObject?
    var sequenceOfCells: [HomeTableViewCells] = [.counter, .caption]
    var shouldGetNewFlex = false
    var sourceViewController: UIViewController?
    
    var width = UIScreen.main.bounds.size.width
    var size = UIScreen.main.bounds.size
    var wasOnComments = false
    var showChezLui = false
    var chezLui: String = ""
    
    var skipTheTripToMyFeed = false
    
    // MARK: - life cycles
    static func storyboardInstance() -> HomeViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(identifier: String(describing: HomeViewController.self))
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.dataSource = self
        tableView.delegate = self
        tableView.isScrollEnabled = false
        flagTableView.dataSource = self
        flagTableView.delegate = self
        tableView.clipsToBounds = false
        flagTableView.isUserInteractionEnabled = false
        tableView.register(UINib(nibName: CounterTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: CounterTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: BreadcrumbTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: BreadcrumbTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: ProfileTopTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ProfileTopTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: BreadcrumbChannelTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: BreadcrumbChannelTableViewCell.cellIdentifier)
        flagTableView.register(UINib(nibName: FlagTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FlagTableViewCell.cellIdentifier)
        tableView.isHidden = true
        tableView.separatorStyle = .none
        // Make the navigation bar background
        navigationController?.navigationBar.setBackgroundImage(UIImage(), for: .default)
        navigationController?.navigationBar.shadowImage = UIImage()
        navigationController?.navigationBar.isTranslucent = true
        navigationController?.navigationBar.tintColor = .white
        if User.isLoggedIn {
            if showChezLui {
                let titleView = UIView(frame: CGRect(x: 0.0, y: 0.0, width: 279, height: 24))
                titleView.backgroundColor = .clear
                butt = UIButton(frame: CGRect(x: 109, y: 0, width: 63, height: 24))
                butt?.setTitle("\(chezLui)", for: .normal)
                butt?.applyPrimaryGFButtonStandards(true)
                butt?.tag = 101
                if let butt = butt {
                    titleView.addSubview(butt)
                    navButtons = [butt]
                }
                navigationItem.titleView = titleView
                navigationItem.setHidesBackButton(false, animated: true)
                tableView.isHidden = false

            } else if UIDevice.isSkinnyIPhone {
                let titleView = UIView(frame: CGRect(x: 0.0, y: 0.0, width: 279, height: 24))
                titleView.backgroundColor = .clear
                leftButt = UIButton(frame: CGRect(x: 0, y: 0, width: 101, height: 24))
                leftButt?.setTitle("My Feed", for: .normal)
                leftButt?.titleLabel?.font = .systemFont(ofSize: 15)
                leftButt?.layer.cornerRadius = 12
                leftButt?.tag = 100
                leftButt?.addTarget(self, action: #selector(didTap(_:)), for: .touchUpInside)
                butt = UIButton(frame: CGRect(x: 109, y: 0, width: 63, height: 24))
                butt?.alpha = 1.0
                butt?.setTitle("Prime", for: .normal)
                butt?.titleLabel?.font = .systemFont(ofSize: 15)
                butt?.layer.cornerRadius = 12
                butt?.tag = 101
                butt?.addTarget(self, action: #selector(didTap(_:)), for: .touchUpInside)
                rightButt = UIButton(frame: CGRect(x: 180, y: 0, width: 99, height: 24))
                rightButt?.setTitle("Channels", for: .normal)
                rightButt?.titleLabel?.font = .systemFont(ofSize: 15)
                rightButt?.layer.cornerRadius = 12
                rightButt?.tag = 102
                rightButt?.addTarget(self, action: #selector(didTap(_:)), for: .touchUpInside)
                if let rightButt = rightButt, let butt = butt, let leftButt = leftButt {
                    titleView.addSubview(rightButt)
                    titleView.addSubview(leftButt)
                    titleView.addSubview(butt)
                    navButtons = [leftButt, butt, rightButt]
                }
                navigationItem.titleView = titleView
                navigationItem.setHidesBackButton(true, animated: true)
                
                tableView.isHidden = false
            } else {
                let titleView = UIView(frame: CGRect(x: 0.0, y: 0.0, width: 279, height: 34))
                titleView.backgroundColor = .clear
                leftButt = UIButton(frame: CGRect(x: 0, y: 0, width: 101, height: 34))
                leftButt?.setTitle("My Feed", for: .normal)
                leftButt?.titleLabel?.font = .systemFont(ofSize: 15)
                leftButt?.layer.cornerRadius = 17
                leftButt?.tag = 100
                leftButt?.addTarget(self, action: #selector(didTap(_:)), for: .touchUpInside)
                butt = UIButton(frame: CGRect(x: 109, y: 0, width: 63, height: 34))
                butt?.alpha = 1.0
                butt?.setTitle("Prime", for: .normal)
                butt?.titleLabel?.font = .systemFont(ofSize: 15)
                butt?.layer.cornerRadius = 17
                butt?.tag = 101
                butt?.addTarget(self, action: #selector(didTap(_:)), for: .touchUpInside)
                rightButt = UIButton(frame: CGRect(x: 180, y: 0, width: 99, height: 34))
                rightButt?.setTitle("Channels", for: .normal)
                rightButt?.titleLabel?.font = .systemFont(ofSize: 15)
                rightButt?.layer.cornerRadius = 17
                rightButt?.tag = 102
                rightButt?.addTarget(self, action: #selector(didTap(_:)), for: .touchUpInside)
                if let rightButt = rightButt, let butt = butt, let leftButt = leftButt {
                    titleView.addSubview(rightButt)
                    titleView.addSubview(leftButt)
                    titleView.addSubview(butt)
                    navButtons = [leftButt, butt, rightButt]
                }
                navigationItem.titleView = titleView
                navigationItem.setHidesBackButton(true, animated: true)
                
                tableView.isHidden = false

            }
        }

    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        Utilities.hideSpinner()
        view.subviews.filter({ $0.isKind(of: SideBarView.self)}).forEach({ $0.removeFromSuperview() })
        if shouldGetNewFlex, let vc = self.children.first {
            shouldGetNewFlex = false
            FeedViewModel.getNewFlex(vc: vc)
        }
        tableView.reloadData()
        flagTableView.reloadData()
    }
        
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        Utilities.hideSpinner()
        if !showChezLui && children.count == 1 && !skipTheTripToMyFeed {
            addTheViewControllers()
        }
        if skipTheTripToMyFeed, !children.filter({ $0.isKind(of: ChannelDirectoryViewController.self )}).isEmpty { // its the channel directory folks
            tableView.isHidden = true
            sideBarView?.isHidden = true
        }
        if view.subviews.filter({ $0.isKind(of: SideBarView.self)}).count == 0, !skipTheTripToMyFeed {
            handleSideBarView()
        }
        let long = UILongPressGestureRecognizer(target: self, action: #selector(didLongPress(_:)))
        view.addGestureRecognizer(long)
        skipTheTripToMyFeed = false
    }
    
    func didTapChannelClose() {
        
    }
    
    func handleSideBarView() {
        if User.isLoggedIn {
            view.subviews.filter({ $0.isKind(of: SideBarView.self)}).forEach({ $0.removeFromSuperview()})
            let chezLuiDummyTabBarHeight = showChezLui ? FeedViewModel.chezLuiDummyTabBarHeight : 0
            sideBarView = SideBarView(frame: CGRect(x: 0,
                                                    y: view.frame.size.height-60-chezLuiDummyTabBarHeight,
                                                    width: view.frame.size.width,
                                                    height: 54.0))
            sideBarView?.delegate = self
            view.addSubview(sideBarView!)
            view.bringSubviewToFront(sideBarView!)
            tableView.isHidden = false
        }
        lightUpPlayButton(false)
    }
    
    func addTheViewControllers() {
        if User.isLoggedIn {
            if let but = leftButt {
                self.didTap(but)
                but.alpha = 1.0
                but.backgroundColor = .gfGreen
                but.setTitleColor(.black, for: .normal)
                let otherButtons: [UIButton] = navButtons.filter({ $0.tag != but.tag })
                handleOtherButtons(otherButtons)
            }
        } else {
            centerizeMain()
        }
        handleSideBarView()
    }
    
    func hideTheControls(_ hide: Bool = false) {
        if hide {
            tableView.isHidden = true
            sideBarView?.isHidden = true
            flagTableView.isHidden = true
            return
        }
        tableView.isHidden = false
        sideBarView?.isHidden = false
        flagTableView.isHidden = false
    }
    
    
    
    // MARK: - Navigation-class Action funcs
    
    func centerizeMain() {
        hideTheControls(false)
        let newVC = MainViewController.storyboardInstance()
        let oldVC = children[0]
        oldVC.willMove(toParent: nil)
        self.addChild(newVC)
        if children[0].isKind(of: MyFeedViewController.self) {
            // it's the following -> main
            newVC.view.frame = CGRect(x: width, y: 0, width: width, height: self.view.frame.size.height)
            let endFrame = CGRect(x: -width, y: 0, width: width, height: self.view.frame.size.height)
            self.transition(from: oldVC, to: newVC, duration: 0.25, options: .curveEaseOut) {
                newVC.view.frame = oldVC.view.frame
                oldVC.view.frame = endFrame
            } completion: { (ended) in
                oldVC.removeFromParent()
                newVC.didMove(toParent: self)
            }
        } else if (children[0].isKind(of: ChannelDirectoryViewController.self) ||
        children[0].isKind(of: ChannelViewController.self)) {
            // it's the channels -> main
            oldVC.willMove(toParent: nil)
            self.addChild(newVC)
            newVC.view.frame = CGRect(x: -width, y: 0, width: width, height: self.view.frame.size.height)
            let endFrame = CGRect(x: 2*width, y: 0, width: width, height: self.view.frame.size.height)
            self.transition(from: oldVC, to: newVC, duration: 0.25, options: .curveEaseOut) {
                newVC.view.frame = oldVC.view.frame
                oldVC.view.frame = endFrame
            } completion: { (ended) in
                oldVC.removeFromParent()
                newVC.didMove(toParent: self)
                FeedViewModel.makeTheLastSeenCall() // sends last seen channel details to server
            }
        } else { // must be first time
            if children.count == 0 {
                let mvc = MainViewController.storyboardInstance()
                addChild(mvc)
                mainContainerView.addSubview(mvc.view)
                mvc.didMove(toParent: self)
            }
        }
    }
    
    func centerizeFollowing() {
        hideTheControls(false)
        let newVC = MyFeedViewController.storyboardInstance()
        let oldVC = children[0]
        oldVC.willMove(toParent: nil)
        self.addChild(newVC)
        newVC.view.frame = CGRect(x: -width, y: 0, width: width, height: self.view.frame.size.height)
        let endFrame = CGRect(x: 2*width, y: 0, width: width, height: self.view.frame.size.height)
        self.transition(from: oldVC, to: newVC, duration: 0.25, options: .curveEaseOut) {
            newVC.view.frame = oldVC.view.frame
            oldVC.view.frame = endFrame
        } completion: { (ended) in
            oldVC.removeFromParent()
            newVC.didMove(toParent: self)
            if let _ = oldVC as? ChannelViewController {
                FeedViewModel.makeTheLastSeenCall() // sends last seen channel details to server
            }
        }        
    }
    
    func centerizeChannels() {
        hideTheControls(true)
        if AppInfo.channelsAreActive {
            let newVC = ChannelDirectoryViewController.storyboardInstance()
            let oldVC = children[0]
            oldVC.willMove(toParent: nil)
            self.addChild(newVC)
            newVC.view.frame = CGRect(x: width, y: 0, width: width, height: self.view.frame.size.height)
            let endFrame = CGRect(x: -width, y: 0, width: width, height: self.view.frame.size.height)
            self.transition(from: oldVC, to: newVC, duration: 0.25, options: .curveEaseOut) {
                newVC.view.frame = oldVC.view.frame
                oldVC.view.frame = endFrame
            } completion: { (ended) in
                oldVC.removeFromParent()
                newVC.didMove(toParent: self)
            }
        } else {
            GFSpinnerView.showIn(view: self.view, message: "channels.off".localized)
        }
    }
    
    // puts the reflexViewController into the embedded view
    func showReflexContent(for flexId:String) {
        hideTheControls(false)
        if let userId = User.userId {
            User.parentsWithReflexesSeen[userId]?.append(flexId)
        }
        DispatchQueue.main.async {
            let newVC = ReflexViewController.storyboardInstance()
            newVC.flexId = flexId
            newVC.numberOfReflexes = self.caption?.numberOfReflexes ?? 0
            let oldVC = self.children[0]
            self.sourceViewController = oldVC
            oldVC.willMove(toParent: nil)
            self.addChild(newVC)
            newVC.view.frame = CGRect(x: self.width, y: 0, width: self.width, height: self.view.frame.size.height)
            let endFrame = CGRect(x: -self.width, y: 0, width: self.width, height: self.view.frame.size.height)
            self.transition(from: oldVC, to: newVC, duration: 0.25, options: .curveEaseOut) {
                newVC.view.frame = oldVC.view.frame
                oldVC.view.frame = endFrame
            } completion: { (ended) in
                oldVC.removeFromParent()
                newVC.didMove(toParent: self)
            }
        }
    }
    
    // returns to the source of the reflexViewController
    func returnToSourceViewController() {
        DispatchQueue.main.async {
            if let newVC = self.sourceViewController {
                let oldVC = self.children[0]
                oldVC.willMove(toParent: nil)
                self.addChild(newVC)
                newVC.view.frame = CGRect(x: -self.width, y: 0, width: self.width, height: self.view.frame.size.height)
                let endFrame = CGRect(x: self.width, y: 0, width: self.width, height: self.view.frame.size.height)
                self.transition(from: oldVC, to: newVC, duration: 0.25, options: .curveEaseOut) {
                    newVC.view.frame = CGRect(x: 0, y: 0, width: self.width, height: self.view.frame.size.height)
                    oldVC.view.frame = endFrame
                } completion: { (ended) in
                    oldVC.removeFromParent()
                    newVC.didMove(toParent: self)
                }
            }
        }
    }
    
    // channel directory selection of channel
    func showTheChannel() {
        hideTheControls(false)
        if let channelId = FeedViewModel.channelId {
            Utilities.showSpinner()
            GFNetworkServices.getChannelDetailFor(channelId: channelId) { (success, channelDetail, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if success {
                    DispatchQueue.main.async {
                        FeedViewModel.channelDetail = channelDetail
                        let vc = ChannelViewController.storyboardInstance()
                        let oldVC = self.children[0]
                        oldVC.willMove(toParent: nil)
                        self.addChild(vc)
                        vc.view.frame = CGRect(x: -self.width, y: 0, width: self.width, height: self.view.frame.size.height)
                        let endFrame = CGRect(x: 2*self.width, y: 0, width: self.width, height: self.view.frame.size.height)
                        self.transition(from: oldVC, to: vc, duration: 0.25, options: .curveEaseOut) {
                            vc.view.frame = oldVC.view.frame
                            oldVC.view.frame = endFrame
                        } completion: { (ended) in
                            oldVC.removeFromParent()
                            vc.didMove(toParent: self)
                        }
                    }
                } else {
                    print("error in home.showTheChannel:\(error?.localizedDescription ?? "info not available.")")
                }
            }
        }
    }
    
    private func flashTheFlags() {
        if (caption?.numberOfReflexes ?? 0 > 0 || caption?.numberOfComments ?? 0 > 0) {
            if let cell = flagTableView.cellForRow(at: IndexPath(row: 0, section: 0)) as? FlagTableViewCell {
                cell.animateTheFlags()
            }
        }
    }
        
    func handleTuple(_ tuple: FeedObject = FeedViewModel.lastSeenTuple) {
        switch tuple.feedType {
        case .main:
            if let but = butt {
                didTap(but)
            }
        case .channels:
            if let but = rightButt {
                didTap(but)
            }
        case .myFeed:
            if let but = leftButt {
                didTap(but)
            }
        }
    }
    
    @objc func didLongPress(_ sender: UILongPressGestureRecognizer) {
        guard children.first as? ChannelDirectoryViewController == nil else { return }
        if sender.state == .began {
            navigationController?.setNavigationBarHidden(true, animated: true)
            sideBarView?.alpha = 0.0
            tableView?.alpha = 0.0
        }
        if sender.state == .ended {
            navigationController?.setNavigationBarHidden(false, animated: true)
            sideBarView?.alpha = 1.0
            tableView?.alpha = 1.0

        }
    }
    
    // MARK: - helpers
    
    @objc func showProfileForFlexter() {
        let pvc = ProfileViewController.storyboardInstance()
        var flexter = Flexter()
        flexter.userId = caption?.captionSourceId
        pvc.flexter = flexter
        pvc.isModal = true
        let nc = GFNavigationController(rootViewController: pvc)
        title = ""
        nc.modalPresentationStyle = .overCurrentContext
        tabBarController?.present(nc, animated: true)
    }
    
    func lightUpPlayButton(_ on: Bool) {
        if on {
            sideBarView?.highlight(this: .play, true)
            sideBarView?.playIcon.image = #imageLiteral(resourceName: "backReflex")
        } else {
            sideBarView?.highlight(this: .play, false)
            sideBarView?.playIcon.image = #imageLiteral(resourceName: "play")
        }
    }
    
    // for Reflexes in progress shows the CounterTableViewCell's collectionView and runs through the animations
    func doCounterTimers(for cRow: Int) {
        
        if let vc = children[0] as? ReflexViewController,
           let flexId = FeedViewModel.getFlexId(vc: vc),
           let row = FeedViewModel.getRow(of: flexId, vc: vc),
           let cell = tableView.cellForRow(at: IndexPath(row: 0, section: 0)) as? CounterTableViewCell {
            for drow in 0..<cRow {
                if let cella = cell.collectionView.cellForItem(at: IndexPath(row: drow, section: 0)) as? CounterCollectionViewCell {
                    cella.animated(cell.thickLineMaxLength)
                }
            }
            if let celler = cell.collectionView.cellForItem(at: IndexPath(row: row, section: 0)) as? CounterCollectionViewCell {
                celler.animateThis(cell.thickLineMaxLength)
            }
        }
    }
    
    func doActionSheet() {
        let actionSheet = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        actionSheet.view.tintColor = .white
        let report = UIAlertAction(title: "Report", style: .destructive) { (report) in
            self.sideBarView?.highlight(this: .share, false)
            let bvc = BugViewController.storyboardInstance()
            self.title = ""
            self.navigationController?.pushViewController(bvc, animated: true)
        }
        let copyLink = UIAlertAction(title: "Copy Link", style: .default) { (copyLink) in
            self.sideBarView?.highlight(this: .share, false)

            // copy link means ?
            DispatchQueue.main.async {
                let alert = UIAlertController(title: "In Development...", message: "Sorry, but we're not finished with this yet.", preferredStyle: .alert)
                let ok = UIAlertAction(title: "OK", style: .default)
                alert.addAction(ok)
                self.present(alert, animated: true, completion: nil)
            }
        }
        let share = UIAlertAction(title: "Share to...", style: .default) { (share) in
            self.sideBarView?.highlight(this: .share, false)

            var flexImage: [UIImage] = []
            if let mvc = self.children.first as? MainViewController {
                let cell = mvc.tableView.visibleCells.first as? FlexTableViewCell
                if let im = cell?.flexView?.image {
                    flexImage = [im]
                }
            } else if let cvc = self.children.first as? ChannelViewController {
                let cell = cvc.collectionView.visibleCells.first as? FlexCollectionViewCell
                if let im = cell?.flexImageView?.image {
                    flexImage = [im]
                }
            } else if let fvc = self.children.first as? MyFeedViewController {
                let cell = fvc.tableView.visibleCells.first as? FlexTableViewCell
                if let im = cell?.flexView?.image {
                    flexImage = [im]
                }
            } else if let rvc = self.children.first as? ReflexViewController {
                rvc.stopTimers()
                let cell = rvc.collectionView.visibleCells.first as? FlexCollectionViewCell
                if let im = cell?.flexImageView?.image {
                    flexImage = [im]
                }
            } else if let chez = self.children.first as? ChezLuiViewController {
                let cell = chez.tableView.visibleCells.first as? FlexTableViewCell
                if let im = cell?.flexView?.image {
                    flexImage = [im]
                }

            }
            if let im = flexImage.first {
                let avc = UIActivityViewController(activityItems: [im, "I saw this on the GameFlex app."], applicationActivities: nil)
                if #available(iOS 13.0, *) {
                    avc.activityItemsConfiguration = [UIActivity.ActivityType.message] as? UIActivityItemsConfigurationReading
                } else {
                    // Fallback on earlier versions
                }
                avc.excludedActivityTypes = [UIActivity.ActivityType.addToReadingList]
                avc.popoverPresentationController?.sourceView = self.view
                self.present(avc, animated: true, completion: nil)
                
                avc.completionWithItemsHandler = { (activityType: UIActivity.ActivityType?, completed:
                Bool, arrayReturnedItems: [Any]?, error: Error?) in
                    self.sideBarView?.highlight(this: .share, false)
                    if completed {
                        print("share completed")
                        return
                    } else {
                        print("cancel")
                    }
                    if let shareError = error {
                        print("error while sharing: \(shareError.localizedDescription)")
                    }
                }

            }

        }
        let addToChannel = UIAlertAction(title: "Add To Channel", style: .default) { (add) in
            self.sideBarView?.highlight(this: .share, false)
            // add means?
            DispatchQueue.main.async {
                let alert = UIAlertController(title: "In Development...", message: "Sorry, but we're not finished with this yet.", preferredStyle: .alert)
                let ok = UIAlertAction(title: "OK", style: .default)
                alert.addAction(ok)
                self.present(alert, animated: true, completion: nil)
            }
        }
        let unfollow = UIAlertAction(title: "UnFollow", style: .default) { (mute) in
            self.sideBarView?.highlight(this: .share, false)
            // mute = unfollow this person
            if let sourceId = self.caption?.captionSourceId {
                GFNetworkServices.unFollowThis([sourceId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            let alert = UIAlertController(title: "Muted", message: "This flexter has been unfollowed.", preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default)
                            alert.addAction(ok)
                            self.present(alert, animated: true, completion: nil)
                        }
                    }
                }
            }
        }
        let follow = UIAlertAction(title: "Follow", style: .default) { (follow) in
            self.sideBarView?.highlight(this: .share, false)
            // follow this person
            if let sourceId = self.caption?.captionSourceId {
                GFNetworkServices.followThis([sourceId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            let alert = UIAlertController(title: "Followed", message: "This flexter has been followed.", preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default)
                            alert.addAction(ok)
                            self.present(alert, animated: true, completion: nil)
                        }
                    } else {
                        DispatchQueue.main.async {
                            let alert = UIAlertController(title: "Followed", message: "This flexter has been followed.", preferredStyle: .alert)
                            let ok = UIAlertAction(title: "OK", style: .default)
                            alert.addAction(ok)
                            self.present(alert, animated: true, completion: nil)
                        }
                    }
                    
                }

            }
        }
        let cancel = UIAlertAction(title: "Cancel", style: .cancel) { (cancel) in
            self.sideBarView?.highlight(this: .share, false)
            
        }
        var arr: [UIAlertAction] = [report, copyLink, share, addToChannel, unfollow, follow, cancel]
        
        if let sourceId = self.caption?.captionSourceId {
            if User.flexter.following?.filter({ $0.channelId == sourceId }).isEmpty ?? true {
                arr = [report, copyLink, share, addToChannel, follow, cancel]
            } else if sourceId == User.userId {
                arr = [report, copyLink, share, addToChannel, cancel]
            } else {
                arr = [report, copyLink, share, addToChannel, unfollow, cancel]
            }
        }
        
        arr.forEach({ actionSheet.addAction($0) })
        self.present(actionSheet, animated: true, completion: nil)
    }
    
    private func makeTheReflex() {
        if GFDefaults.shared.hasAskedCameraPermission && GFDefaults.shared.hasAskedLibraryPermission /*&& GFDefaults.shared.hasAskedMicrophonePermission*/ && GFDefaults.shared.hasAskedPushPermission {
            let cvc = CameraViewController.storyboardInstance()
            CameraViewModel.state = .buttons
        
            navigationController?.pushViewController(cvc, animated: true)
        } else {
            let ttfvc = TryingToFlexViewController.storyboardInstance()
            ttfvc.delegate = self
            present(ttfvc, animated: true)
        }
    }
}

// MARK: - Channel Delegate services
extension HomeViewController: ChannelDelegate {
    
    func didTapForChannelAction(_ action: ChannelAction) {
        switch action {
        
        case .more:
            sideBarView?.highlight(this: .share, true)
            doActionSheet()
        case .like:
            if let vc = children.first {
                FeedViewModel.processLikeUnlikeFor(vc: vc)
            }
        case .comment:
            let covc = CommentViewController.storyboardInstance()
            let nc = GFNavigationController(rootViewController: covc)
            covc.delegate = self
            if let vc = children.first, let flexId = FeedViewModel.getFlexId(vc: vc) {
                covc.flexId = flexId
                if let rvc = vc as? ReflexViewController {
                    rvc.stopTimers()
                }
                present(nc, animated: true, completion: nil)
            }
        case .reflex:
            shouldGetNewFlex = true
            if let vc = children.first, let flexId = FeedViewModel.getFlexId(vc: vc) {
                CameraViewModel.isReflex = true
                CameraViewModel.parentFlexId = flexId
                if let _ = vc as? ChezLuiViewController {
                    makeTheReflex()
                } else if let _ = vc as? ChannelViewController {
                    FinalizeViewModel.channelId = FeedViewModel.channelId
                    makeTheReflex()
                }
                makeTheReflex()
            }
        case .play:
            lightUpPlayButton(true)
            if let vc = children.first, let flexId = FeedViewModel.getFlexId(vc: vc), flexId != "" {
                if let rvc = vc as? ReflexViewController {
                    rvc.stopTimers()
                    returnToSourceViewController()
                    lightUpPlayButton(false)
                } else {
                    showReflexContent(for: flexId)
                }
            }
        case .caption: break

        case .goToTop:
            if let mvc = children.first as? MainViewController {
                mvc.tableView.scrollToRow(at: IndexPath(row: 0, section: 0), at: .top, animated: true)
                mvc.currentRow = 0
                mvc.refreshChannel(top: true)
            } else if let cvc = children.first as? ChannelViewController {
                cvc.collectionView.scrollToItem(at: IndexPath(row: 0, section: 0), at: .top, animated: true)
                cvc.currentRow = 0
                cvc.refreshChannel(top: true)
            } else if let fvc = children.first as? MyFeedViewController {
                fvc.tableView.scrollToRow(at: IndexPath(row: 0, section: 0), at: .top, animated: true)
                fvc.currentRow = 0
                fvc.refreshChannel(top: true)
            } else if let chez = children.first as? ChezLuiViewController { // no home button available in this presentation, but just in case
                chez.tableView.scrollToRow(at: IndexPath(row: 0, section: 0), at: .top, animated: true)
                chez.currentRow = 0
                chez.refreshChannel(top: true)

            }
        }
    }
}

// MARK: - UITableView Delegate and DataSource

extension HomeViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard tableView != flagTableView else {
            return 1
        }
        return sequenceOfCells.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == flagTableView {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: FlagTableViewCell.cellIdentifier, for: indexPath) as? FlagTableViewCell else { return FlagTableViewCell() }
            cell.selectionStyle = .none
            cell.backgroundColor = .clear
            cell.isUserInteractionEnabled = false
            cell.shouldDisableTouches = true
            cell.clipsToBounds = true
            cell.configureCell(numberOfReflexes: caption?.numberOfReflexes ?? 0, numberOfComments: caption?.numberOfComments ?? 0)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                self.flashTheFlags()
            }
            return cell
        }
        
        switch sequenceOfCells[indexPath.row] {
        case .counter:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: CounterTableViewCell.cellIdentifier, for: indexPath) as? CounterTableViewCell else { return CounterTableViewCell() }
            cell.selectionStyle = .none
            cell.isUserInteractionEnabled = false
            cell.backgroundColor = .clear
            cell.tableViewWidth = view.frame.size.width
            var reflexCount = caption?.numberOfReflexes ?? 0
            if let rvc = children.first as? ReflexViewController {
                reflexCount = rvc.numberOfReflexes
            }
            cell.numberOfReflexes = reflexCount > 0 ? reflexCount : 1
            cell.collectionView.isHidden = !(reflexCount > 0)
            cell.collectionView.reloadData()
            if let vc = children[0] as? ReflexViewController,
               let flexId = FeedViewModel.getFlexId(vc: vc),
               let row = FeedViewModel.getRow(of: flexId, vc: vc) {
                for drow in 0..<row {
                    if let cella = cell.collectionView.cellForItem(at: IndexPath(row: drow, section: 0)) as? CounterCollectionViewCell {
                        cella.animated(cell.thickLineMaxLength)
                    }
                }
                if let celler = cell.collectionView.cellForItem(at: IndexPath(row: row, section: 0)) as? CounterCollectionViewCell {
                    celler.animateThis(cell.thickLineMaxLength)
                }
            }
            return cell
        case .caption:
            if let _ = FeedViewModel.channelId, let _ = children.first as? ChannelViewController {
                guard let cell = tableView.dequeueReusableCell(withIdentifier: BreadcrumbChannelTableViewCell.cellIdentifier, for: indexPath) as? BreadcrumbChannelTableViewCell else { return BreadcrumbChannelTableViewCell() }
                cell.selectionStyle = .none
                cell.isUserInteractionEnabled = true
                cell.backgroundColor = .clear
                cell.configureCellForChannel()
                cell.channelName.textColor = .gfOffWhite
                cell.channelImageView.layer.cornerRadius = cell.channelImageView.frame.size.width/2
                cell.profileImageView.layer.cornerRadius = cell.profileImageView.frame.size.width/2
                
                cell.flexterName.text = caption?.captionFlexterName ?? ""
                if caption?.captionFlexterName == User.flexter.flexterName {
                    cell.flexterName.textColor = .gfGreen
                } else {
                    cell.flexterName.textColor = .gfOffWhite
                }
                cell.commentTextView.text = caption?.caption
                cell.statsLabel.text = caption?.stats

                cell.delegate = self
                
                let isFollowing = User.flexter.following?.filter({$0.channelId == caption?.captionSourceId}).first != nil
                cell.configureCell(following: isFollowing, urlString: caption?.profileImage, flexterId: caption?.captionSourceId)
                cell.profileImageView.layer.cornerRadius = cell.profileImageView.frame.size.width/2

                return cell
            }
            guard let cell = tableView.dequeueReusableCell(withIdentifier: BreadcrumbTableViewCell.cellIdentifier, for: indexPath) as? BreadcrumbTableViewCell else { return BreadcrumbTableViewCell() }
            cell.selectionStyle = .none
            cell.isUserInteractionEnabled = true
            cell.backgroundColor = .clear
            
            cell.flexterName.text = caption?.captionFlexterName ?? ""
            if caption?.captionFlexterName == User.flexter.flexterName {
                cell.flexterName.textColor = .gfGreen
            } else {
                cell.flexterName.textColor = .gfOffWhite
            }
            cell.commentTextView.text = caption?.caption
            cell.statsLabel.text = caption?.stats

            cell.profileImageView.layer.cornerRadius = cell.profileImageView.frame.size.width/2
            
            cell.delegate = self
            
            let isFollowing = User.flexter.following?.filter({$0.channelId == caption?.captionSourceId}).first != nil
            cell.configureCell(following: isFollowing, urlString: caption?.profileImage, flexterId: caption?.captionSourceId)
            cell.profileImageView.layer.cornerRadius = cell.profileImageView.frame.size.width/2

            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard tableView != flagTableView else {
            return 78
        }
        switch sequenceOfCells[indexPath.row] {
        case .counter: return 5
        case .caption:
            return 44
//            if let _ = FeedViewModel.channelId, let _ = children.first as? ChannelViewController {
//                return 52
//            }
//        return UITableView.automaticDimension
        }
    }
    
    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 68
    }
    
}

// MARK: - HomeDelegate

extension HomeViewController: HomeDelegate {
    
    func updateSideBar(objects: [SideBarObject] ) {
        if let cell = tableView.cellForRow(at: IndexPath(row: 0, section: 0)) as? BreadcrumbTableViewCell, !objects.isEmpty, objects[0].row == 0 {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                cell.profileImageView.layer.cornerRadius = 20
            }
        }
            for object in objects {
                switch object.buttonType {
                case .more:
                    DispatchQueue.main.async {
                        self.sideBarView?.shareIcon.image = object.isHighlighted ? #imageLiteral(resourceName: "dotsSelected") : #imageLiteral(resourceName: "dots")
                    }
                case .like:
                    DispatchQueue.main.async {
                        self.sideBarView?.likeLabel.text = object.buttonCount > 0 ? "\(object.buttonCount)" : "Like".localized
                        self.sideBarView?.highlight(this: .like, object.isHighlighted)
                        let commentCount = Int(self.sideBarView?.commentLabel.text ?? "0") ?? 0
                        self.caption?.numberOfComments = commentCount
                    }
                case .play:
                    DispatchQueue.main.async {
                        self.sideBarView?.hasReflexes(!object.isHidden)
                        self.sideBarView?.reflexPresentLight.isHidden = !(object.isHighlighted && !object.isHidden)
                    }
                case .reflex:
                    if let rvc = (children[0] as? ReflexViewController), object.restartTimers {
                        DispatchQueue.main.async {
                            rvc.doTimer()
                            return
                        }
                    }
                    DispatchQueue.main.async {
                        if object.isHidden {
                            self.sideBarView?.reflexView.isHidden = true
                            self.sideBarView?.reflexIcon.isHidden = true
                            self.sideBarView?.reflexLabel.isHidden = true
                        } else {
                            self.sideBarView?.reflexView.isHidden = false
                            self.sideBarView?.reflexIcon.isHidden = false
                            self.sideBarView?.reflexLabel.isHidden = false
                        }
                        self.sideBarView?.reflexLabel.text = object.buttonCount > 0 ? "\(object.buttonCount)" : "Reflex".localized
                        self.caption?.numberOfReflexes = object.buttonCount
                        if !(self.children.first?.isKind(of: ReflexViewController.self) ?? false) {
                            self.tableView.reloadData()
                            self.flagTableView.reloadData()
                        }
                    }
                case .caption:
                    if object.captionData != nil {
                        self.caption = object.captionData
                        DispatchQueue.main.async {
                            if (self.children.first?.isKind(of: ReflexViewController.self) ?? false) {
                                self.tableView.reloadRows(at: [IndexPath(row: 1, section: 0)], with: .none)
                            } else {
                                self.tableView.reloadData()
                                self.flagTableView.reloadData()
                            }
                        }
                    }
                case .comment:
                    if wasOnComments {
                        wasOnComments = false
                        if let vc = children.first {
                            FeedViewModel.getNewFlex(vc: vc)
                        }
                    } else {
                        DispatchQueue.main.async {
                            self.sideBarView?.commentLabel.text = object.buttonCount > 0 ? "\(object.buttonCount)" : "Comment".localized
                            let commentCount = object.buttonCount
                            self.caption?.numberOfComments = commentCount
                        }
                    }
                default: break
                }
            }
    }
    
    func didTapToFollow(_ sender: UIButton, _ tableViewCell: UITableViewCell?) {
        if let cell = sender.superview as? BreadcrumbTableViewCell, let captionSourceId = caption?.captionSourceId {
            Utilities.showSpinner()
            if sender.backgroundColor == .gfGreen {
                GFNetworkServices.followThis([captionSourceId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            cell.configureCell(following: true, urlString: self.caption?.profileImage, flexterId: self.caption?.captionSourceId)
                        }
                    }
                }
            } else {
                GFNetworkServices.unFollowThis([captionSourceId]) { (success, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if success {
                        DispatchQueue.main.async {
                            cell.configureCell(following: false, urlString: self.caption?.profileImage, flexterId: self.caption?.captionSourceId)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - Profile Delegate
extension HomeViewController: ProfileDelegate {
    
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        if type == .goToProfile {
            showProfileForFlexter()
        } else if type == .goToChannelReview {
            let cc = CreateChannelViewController.storyboardInstance()
            cc.isModal = true
            let nc = GFNavigationController(rootViewController: cc)
            title = ""
            nc.modalPresentationStyle = .overCurrentContext
            tabBarController?.present(nc, animated: true)

        } else if type == .goToChannelDirectory {
            centerizeChannels()
        }
    }
}

extension HomeViewController: PermissionsDelegate {
    func didFinishWithPermissions() {
        didTapForChannelAction(.reflex)
    }
}
