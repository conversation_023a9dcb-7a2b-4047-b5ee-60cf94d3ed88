//
//  FeedViewModel.swift
//  GameFlex
//
//  Created by <PERSON> on 1/9/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

struct FeedViewModel {
    
    static let shared = FeedViewModel()
    
    static var myFeedFlexArray: [Flex] = []
    
    static var primeFlexArray: [Flex] = []
    
    static var channelFlexArray: [Flex] = []
    
    static var channelId: String?
    static var channelDetail: ChannelDetail? // consumed by HomeViewController
    
    static func getTheMain(top: Bool, _ closure: @escaping(_ success: Bool, _ error: Error?) -> Void) {
        GFNetworkServices.getMainFlexes(top: top, { (success, result, ts, error) in
            if !result.isEmpty {
                if top {
                    self.primeFlexArray = []
                }
                result.forEach({ self.getTheImage(flex: $0)})
                if self.primeFlexArray.filter( { $0.flexId == result[0].flexId }).isEmpty {
                    self.primeFlexArray.append(contentsOf: result)
                }
                if let _ = GFDefaults.channelTimeStamps, let time = ts {
                    GFDefaults.channelTimeStamps?[GFNetworkServices.kMainChannel] = time
                } else if let time = self.primeFlexArray.last?.createAt {
                    GFDefaults.channelTimeStamps = [GFNetworkServices.kMainChannel: time]
                }
                closure(success, error)
            }
        })
    }
    
    static func getMyFeed(top: Bool, _ closure: @escaping(_ success: Bool, _ isEmpty: Bool, _ error: Error?) -> Void) {
        if let channelId = User.userId {
            GFNetworkServices.getChannelFlexes(channelId: channelId, top: top, { (success, result, error) in
                if !result.isEmpty {
                    if top {
                        self.myFeedFlexArray = []
                    }
                    result.forEach({ self.getTheImage(flex: $0)})
                    var resul = result.filter({ $0.flexId != nil })
                    resul = resul.filter({ $0.createAt != nil })
                    self.myFeedFlexArray.append(contentsOf: resul)
                    self.myFeedFlexArray = Array(Set(self.myFeedFlexArray))
                    self.myFeedFlexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    if let channelId = User.userId, let _ = GFDefaults.channelTimeStamps, let tim = self.myFeedFlexArray.last?.createAt {
                        GFDefaults.channelTimeStamps?[channelId] = tim
                    } else if let channelId = User.userId, let ti = self.myFeedFlexArray.last?.createAt {
                        GFDefaults.channelTimeStamps = [channelId: ti]
                    }
                }
                closure(success, result.isEmpty, error)
            })
        }
    }
    
    static func getTheChannel(top: Bool, _ closure: @escaping(_ success: Bool, _ isEmpty: Bool, _ error: Error?) -> Void) {
        if let channelId = channelId {
            if top {
                self.channelFlexArray = []
            }
            GFNetworkServices.getChannelFlexes(channelId: channelId, top: top, { (success, result, error) in
                if !result.isEmpty {
                    if top {
                        self.myFeedFlexArray = []
                    }
                    result.forEach({ self.getTheImage(flex: $0)})
                    var resul = result.filter({ $0.flexId != nil })
                    resul = resul.filter({ $0.createAt != nil })
                    self.channelFlexArray.append(contentsOf: resul)
                    self.channelFlexArray = Array(Set(self.channelFlexArray))
                    self.channelFlexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    if let channelId = self.channelId, let _ = GFDefaults.channelTimeStamps, let tim = self.channelFlexArray.last?.createAt {
                        GFDefaults.channelTimeStamps?[channelId] = tim
                    } else if let channelId = User.userId, let ti = self.channelFlexArray.last?.createAt {
                        GFDefaults.channelTimeStamps = [channelId: ti]
                    }
                }
                closure(success, result.isEmpty, error)
            })
        }
    }
    
    static func getChezLui(top: Bool, _ closure: @escaping(_ success: Bool, _ error: Error?) -> Void) {
        
    }
    
    
    static func getReflexes(top: Bool, _ closure: @escaping(_ success: Bool, _ error: Error?) -> Void) {
        
    }

    
    // downloads the flex images to cache
    private static func getTheImage(flex: Flex) {
        if let urlString = flex.mediaUrl?[0], let url = URL(string: urlString) {
            KingfisherManager.shared.retrieveImage(with: url) { result in
                // Do something with `result`
                print("image cached")
            }
        }
    }
    
    static var chezLuiDummyTabBarHeight: CGFloat = 67.0
    
    // MARK: - comments
    static func getNewFlex(vc: UIViewController) {
        var currentRow = 0
        var flexArray: [Flex] = []
        if let show = vc as? MainViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? ChannelViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? MyFeedViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? ReflexViewController {
            currentRow = show.currentRow
            flexArray = show.reflexArray
        } else if let show = vc as? ChezLuiViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        }
        
        if currentRow < flexArray.count, let flexId = flexArray[currentRow].flexId, let userId = User.userId, userId != "" {
            GFNetworkServices.getNewFlex(for: flexId, { (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if let result = result, let _ = flexArray.filter({ $0.flexId == flexId }).first {
                    flexArray.remove(at: currentRow)
                    flexArray.insert(result, at: currentRow)
                    self.updateThePresentation(flexArray: flexArray, objects: [], vc: vc)
                }
            })
        }
    }
    
    // MARK: - likes / unlikes
    // used by HomeViewController to like/unlike
    static func processLikeUnlikeFor(vc: UIViewController) {
        var currentRow = 0
        var flexArray: [Flex] = []
        if let show = vc as? MainViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? ChannelViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? MyFeedViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? ReflexViewController {
            currentRow = show.currentRow
            flexArray = show.reflexArray
        } else if let show = vc as? ChezLuiViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        }
        
        if flexArray.count > currentRow, let flexId = flexArray[currentRow].flexId {
        
            if let userId = User.userId, userId != "" {
                if User.likedFlexes[userId] == nil {
                    User.likedFlexes[userId] = []
                }
                Utilities.showSpinner()
                if User.likedFlexes[userId]?.filter({ $0 == flexId }).count == 0 { // likes
                    GFNetworkServices.likeThisFlex(true, flexId) { (success, count, error) in
                        DispatchQueue.main.async {
                            Utilities.hideSpinner()
                        }
                        if success {
                            User.likedFlexes[userId]?.append(flexId)
                            if var newFlex = flexArray.filter({ $0.flexId == flexId }).first {
                                if var reaction = newFlex.reactions?.filter({ $0.type == .likes }).first {
                                    reaction.number = count
                                    if let row = newFlex.reactions?.firstIndex(where: {$0.type == .likes}) {
                                        newFlex.reactions?[row] = reaction
                                    } else if newFlex.reactions?.count == 0 {
                                        newFlex.reactions = [reaction]
                                    }
                                }
                                flexArray.remove(at: currentRow)
                                flexArray.insert(newFlex, at: currentRow)
                                var object = SideBarObject()
                                object.buttonType = .like
                                object.buttonCount = count ?? 0
                                object.isHighlighted = true
                                self.updateThePresentation(flexArray: flexArray, objects: [object], vc: vc)
                            }
                        }
                    }
                } else {
                    GFNetworkServices.likeThisFlex(false, flexId) { (success, count, error) in
                        DispatchQueue.main.async {
                            Utilities.hideSpinner()
                        }
                        if success {
                            User.likedFlexes[userId]?.removeAll(where: { $0 == flexId })
                            if var newFlex = flexArray.filter({ $0.flexId == flexId }).first {
                                if var reaction = newFlex.reactions?.filter({ $0.type == .likes }).first {
                                    reaction.number = count
                                    if let row = newFlex.reactions?.firstIndex(where: {$0.type == .likes}) {
                                        newFlex.reactions?[row] = reaction
                                    } else if newFlex.reactions?.count == 0 {
                                        newFlex.reactions = [reaction]
                                    }
                                }
                                flexArray.remove(at: currentRow)
                                flexArray.insert(newFlex, at: currentRow)
                                var object = SideBarObject()
                                object.buttonType = .like
                                object.buttonCount = count ?? 0
                                object.isHighlighted = false
                                self.updateThePresentation(flexArray: flexArray, objects: [object], vc: vc)
                            }
                        }
                    }
                }
            }
        }
    }
    
    static func updateThePresentation(flexArray: [Flex], objects: [SideBarObject], vc: UIViewController) {
        if let show = vc as? MainViewController {
            show.flexArray = flexArray
            DispatchQueue.main.async {
                (show.parent as? HomeViewController)?.updateSideBar(objects: objects)
                (show.parent as? HomeViewController)?.tableView.reloadData()
            }
        } else if let show = vc as? ChannelViewController {
            show.flexArray = flexArray
            DispatchQueue.main.async {
                (show.parent as? HomeViewController)?.updateSideBar(objects: objects)
                (show.parent as? HomeViewController)?.tableView.reloadData()
            }
        } else if let show = vc as? MyFeedViewController {
            show.flexArray = flexArray
            DispatchQueue.main.async {
                (show.parent as? HomeViewController)?.updateSideBar(objects: objects)
                (show.parent as? HomeViewController)?.tableView.reloadData()
            }
        } else if let show = vc as? ReflexViewController {
            show.reflexArray = flexArray
            DispatchQueue.main.async {
                (show.parent as? HomeViewController)?.updateSideBar(objects: objects)
                (show.parent as? HomeViewController)?.tableView.reloadData()
            }
        } else if let show = vc as? ChezLuiViewController {
            show.flexArray = flexArray
            DispatchQueue.main.async {
                (show.parent as? HomeViewController)?.updateSideBar(objects: objects)
                (show.parent as? HomeViewController)?.tableView.reloadData()
            }

        }
    }

    static func getFlexId(vc: UIViewController) -> String? {
        var currentRow = 0
        var flexArray: [Flex] = []
        if let show = vc as? MainViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? ChannelViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? MyFeedViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        } else if let show = vc as? ReflexViewController {
            currentRow = show.currentRow
            flexArray = show.reflexArray
        } else if let show = vc as? ChezLuiViewController {
            currentRow = show.currentRow
            flexArray = show.flexArray
        }
        
        if !flexArray.isEmpty, currentRow < flexArray.count, let flexId = flexArray[currentRow].flexId {
            return flexId
        }
        return nil
    }
    
    static func getRow(of FlexId: String, vc: UIViewController) -> Int? {
        var currentRow = 0
        if let show = vc as? MainViewController {
            currentRow = show.currentRow
        } else if let show = vc as? ChannelViewController {
            currentRow = show.currentRow
        } else if let show = vc as? MyFeedViewController {
            currentRow = show.currentRow
        } else if let show = vc as? ReflexViewController {
            currentRow = show.currentRow
        } else if let show = vc as? ChezLuiViewController {
            currentRow = show.currentRow
        }
        return currentRow
    }

    static func makeTheSideBarObjects(vc: UIViewController, _ closure: @escaping (_ success: Bool, _ objects: [SideBarObject], _ error: Error? ) -> Void) {
        if let flexId = getFlexId(vc: vc), let row = getRow(of: flexId, vc: vc), let userId = User.userId, userId != "" {
            GFNetworkServices.getNewFlex(for: flexId) { (success, flex, error) in
                guard error == nil else {
                    closure(false, [], error)
                    return
                }
                if success, let flex = flex {
                    var object = SideBarObject()
                    
                    object.row = row
                    object.buttonType = .like
                    if let reaction = flex.reactions?.filter({ $0.type == .likes }).first {
                        object.buttonCount = reaction.number ?? 0
                    }
                    if User.likedFlexes[userId]?.filter({ $0 == flexId }).count ?? 0 > 0 {
                        object.isHighlighted = true
                    } else {
                        object.isHighlighted = false
                    }
                    
                    // pass the caption to HomeViewController
                    var object1 = SideBarObject()
                    object1.buttonType = .caption
                    var captionObject = CaptionObject()
                    captionObject.caption = flex.caption
                    captionObject.captionSourceId = flex.owner?.userId
                    captionObject.profileImage = flex.owner?.profileImage
                    captionObject.captionFlexterName = flex.owner?.flexterName
//                    let reaction = flex.reactions?.filter({ $0.type == .likes }).first
                    captionObject.stats = "\(Utilities.relativeTime(date: flex.createAtDate))"//" • \(reaction?.number ?? 0) likes • \(flex.numberOfComments ?? 0) comments"
                    object1.captionData = captionObject
                    
                    // pass the comment count to HomeViewController
                    var object2 = SideBarObject()
                    object2.buttonType = .comment
                    object2.buttonCount = flex.numberOfComments ?? 0
                    object2.commentData = nil
                    
                    // hide the play button if no reflexes
                    var object3 = SideBarObject()
                    object3.buttonType = .play
                    object3.isHidden = !(flex.numberOfReflexes ?? 0 > 0)
                    // show the play dot if reflexes
                    if User.parentsWithReflexesSeen[userId] == nil {
                        User.parentsWithReflexesSeen[userId] = []
                    }
                    if !object3.isHidden, (User.parentsWithReflexesSeen[userId]?.filter({ $0 == flexId }).count ?? 0 > 0) {
                        object3.isHighlighted = false
                    } else {
                        object3.isHighlighted = true
                    }
                    var object4 = SideBarObject()
                    object4.buttonType = .reflex
                    object4.buttonCount = flex.numberOfReflexes ?? 0
                    
                    var objects =  [object, object1, object2, object3, object4]
                    if flexId.contains("PSA") {
                        objects = [object, object2, object3, object4]
                    }
                    closure(true, objects, nil)
                }
            }
        }
    }
        
    // keep track here of what flex of what channel of what feed the user was last on
    // so we can get back there when the user returns
    
    static var lastSeenFeed: FeedType = .myFeed
    static var lastSeenChannelId: String = ""
    static var lastSeenFlexId: String = ""
    
    static var lastSeenTuple: FeedObject {
        get {
            return FeedObject(feedType: lastSeenFeed, channelId: lastSeenChannelId, flexId: lastSeenFlexId)
        }
    }
    
    // MARK: - Channel state tracking
    
    // if user goes to another channel, make note.
    // If user sees reflex do nothing.
    // If user makes reflex or makes comment, do nothing.
    // if user taps another tab > make the call.
    // if app goes to background from channels
    static func makeTheLastSeenCall() {
        var report : [[String: String]] = [[:]]
        for key in lastSeenDictionary.keys {
            if let value = lastSeenDictionary[key] {
            let input = ["channelId": key, "ts": value]
                report.append(input)
            }
        }
        if ((report.first?.isEmpty) != nil) {
            report.removeFirst()
        }
        if report.count > 0 {
            GFNetworkServices.postAllLastSeen(["channels": report]) { (success, error) in
                guard error == nil else {
                    print("\(error?.localizedDescription ?? "error postAllLastSeen")")
                    return
                }
                if success {
                    lastSeenDictionary = [:]
                }
            }
        }
    }
    
    // only used for Channels and reporting to server what were last seen flexes by channel
    static var lastSeenDictionary: [String: String] = [:]
    
    // used to send to channels
    static func updateChannelLastSeen(flex: Flex) {
        if let channelId = flex.channel?.channelId, let dated = flex.createAt {
            if let tracked = FeedViewModel.lastSeenDictionary[channelId],
               Utilities.cleanUpServerDate(dated) > Utilities.cleanUpServerDate(tracked) {
                FeedViewModel.lastSeenDictionary[channelId] = dated
            } else {
                FeedViewModel.lastSeenDictionary[channelId] = dated
            }
            lastSeenFeed = .channels
            lastSeenChannelId = channelId
            lastSeenFlexId = flex.flexId ?? ""
        }
    }
    
    static func clearTheAccount() {
        myFeedFlexArray = []
        primeFlexArray = []
        channelFlexArray = []
    }
    
}
