//
//  ChezLuiViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 12/21/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import Kingfisher

struct ChezLuiDetail {
    var row: Int?
    var flexArray: [Flex]?
}

class ChezLuiViewController: GFChannelViewController {
    
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var dummyTabBarHeightConstraint: NSLayoutConstraint!

    var counter: Int = 1
    var currentRow = 0
    var direction: ScrollDirection = .up
    var flexArray: [Flex] = []
    var lagToRestartTimer: Timer?
    var lastScrollViewContentOffset: CGFloat = 0.0
    var psaIsPresent = false
    var timer: Timer?
    var noMoreData = false
    
    var lui: Flexter?
    
// MARK: - Life cycle funcs

    static func storyboardInstance() -> ChezLuiViewController {
        let sb = UIStoryboard(name: "Main", bundle:     nil)
        return sb.instantiateViewController(withIdentifier: String(describing: ChezLuiViewController.self)) as! ChezLuiViewController
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.tableFooterView = UIView()
        tableView.contentInsetAdjustmentBehavior = .never
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "cell")
        tableView.register(UINib(nibName: FlexTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: FlexTableViewCell.cellIdentifier)
        // add custom refresh control animation
        tableView.refreshControl = UIRefreshControl()
        tableView.refreshControl?.tintColor = .clear
        tableView.refreshControl?.addTarget(self, action: #selector(refreshContent), for: .valueChanged)
        tableView.gestureRecognizers = []
        dummyTabBarHeightConstraint.constant = FeedViewModel.chezLuiDummyTabBarHeight
    }
        
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
//        doTimer()
        if let butt = leftButt {
            didTap(butt)
        }
        if tableView.gestureRecognizers?.count == 0 {
            let swipeUp = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeUp(_:)))
            swipeUp.direction = .up
            tableView.addGestureRecognizer(swipeUp)
            let swipeDown = UISwipeGestureRecognizer(target: self, action: #selector(didSwipeDown(_:)))
            swipeDown.direction = .down
            tableView.addGestureRecognizer(swipeDown)
        }
        refreshChannel(top: true)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        tabBarController?.tabBar.isHidden = false
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        timer?.invalidate()
    }
    
    // MARK: - Gesture Recognizer processing
    
    // cell moves up... indexPath.row increases
    @objc func didSwipeUp(_ sender: UIGestureRecognizer) {
        direction = .up
        guard !(flexArray.last?.flexId?.contains("PSA") ?? false) else { return }
        if let cell = self.tableView.visibleCells.last {
            guard let row = self.tableView.indexPath(for: cell)?.row else { return }
            if row == flexArray.count - 4 {
                refreshChannel(top: false)
            }
            currentRow += 1
            guard currentRow < tableView.numberOfRows(inSection: 0) else {
                currentRow -= 1
                return
            }
            if row != currentRow, currentRow < flexArray.count {
                tableView.scrollToRow(at: IndexPath(row: currentRow, section: 0), at: .top, animated: true)
            }
        }
        if tableView.visibleCells.count == 0 {
            tableView.reloadData()
        }
    }
        
    // cell moves down... indexPath.row decreases
    @objc func didSwipeDown(_ sender: UIGestureRecognizer) {
        if let cell = self.tableView.visibleCells.last {
            guard let row = self.tableView.indexPath(for: cell)?.row else { return }
            currentRow -= 1
            guard currentRow > -1 else {
                currentRow = 0
                tableView.contentInsetAdjustmentBehavior = .scrollableAxes
                let refresh = GFSproketView(frame: CGRect(origin: CGPoint(x: 0.0, y: 0.0), size: CGSize(width: 200, height: 200)))
                refresh.center = UIDevice.isIPhoneX ? CGPoint(x: view.frame.size.width/2, y: 150.0) : CGPoint(x: view.frame.size.width/2, y: 100.0)
                view.addSubview(refresh)
                UIView.animate(withDuration: 1.5) {
                    self.tableView.contentOffset.y = -200
                } completion: { (done) in
                    self.doThePullToRefreshEnding()
                }
                return
            }
            if row != currentRow, currentRow < tableView.numberOfRows(inSection: 0) {
                tableView.scrollToRow(at: IndexPath(row: currentRow, section: 0), at: .top, animated: true)
            }
            self.direction = .down
        }
    }
    
    func doThePullToRefreshEnding() {
        UIView.animate(withDuration: 0.5) {
            self.tableView.contentOffset.y = 0
            self.tableView.contentInsetAdjustmentBehavior = .never
        } completion: { (done) in
            self.view.subviews.filter({ $0.isKind(of: GFSproketView.self) }).forEach({ $0.removeFromSuperview() })
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.refreshChannel(top: true)
            }
        }
    }

    // for refreshControls only
    @objc private func refreshContent() {
        tableView.refreshControl?.beginRefreshing()
        refreshChannel(top: true)
    }
    
    // MARK: - Network Services and SideBarView Controls
    
    func getNewFlex() {
        Utilities.showSpinner()
        if self.tableView.visibleCells.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                self.getNewFlex()
                Utilities.hideSpinner()
                return
            }
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let cell = self.tableView.visibleCells[0]
            guard let row = self.tableView.indexPath(for: cell)?.row else { return }
            if let flexId = self.flexArray[row].flexId, let userId = User.userId, userId != "" {
                GFNetworkServices.getNewFlex(for: flexId, { (success, result, error) in
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                    }
                    if let result = result, let _ = self.flexArray.filter({ $0.flexId == flexId }).first {
                        self.flexArray.remove(at: row)
                        self.flexArray.insert(result, at: row)
                        DispatchQueue.main.async {
                            self.tableView.reloadRows(at: [IndexPath(row: row, section: 0)], with: .fade)
                        }
                    }
                })
            }
        }
    }
    
    @objc func refreshChannel(top: Bool) {
        if let channelId = lui?.userId {
            GFNetworkServices.getChannelFlexes(channelId: channelId, top: top, { (success, result, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if !result.isEmpty {
                    if top {
                        self.flexArray = []
                    }
                    self.flexArray.append(contentsOf: result)
                    self.flexArray = Array(Set(self.flexArray))
                    self.flexArray.sort(by: { $0.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 > $1.createAtDate?.timeIntervalSinceReferenceDate ?? 0.0 })
                    DispatchQueue.main.async {
                        self.tableView.reloadData()
                        self.noMoreData = false
                        if self.currentRow == 0 {
                            self.makeTheSideBarObjects(for: 0)
                        }
                    }
                } else {
                    self.noMoreData = true
                }
            })
        }
    }
    
    private func doTimer() {
        timer = Timer.scheduledTimer(timeInterval: AppInfo.mainChannelTime, target:self , selector: #selector(rotateCell), userInfo: nil, repeats: true)
    }
    
    @objc private func rotateCell() {
       /* ************** TODO: this needs to conform to the swipeUp/swipeDown funcs, if timer is to be used.
         if flexArray.count > 0 {
            UIView.animate(withDuration: 0.5) {
                self.tableView.contentOffset.y = CGFloat(self.counter) * self.view.frame.size.height
            }
            counter += 1
            if counter == Int(Double(flexArray.count) * 0.8) {
                refreshChannel(top: false)
            }
            if counter == flexArray.count {
                counter = 0
            }
        }*/
    }
        
    // logged in user double taps the flex to like it
    @objc func likeThisFlex(_ gesture: UITapGestureRecognizer) {
        (self.parent as? HomeViewController)?.didTapForChannelAction(.like)
    }
    
    private func makeTheSideBarObjects(for row: Int) {
        guard row < flexArray.count else { return }
        let flex = flexArray[row]
        // update the sideBar
        if let flexId = flex.flexId, let userId = User.userId, userId != "" {
            var object = SideBarObject()
            object.buttonType = .like
            if let reaction = flex.reactions?.filter({ $0.type == .likes }).first {
                object.buttonCount = reaction.number ?? 0
            }
            if User.likedFlexes[userId]?.filter({ $0 == flexId }).count ?? 0 > 0 {
                object.isHighlighted = true
            } else {
                object.isHighlighted = false
            }
            
            // pass the caption to HomeViewController
            var object1 = SideBarObject()
            object1.buttonType = .caption
            var captionObject = CaptionObject()
            captionObject.caption = flex.caption
            captionObject.captionSourceId = flex.owner?.userId
            captionObject.profileImage = flex.owner?.profileImage
            captionObject.captionFlexterName = flex.owner?.flexterName
//            let reaction = flex.reactions?.filter({ $0.type == .likes }).first
            captionObject.stats = "\(Utilities.relativeTime(date: flex.createAtDate))"//" • \(reaction?.number ?? 0) likes • \(flex.numberOfComments ?? 0) comments"
            object1.captionData = captionObject
            
            // pass the comment count to HomeViewController
            var object2 = SideBarObject()
            object2.buttonType = .comment
            object2.buttonCount = flex.numberOfComments ?? 0
            object2.commentData = nil
            
            // hide the play button if no reflexes
            var object3 = SideBarObject()
            object3.buttonType = .play
            object3.isHidden = !(flex.numberOfReflexes ?? 0 > 0)
            // show the play dot if reflexes
            if User.parentsWithReflexesSeen[userId] == nil {
                User.parentsWithReflexesSeen[userId] = []
            }
            if !object3.isHidden, (User.parentsWithReflexesSeen[userId]?.filter({ $0 == flexId }).count ?? 0 > 0) {
                object3.isHighlighted = false
            } else {
                object3.isHighlighted = true
            }
            var object4 = SideBarObject()
            object4.buttonType = .reflex
            object4.buttonCount = flex.numberOfReflexes ?? 0
            
            if flexId.contains("PSA") {
                DispatchQueue.main.async {
                    (self.parent as? HomeViewController)?.updateSideBar(objects: [object, object2, object3, object4])
                }
            } else {
                DispatchQueue.main.async {
                    (self.parent as? HomeViewController)?.updateSideBar(objects: [object, object1, object2, object3, object4])
                }
            }
        }

    }
}

// MARK: - UITableViewDelegate and DataSource

extension ChezLuiViewController: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return flexArray.count + 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: FlexTableViewCell.cellIdentifier, for: indexPath) as? FlexTableViewCell else { return FlexTableViewCell() }
        cell.gestureRecognizers = []
        var flex = Flex()
        if indexPath.row >= flexArray.count {
            flex = FlexManager.psa()
            psaIsPresent = true
        } else {
            flex = flexArray[indexPath.row]
        }
        if let urlString = flex.mediaUrl?[0] {
        let url = URL(string: urlString)
        let processor = DownsamplingImageProcessor(size: cell.flexView.bounds.size)
        cell.flexView.kf.indicatorType = .activity //.custom(indicator: GFSproketView2())
        cell.flexView.kf.setImage(
            with: url,
            placeholder: UIImage(named: "loadingPlaceholder"),//FlexManager.randomImagePlaceholder()),
                options: [
                    .processor(processor),
                    .scaleFactor(UIScreen.main.scale),
                    .transition(.fade(1)),
                    .cacheOriginalImage
                ], completionHandler:
                    {
                        result in
                        switch result {
                        case .success(_):
                            break
                        case .failure(let error):
                            print("Job failed: \(error.localizedDescription)")
                        }
                    })
        }
        cell.tag = indexPath.row
        
        // no need to show a sideBarView on any other cell!
        if indexPath.row == currentRow {
            makeTheSideBarObjects(for: indexPath.row)
        }
        if User.isLoggedIn {
            let doubleTap = UITapGestureRecognizer(target: self, action: #selector(likeThisFlex(_:)))
            doubleTap.numberOfTapsRequired = 2
            cell.addGestureRecognizer(doubleTap)
        }
        if !noMoreData, indexPath.row == tableView.numberOfRows(inSection: 0) - 3 {
            refreshChannel(top: false)
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return view.frame.size.height
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//        timer?.invalidate()
//        lagToRestartTimer?.invalidate()
//        lagToRestartTimer = Timer(timeInterval: 5.0, repeats: false, block: { (timer) in
//            self.doTimer()
//        })
    }
}

extension ChezLuiViewController: UIScrollViewDelegate {
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        DispatchQueue.main.async {
            if scrollView.contentOffset.y <= 0 {
                var object = SideBarObject()
                object.buttonType = .like
                if !self.flexArray.isEmpty, let reaction = self.flexArray[0].reactions?.filter({ $0.type == .likes }).first {
                    object.buttonCount = reaction.number ?? 0
                }
                if !self.flexArray.isEmpty, let flexId = self.flexArray[0].flexId, let userId = User.userId, userId != "" {
                    if User.likedFlexes[userId]?.filter({ $0 == flexId }).count == 0 {
                        object.isHighlighted = false
                    } else {
                        object.isHighlighted = true
                    }
                }
                DispatchQueue.main.async {
                    (self.parent as? HomeViewController)?.updateSideBar(objects: [object])
                }
            }
        }
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
    }

}

// MARK: - from the channelDelegate

extension ChezLuiViewController: ChannelDelegate {
    
    func didTapForChannelAction(_ action: ChannelAction) {
        if tableView.visibleCells.isEmpty {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                self.didTapForChannelAction(action)
            }
        }
        switch action {
        case .more:
            break
        case .like:
            break
        case .play:
            break
        case .reflex:
            break
        case .comment:
            break
        default: break
        }
    }
}
