//
//  ChannelDirectoryViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 1/18/21.
//  Copyright © 2021 GameFlex. All rights reserved.
//

import UIKit

enum ChannelListType {
    case owner, contributor, subscribed, all
}

class ChannelDirectoryViewController: GFChannelViewController {
    
    @IBOutlet weak var tableView: UITableView!
    
    var tableType: ChannelListType = .owner
    
    static func storyboardInstance() -> ChannelDirectoryViewController {
        return UIStoryboard(name: "Main", bundle: nil).instantiateViewController(identifier: String(describing: ChannelDirectoryViewController.self))
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: String(describing: UITableViewCell.self))
        tableView.register(UINib(nibName: ProfileMiddleTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ProfileMiddleTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName:  ChannelTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ChannelTableViewCell.cellIdentifier)
        tableView.register(UINib(nibName: ImageViewTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: ImageViewTableViewCell.cellIdentifier)

        tableView.tableFooterView = UIView()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        tableView.reloadData()
        FeedViewModel.channelDetail = nil
    }
    
}

// MARK: - UITableViewDelegate, DataSource
extension ChannelDirectoryViewController: UITableViewDelegate, UITableViewDataSource {
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        switch tableType {
        case .owner:
            let counted = (User.flexter.channelsOwned?.count ?? 0)
            if counted == 0 { return 3 }
            return 2 + counted
        case .contributor: return 1 + (User.flexter.channelsParticipated?.count ?? 0)
        case .subscribed: return 1 + (User.flexter.channelsFollowed?.count ?? 0)
        case .all: return 1
        }
        
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        
        if tableType == .owner, indexPath.row == 1 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ChannelTableViewCell.cellIdentifier, for: indexPath) as? ChannelTableViewCell else { return ChannelTableViewCell() }
            cell.configureCellForBlank()
            cell.delegate = self
            cell.selectionStyle = .none
            return cell
        }
        if tableType == .owner, indexPath.row == 2, (User.flexter.channelsOwned?.count ?? 0) == 0 {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ImageViewTableViewCell.cellIdentifier, for: indexPath) as? ImageViewTableViewCell else { return ImageViewTableViewCell() }
            cell.selectionStyle = .none
            return cell
        }
        switch indexPath.row {
        case 0:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ProfileMiddleTableViewCell.cellIdentifier, for: indexPath) as? ProfileMiddleTableViewCell else { return ProfileMiddleTableViewCell() }
            cell.configureCellForChannelDirectory()
            cell.tableViewWidth = view.frame.size.width
            cell.delegate = self
            return cell
        default:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ChannelTableViewCell.cellIdentifier, for: indexPath) as? ChannelTableViewCell else { return ChannelTableViewCell() }
            switch tableType {
            case .owner:
                cell.channel = User.flexter.channelsOwned?[indexPath.row - 2]
            case .contributor:
                cell.channel = User.flexter.channelsParticipated?[indexPath.row - 1]
            case .subscribed:
                cell.channel = User.flexter.channelsFollowed?[indexPath.row - 1]
            case .all:
                break
            }
            cell.configureCell()
            cell.getlatestFlexFromChannel()
            cell.getChannelDetails()
            cell.delegate = self
            cell.selectionStyle = .none
            if tableType == .owner {
                cell.favButtonWidthConstraint.constant = 0
                cell.moreButtonWidthConstraint.constant = 33
            } else {
                cell.favButtonWidthConstraint.constant = 33
                cell.moreButtonWidthConstraint.constant = 33
                if tableType == .contributor {
                    cell.favButtonWidthConstraint.constant = 0
                }
                let selected = tableType == .subscribed
                cell.favButtonIsSelected(selected)
            }
            //        cell.moreButton.isHidden = true
            
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.row == 0 {
            return 45
        }
        if tableType == .owner, indexPath.row == 2, (User.flexter.channelsOwned?.count ?? 0) == 0 {
            return 226
        }
        return 144
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        switch indexPath.row {
        case 0: break
        default:
            if tableType == .owner, indexPath.row == 1 { return }
            if tableType == .owner, indexPath.row == 2, (User.flexter.channelsOwned?.count ?? 0) == 0 { return }
            var selectedChannelId = ""
            switch tableType {
            case .owner:
                selectedChannelId = User.flexter.channelsOwned?[indexPath.row - 2].channelId ?? ""
            case .contributor:
                selectedChannelId = User.flexter.channelsParticipated?[indexPath.row - 1].channelId ?? ""
            case .subscribed:
                selectedChannelId = User.flexter.channelsFollowed?[indexPath.row - 1].channelId ?? ""
            case .all: return
            }
            if selectedChannelId == "" { return }
            FeedViewModel.channelId = selectedChannelId
            FeedViewModel.channelFlexArray = []
            if let home = parent as? HomeViewController {
                home.sideBarView?.isHidden = false
                home.tableView.isHidden = false
                home.showTheChannel()
            }
//            navigationController?.pushViewController(cvc, animated: true)
//            present(cvc, animated: true, completion: nil)
        // do a special transition in place of channelDirectory to keep the home, tab, nav bars
        
        }
    }
}

extension ChannelDirectoryViewController: ProfileDelegate {
    func updateSideBar(objects: [SideBarObject]) {
        /* */
    }
    
    func didTapToFollow(_ channelId: String?) {
        Utilities.showSpinner()
        guard channelId != nil else { return }
        if let channelId = channelId, let _ = User.flexter.channelsFollowed?.filter({ $0.channelId == channelId }).first {
            GFNetworkServices.unFollowThis([channelId]) { (success, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if success {
                    DispatchQueue.main.async {
                        self.tableView.reloadData()
                    }
                }
            }
        } else if let channelId = channelId {
            GFNetworkServices.followThis([channelId]) { (success, error) in
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                }
                if success {
                    DispatchQueue.main.async {
                        self.tableView.reloadData()
                    }
                }
            }
        }
        
    }
    
    func didTapForProfileAction(_ type: ProfileUpdateDataType, _ object: Any?) {
        switch type {
        case .owner:
            tableType = .owner
            tableView.reloadData()
        case .contributor:
            tableType = .contributor
            tableView.reloadData()
        case .subscribed:
            tableType = .subscribed
            tableView.reloadData()
        case .all:
            tableType = .all
            tableView.reloadData()
        case .newChannel:
            let cc = CreateChannelViewController.storyboardInstance()
            navigationController?.pushViewController(cc, animated: true)
        case .favThisChannel:
            if let channelDetail = object as? ChannelDetail, let channelId = channelDetail.channelId {
                didTapToFollow(channelId)
            }            
        case .goToInvites:
            DispatchQueue.main.async {
                if let channelDetail = object as? ChannelDetail {
                    let invite = InviteViewController.storyboardInstance()
                    invite.channelDetail = channelDetail
                    invite.tableType = self.tableType
                    self.navigationController?.pushViewController(invite, animated: true)
                }
                return
            }
            
        default: break
        }
    }
}
