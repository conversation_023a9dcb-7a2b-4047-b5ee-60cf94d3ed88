//
//  LoginViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 8/25/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit
import FirebaseAuth

class LoginViewController: GFViewController {
    
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var headerView: UIView!
    @IBOutlet weak var logoView: LoginHeaderView!
    @IBOutlet weak var logoLabel: UILabel!
    @IBOutlet weak var signInButton: UIButton!
    @IBOutlet weak var legalTextView: UITextView!
    @IBOutlet weak var passwordResetButton: UIButton!
 
    let headerLabelTag = 9998211
    let headerCheckmarkTag = 9998212

    var emailText = ""
    var passwordText = ""
        
    var topHeaderErrorText = ""
    var bottomHeaderErrorText = ""
    
    let placeholders: [[String: [String]]] = [["Email": ["Email"]],
                                               ["Password": ["Password"]]]

    
    static func storyboardInstance() -> LoginViewController {
        let sb = UIStoryboard(name: "Main", bundle: nil)
        return sb.instantiateViewController(withIdentifier: String(describing: LoginViewController.self)) as! LoginViewController
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setBackgroundGradient()

        navigationController?.navigationBar.tintColor = .black
        let textAttributes = [NSAttributedString.Key.foregroundColor: UIColor.black]
        navigationController?.navigationBar.titleTextAttributes = textAttributes
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UINib(nibName: TextFieldTableViewCell.cellIdentifier, bundle: nil), forCellReuseIdentifier: TextFieldTableViewCell.cellIdentifier)
        tableView.backgroundColor = .clear
        legalTextView.text = "login.legal".localized
        let tap = UITapGestureRecognizer(target: self, action: #selector(didTapView))
        view.addGestureRecognizer(tap)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        title = "Log in"
        // completes the logoView
        let logo = UIImageView(frame: CGRect(x: 0, y: -2, width: 24, height: 24))
        logo.image = UIImage(named: "AppIcon")
        logo.layer.cornerRadius = 12
        logo.clipsToBounds = true
        logoView.addSubview(logo)
        let im2 = UIImage(named: "gameFlex")
        let name = UIImageView(frame: CGRect(x: 30, y: 0, width: 128, height: 20))
        name.image = im2
        logoView.addSubview(name)
        logoView.backgroundColor = .clear
        logoLabel.text = "login.new".localized
        logoLabel.textColor = .black
        let attr = NSMutableAttributedString(string: "login.new".localized)
        if let subStringRange = "login.new".localized.range(of: "login.newBolded".localized) {
            let nsRange = NSRange(subStringRange, in: "login.new".localized)
            attr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 17),
                                NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                               range: nsRange)
            logoLabel.attributedText = attr
        }
        let butt = UIButton(frame: CGRect(x: 0, y: 0, width: headerView.frame.width, height: headerView.frame.height + 25))
        butt.addTarget(self, action: #selector(didTapNewToApp), for: .touchUpInside)
        butt.backgroundColor = .clear
        butt.isUserInteractionEnabled = true
        headerView.addSubview(butt)
        
        // setup the signup button
        signInButton.layer.cornerRadius = 8.0
        signInButton.alpha = 0.3
        signInButton.isUserInteractionEnabled = false
        signInButton.backgroundColor = .gfGreen
        signInButton.tintColor = .black
        signInButton.setTitle("login.signin".localized, for: .normal)
        
        // password reset button
        passwordResetButton.setTitle("resetPassword".localized, for: .normal)
        passwordResetButton.titleLabel?.font = .boldSystemFont(ofSize: 15)
        enableResetPasswordButton()
        
        // textView
        legalTextView.text = "login.legal".localized
        legalTextView.textColor = .white
        let bttr = NSMutableAttributedString(string: "login.legal".localized)
        bttr.addAttribute(NSAttributedString.Key.foregroundColor, value: UIColor.white, range: NSRange(location: 0, length: "login.legal".localized.count))
        if let subStringRange = "login.legal".localized.range(of: "Privacy Policy") {
            let nsRange = NSRange(subStringRange, in: "login.legal".localized)
            bttr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 12),
                                NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                               range: nsRange)
            if let subbStringRange = "login.legal".localized.range(of: "Terms of Service") {
                let nsRange1 = NSRange(subbStringRange, in: "login.legal".localized)
                bttr.addAttributes([NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 12),
                                    NSAttributedString.Key.foregroundColor: UIColor.gfGreen],
                                   range: nsRange1)
            }
            legalTextView.attributedText = bttr
            legalTextView.textAlignment = .center
        }

        legalTextView.backgroundColor = .clear
        
    }
    
    @objc func didTapView() {
        view.endEditing(true)
    }
    
    @objc func didTapNewToApp() {
        navigationController?.popViewController(animated: true)
    }
    
    @IBAction func didTapSignInButton() {
        Utilities.showSpinner()
        User.authenticationService = .email
        Auth.auth().signIn(withEmail: emailText, password: passwordText) { [weak self] authResult, error in
            guard let strongSelf = self else { return }
            if error != nil {
                if AuthErrorCode(rawValue: error!._code) == AuthErrorCode(rawValue: 17011) { // not a valid firebase user
                    // create the firebase user
                    strongSelf.createFirebaseUser(email: strongSelf.emailText, password: strongSelf.passwordText) { (success, error) in
                        guard error == nil else {
                            DispatchQueue.main.async {
                                Utilities.hideSpinner()
                                let alert = UIAlertController(title: "Error", message: "Something unexpected happened. code 143.", preferredStyle: .alert)
                                let ok = UIAlertAction(title: "Try Again", style: .default) {_ in
                                    strongSelf.emailText = ""
                                    strongSelf.passwordText = ""
                                    strongSelf.tableView.reloadData()
                                }
                                alert.addAction(ok)
                                Utilities.hideSpinner()
                                strongSelf.present(alert, animated: true, completion: nil)
                            }
                            return
                        }
                        
                    }
                } else if AuthErrorCode(rawValue: error!._code) == AuthErrorCode(rawValue: 17009) { // not a valid password
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        let alert = UIAlertController(title: "Error", message: "Something unexpected happened. code 159.", preferredStyle: .alert)
                        let ok = UIAlertAction(title: "Try Again", style: .default) {_ in
                            strongSelf.emailText = ""
                            strongSelf.passwordText = ""
                            strongSelf.tableView.reloadData()
                        }
                        alert.addAction(ok)
                        DispatchQueue.main.async {
                            Utilities.hideSpinner()
                            strongSelf.present(alert, animated: true, completion: nil)
                        }
                    }
                } else if AuthErrorCode(rawValue: error!._code) == AuthErrorCode(rawValue: 17010) { // too many unsuccessful pw attempts
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        let alert = UIAlertController(title: "Error", message: "Too many unsuccessful attempts. Reset your password or try again later.", preferredStyle: .alert)
                        let ok = UIAlertAction(title: "Try Again", style: .default) {_ in
                            User.shouldDismissOnPop = true
                            self?.navigationController?.popViewController(animated: true)
                        }
                        let reset = UIAlertAction(title: "Reset Password", style: .default) {_ in
                            strongSelf.emailText = ""
                            strongSelf.passwordText = ""
                            strongSelf.tableView.reloadData()
                            strongSelf.resetPassword()
                        }
                        alert.addAction(ok)
                        alert.addAction(reset)
                        strongSelf.present(alert, animated: true, completion: nil)
                    }
                } else {
                    DispatchQueue.main.async {
                        Utilities.hideSpinner()
                        let alert = UIAlertController(title: "Error", message: "Report this bug:\n\n \(error.debugDescription)", preferredStyle: .alert)
                        let report = UIAlertAction(title: "Report", style: .default) { (action) in
                            let bvc = BugViewController.storyboardInstance()
                            bvc.preRecordedMessage = "LOGIN ERROR\n\(error.debugDescription)"
                            DispatchQueue.main.async {
                                Utilities.hideSpinner()
                                strongSelf.present(bvc, animated: true)
                            }
                        }
                        let not = UIAlertAction(title: "Cancel", style: .default, handler: nil)
                        alert.addAction(not)
                        alert.addAction(report)
                        strongSelf.present(alert, animated: true, completion: nil)
                    }
                }
                return
            }
            if let use = authResult?.user {
                User.flexter.flexterName = use.displayName ?? ""
                User.userId       = use.uid
                User.flexter.email    = use.email ?? ""
                User.isEmailVerified = use.isEmailVerified
                if use.photoURL?.absoluteString != nil {
                    User.flexter.profileImage   = use.photoURL?.absoluteString ?? ""
                }
                if !use.isEmailVerified {
                    self?.sendVerificationEmail()
                } else if User.flexter.email != "" {
                    User.shouldDismissOnPop = true
                    User.shouldGoToLoginOnPop = false
                    if let email = User.flexter.email, email != "" {
                        GFNetworkServices.loginAtGameFlexWithEmail(email) { (success, error) in
                            guard error == nil else {
                                DispatchQueue.main.async {
                                    Utilities.hideSpinner()
                                    let alert = UIAlertController(title: "Error",
                                                                  message: "Something unexpected happened. Try again.",
                                                                  preferredStyle: .alert)
                                    let ok = UIAlertAction(title: "OK", style: .default, handler: { (ok) in
                                        self?.emailText = ""
                                        self?.passwordText = ""
                                        self?.tableView.reloadData()
                                    })
                                    alert.addAction(ok)
                                    self?.present(alert, animated: true, completion: nil)
                                }
                                return
                            }
                            if success, let userId = User.userId {
                                GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                                    if let flexter = flexter {
                                        User.updateTheUser(flexter)
                                    }
                                    DispatchQueue.main.async {
                                        Utilities.hideSpinner()
                                        let alert = UIAlertController(title: "Success",
                                                                      message: "",
                                                                      preferredStyle: .alert)
                                        switch User.authenticationService {
                                        case .apple: alert.message = OnboardingStateMachine.existingAccount ? "login.successApple.existingAccount".localized : "login.successApple".localized
                                        case .google: alert.message = OnboardingStateMachine.existingAccount ? "login.successGoogle.existingAccount".localized : "login.successGoogle".localized
                                        case .email: alert.message = OnboardingStateMachine.existingAccount ? "login.successEmail.existingAccount".localized : "login.successEmail".localized
                                        case .none: alert.message = OnboardingStateMachine.existingAccount ? "login.successNone.existingAccount".localized : "login.successNone".localized
                                        }
                                        let ok = UIAlertAction(title: "OK", style: .default, handler: { _ in
                                            User.shouldDismissOnPop = true
                                            OnboardingStateMachine.didComplete(this: .login)
                                        })
                                        alert.addAction(ok)
                                        self?.present(alert, animated: true, completion: nil)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    @IBAction func resetPassword() {
        Utilities.showSpinner()
        Auth.auth().sendPasswordReset(withEmail: emailText) { error in
            guard error == nil else {
                return
            }
                DispatchQueue.main.async {
                    Utilities.hideSpinner()
                    let alert = UIAlertController(title: "Password Reset Email",
                                                  message: "GameFlex has sent a password reset email.",
                                                  preferredStyle: .alert)
                    let ok = UIAlertAction(title: "OK", style: .default, handler: { _ in
                        User.shouldGoToLoginOnPop = true
                        self.navigationController?.popViewController(animated: true)
                    })
                    alert.addAction(ok)
                    self.present(alert, animated: true, completion: nil)
                }

        }

    }
    
    private func enableResetPasswordButton() {
        if emailText.count > 0, Utilities.isValidEmail(emailText) {
            passwordResetButton.alpha = 1.0
            passwordResetButton.isUserInteractionEnabled = true
            passwordResetButton.tintColor = .gfGreen
        } else {
            passwordResetButton.alpha = 0.3
            passwordResetButton.isUserInteractionEnabled = false
            passwordResetButton.tintColor = .darkText
        }
    }
    
    private func enableSignInButton() {
        enableResetPasswordButton()
        if emailText.count > 0,
            Utilities.isValidEmail(emailText),
            Utilities.isValidPassword(passwordText) {
            signInButton.alpha = 1.0
            signInButton.isUserInteractionEnabled = true
        } else {
            signInButton.alpha = 0.3
            signInButton.isUserInteractionEnabled = false
        }
    }

}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension LoginViewController: UITableViewDataSource, UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return 2
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: TextFieldTableViewCell.cellIdentifier, for: indexPath) as? TextFieldTableViewCell else { return TextFieldTableViewCell() }
        let str = decomposeThePlaceholders(index: indexPath.section)[indexPath.row]
        if indexPath.section == 1 {
            cell.textField.isSecureTextEntry = true
            cell.textField.tag = indexPath.row + 10
            cell.configureCell(str, password: true)
            cell.textField.text = passwordText
        } else {
            cell.configureCell(str, password: false)
            cell.textField.tag = indexPath.row
            cell.textField.text = emailText
            enableResetPasswordButton()
        }
        switch str {
        case "Name":
            cell.textField.autocapitalizationType = .words
        default:
            cell.textField.autocapitalizationType = .none
        }
        cell.backgroundColor = .clear
        cell.selectionStyle = .none
        cell.textField.delegate = self
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 55
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {

        let header = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.size.width, height: 35))
        let label = UILabel(frame: CGRect(x: 16, y: 5, width: 250.0, height: 25))
        label.text = decomposePlaceholderHeader(index: section)
        label.font = .boldSystemFont(ofSize: 20)
        label.textColor = .white
        header.addSubview(label)
        
        header.subviews.filter({ $0.tag == self.headerLabelTag }).forEach({ $0.removeFromSuperview() })
        header.subviews.filter({ $0.tag == self.headerCheckmarkTag }).forEach({ $0.removeFromSuperview() })

        switch section {
        case 0:
            if Utilities.isValidEmail(emailText), emailText.count > 0 {
                topHeaderErrorText = ""
                // add checkmark
                let im = UIImageView(frame: CGRect(x: 125.0, y: 8.0, width: 17, height: 17))
                im.image = #imageLiteral(resourceName: "circleCheck")
                im.tag = headerCheckmarkTag
                header.addSubview(im)
            } else if emailText.count > 0 {
                topHeaderErrorText = "email.inValidEmail".localized
            } else {
                topHeaderErrorText = ""
            }
            if topHeaderErrorText != "" {
                let topErrorLabel = UILabel(frame: CGRect(x: 150.0, y: 0.0, width: view.frame.width - 106.0, height: 35.0))
                topErrorLabel.text = topHeaderErrorText.uppercased()
                topErrorLabel.font = .boldSystemFont(ofSize: 10)
                topErrorLabel.textColor = .gfYellow_F2DE76
                topErrorLabel.textAlignment = .left
                topErrorLabel.numberOfLines = 0
                topErrorLabel.tag = 9998211
                header.addSubview(topErrorLabel)
                let im = UIImageView(frame: CGRect(x: 120, y: 8.0, width: 20, height: 20))
                im.image = #imageLiteral(resourceName: "alertTriangle")
                im.tag = headerLabelTag
                header.addSubview(im)
            }
        case 1:
            if Utilities.isValidPassword(passwordText), passwordText.count > 0 {
                bottomHeaderErrorText = ""
                //add checkmark
                let im = UIImageView(frame: CGRect(x: 125.0, y: 8.0, width: 17, height: 17))
                im.image = #imageLiteral(resourceName: "circleCheck")
                im.tag = headerCheckmarkTag
                header.addSubview(im)
            } else  if passwordText.count > 0 {
                bottomHeaderErrorText = "login.inValidPassword".localized
            } else {
                bottomHeaderErrorText = ""
            }
            if bottomHeaderErrorText != "" {
                let bottomErrorLabel = UILabel(frame: CGRect(x: 150.0, y: 0.0, width: view.frame.width - 106.0, height: 35.0))
                bottomErrorLabel.text = bottomHeaderErrorText.uppercased()
                bottomErrorLabel.font = .boldSystemFont(ofSize: 10)
                bottomErrorLabel.textColor = .gfYellow_F2DE76
                bottomErrorLabel.textAlignment = .left
                bottomErrorLabel.numberOfLines = 0
                bottomErrorLabel.tag = 9998211
                header.addSubview(bottomErrorLabel)
                let im = UIImageView(frame: CGRect(x: 120, y: 8.0, width: 20, height: 20))
                im.image = #imageLiteral(resourceName: "alertTriangle")
                im.tag = headerLabelTag
                header.addSubview(im)
            }
        default: break
        }
        
        return header
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 35
    }
    
    // MARK: - tableView helpers
    func decomposeThePlaceholders(index: Int) -> [String] {
        let values = placeholders[index].values
        var arr: [String] = []
        for val in values {
            arr.append(contentsOf: val)
        }
        return arr
    }
    
    func decomposePlaceholderHeader(index: Int) -> String {
        let keys = placeholders[index].keys
        
        return keys.first ?? ""
    }
}

extension LoginViewController: UITextFieldDelegate {
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        let section = textField.tag < 9 ? 0 : 1
        let row = textField.tag < 9 ? textField.tag : textField.tag - 10
        guard let cell = tableView.cellForRow(at: IndexPath(row: row, section: section)) as? TextFieldTableViewCell else { return }
        cell.hideOrShowTheClearButton(tag: textField.tag)
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {
        let section = textField.tag < 10 ? 0 : 1
        let row = textField.tag < 10 ? textField.tag : textField.tag - 10
        guard let cell = tableView.cellForRow(at: IndexPath(row: row, section: section)) as? TextFieldTableViewCell else { return }
        cell.hideOrShowTheClearButton(tag: textField.tag)
        let str = textField.text?.trim()
        textField.text = str
        enableSignInButton()
        tableView.reloadData()
    }
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        guard string != "\n" else {
            return false
        }

        let result = (textField.text as NSString?)?.replacingCharacters(in: range, with: string) ?? string
        switch textField.tag {
        case 0: // emailText
            emailText = result
        case 10:
            passwordText = result
        default: break
        }
        enableSignInButton()
        return true
    }
    
    
}
