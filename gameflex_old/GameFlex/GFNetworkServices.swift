//
//  GFNetworkServices.swift
//  GameFlex
//
//  Created by <PERSON> on 8/6/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import Foundation
import Alamofire
import SwiftyJSON
import AWSRekognition

class GFNetworkServices {
    
    static let shared = GFNetworkServices()
    
    static let kUploadMediaURL          = "https://media.gameflexdev.com/%@/%@"
    static let kBaseURL                 = "https://api.gameflexdev.com"
    static let kSearchURL               = "https://search.gameflexdev.com"
    
    static let kAcceptInviteEndPoint    = "user/accept"
    static let kChannelEndPoint         = "channel"
    static let kCommentEndPoint         = "comments"
    static let kFlexEndPoint            = "flex"
    static let kFollowEndPoint          = "follow"
    static let kGetFlexesEndPoint       = "flexes"
    static let kInviteEndpoint          = "channel/invite"
    static let kChannelFollowersEndpoint = "channel/followers"
    static let kLoginEndPoint           = "login"
    static let kNotificationsEndPoint   = "notifications"
    static let kReactionEndPoint        = "reaction"
    static let kReflexEndPoint          = "reflex"
    static let kReflexesEndPoint        = "reflexes"
    static let kRegisterEndPoint        = "registration"
    static let kSearchUsersEndPoint     = "find"
    static let kUserEndPoint            = "user"
    static let kUserFlexesEndPoint      = "user/flexes"
    static let kUserReactionsEndPoint   = "user/reactions"
    static let kUserChannelsEndPoint    = "user/channels"
    static let kLastSeenEndPoint        = "user/lastseen"
    static let kFollowersEndPoint       = "user/followers"
    static let kGetMainChannelFlexes    = "flexes/main"

    static let kMainChannel             = "3dce98e4-6e66-435c-b80c-b37ebff5abd4"
    
    // MARK: - Flex services
    
    // for uploading media and automatically posting the flex
    // The postReflex is determined by the parameter in uploadMEdia's interior call
    static func uploadMedia(channelId: String, image: UIImage, closure: @escaping (_ success: Bool, _ error: Error? ) -> Void) {
        if let im = image.compress(to: AppInfo.maxFileSizekb), let data = im.jpegData(compressionQuality: 1.0) /* 2MB limit*/ {
            let dated = String(format: "%.f", Date().timeIntervalSinceReferenceDate)
            if channelId != "" {
                let urlString = String(format: kUploadMediaURL, channelId, "\(dated)_\(GFDefaults.completedFlexCount).png")
                if let url = URL(string: urlString), let token = User.refreshToken, User.refreshToken != "" {
                    var request = URLRequest(url: url)
                    request.httpMethod = "PUT"
                    request.httpBody = data
                    request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                    request.addValue("application/json", forHTTPHeaderField: "Accept")
                    request.addValue(token, forHTTPHeaderField: "Authorization")
                    let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                        if let error = error {
                            print ("error: \(error.localizedDescription)")
                            closure(false, error)
                        }
                        guard let response = response as? HTTPURLResponse,
                              (200...299).contains(response.statusCode) else {
                            closure(false, error)
                            return
                        }
                        if let data = data, let resultUrlString = String(data: data, encoding: .utf8) {
                            self.postFlex(mediaURLString: resultUrlString, channelId: channelId) { (succeeded, error2) in
                                closure(succeeded, error2)
                            }
                        } else {
                            closure(false, GFError(errorType: .failedURLPut))
                        }
                    })
                    task.resume()
                }
            } else {
                closure(false, GFError(errorType: .flawedURL))
            }
        }
    }
    
    // use uploadMedia() func above, this is consumed by uploadMedia()
    private static func postFlex(mediaURLString: String, channelId: String, closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        if var url = URL(string: "\(kBaseURL)/\(kFlexEndPoint)"), let token = User.refreshToken, User.refreshToken != ""  {
            if let parentFlexId = FlexManager.flex?.parentFlexId, let reUrl = URL(string: "\(kBaseURL)/\(kReflexEndPoint)/\(parentFlexId)") {
                url = reUrl
            }
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")

            var objects: [String: String] = FlexManager.buildTheParameters(channelId)
            
            objects["mediaUrl"] = mediaURLString
            request.httpBody = try? JSONSerialization.data(withJSONObject: objects, options: .prettyPrinted)
            
            let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                if let error = error {
                    print ("error: \(error.localizedDescription)")
                    closure(false, error)
                }
                let code = (response as? HTTPURLResponse)?.statusCode
                guard let response = response as? HTTPURLResponse,
                    (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, error)
                            return
                        }
                    closure(false, GFError(errorType: .unknownError))
                        return
                }
                closure(true, nil)
            })
            task.resume()
        }
    }
        
    // without timestamp gets 5 oldest reflexes
    // with timestamp of oldest flex, gets next 25 older ones
    static func getReflexes(top: Bool, for parentFlexId: String, _ closure: @escaping (_ success: Bool, _ results: [Flex], _ error: Error?) -> Void) {
    
        var urlString = "\(kBaseURL)/\(kReflexesEndPoint)/\(parentFlexId)"
        
        if !top, let dict = GFDefaults.channelTimeStamps, let stamp = dict[parentFlexId] {
            urlString = "\(urlString)?timestamp=\(stamp)&limit=25"
        }
        
        if let url = URL(string: urlString) {
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            let token = User.refreshToken ?? "702bf98b-60b2-4206-ae9c-7e587714a22a"
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                guard let arr = json["body"]["reflexes"].array else {
                    closure(false, [], nil)
                    return
                }
                guard !arr.isEmpty else {
                    closure(true, [], error)
                    return
                }
                let result = Flex.parse(arr)
                
                closure(true, result, nil)
            }
            dataTask?.resume()
        }
    }
    
    // MARK: - Channel Services
    
    static func getNewFlex(for flexId: String, _ closure: @escaping (_ success: Bool, _ result: Flex?, _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "", let url = URL(string: "\(kBaseURL)/\(kFlexEndPoint)/\(flexId)") {
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            let task = URLSession.shared.dataTask(with: request) { (data, response, error) in
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                let dict = json["body"]["flex"]
                let result = Flex.parseFlex(dat: dict)
                closure(true, result, nil)
            }
            task.resume()
        }
    }
    
    
    static func getChannelDetailFor(channelId:String, _ closure: @escaping (_ success: Bool, _ result: ChannelDetail, _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kChannelEndPoint)/\(channelId)"
        
        if let url = URL(string: urlString),let token = User.refreshToken, User.refreshToken != "" {
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                let result = ChannelDetail.parseChannelDetail(json: json)
                
                closure(true, result, nil)
            }
            dataTask?.resume()
        }
    }
    
    static func getAFewFlexesForEachChannel(_ closure: @escaping (_ success: Bool, _ results: [Channel], _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kUserChannelsEndPoint)"
        
        if let url = URL(string: urlString) {
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            let token = User.refreshToken ?? "702bf98b-60b2-4206-ae9c-7e587714a22a"
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                var channels: [Channel] = []
                channels.append(contentsOf: Channel.parseArray(json["channelsParticipated"].arrayValue))
                channels.append(contentsOf: Channel.parseArray(json["channelsOwned"].arrayValue))
                channels.append(contentsOf: Channel.parseArray(json["channelsFollowed"].arrayValue))
                let filtered = channels.filter({ $0.flexes?.count != 0 })
                var temp = filtered
                for index in 0..<filtered.count {
                    let sorted = filtered[index].flexes?.sorted(by: { $0.createAtDate ?? Date() < $1.createAtDate ?? Date()})
                    temp[index].flexes = sorted
                }
                closure(true, temp, nil)
            }
            dataTask?.resume()
        }
    }
    
    // without timestamp gets 5 most recent flexes
    // with timestamp of oldest flex, gets next 20? older ones
    static func getChannelFlexes(channelId: String, top: Bool, _ closure: @escaping (_ success: Bool, _ results: [Flex], _ error: Error?) -> Void) {
        if channelId != "" {
        var urlString = "\(kBaseURL)/\(kGetFlexesEndPoint)?channelId=\(channelId)"
        
        if !top, let dict = GFDefaults.channelTimeStamps, let stamp = dict[channelId] {
            urlString = "\(urlString)&timestamp=\(stamp)&limit=25"
        }
        
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                let token = User.refreshToken ?? "702bf98b-60b2-4206-ae9c-7e587714a22a"
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                let defaultSession = URLSession(configuration: .default)
                
                var dataTask: URLSessionDataTask?
                dataTask = defaultSession.dataTask(with: request) { data, response, error in
                    
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    guard let arr = json["body"]["flexes"].array else {
                        closure(false, [], nil)
                        return
                    }
                    guard !arr.isEmpty else {
                        closure(false, [], error)
                        return
                    }
                    let result = Flex.parse(arr)
                    
                    closure(true, result, nil)
                }
                dataTask?.resume()
            }
        }
        closure(false, [], GFError(errorType: .flawedURL))
    }
    
    static func getChannelFollowers(for channelId: String, closure: @escaping (_ success: Bool, _ results: [Channel], _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kChannelFollowersEndpoint)/\(channelId)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, [], error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, [], error)
                            return
                        }
                        closure(false, [], error)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    let result = Channel.parseArray(json["followers"].arrayValue)
                    closure(true, result, nil)
                })
                task.resume()
            }
        }
    }
    
    // without timestamp gets 5 most recent flexes
    // with timestamp of oldest flex, gets next 20? older ones
    static func getMainFlexes(top: Bool, _ closure: @escaping (_ success: Bool, _ results: [Flex], _ ts: String?, _ error: Error?) -> Void) {
        var urlString = "\(kBaseURL)/\(kGetMainChannelFlexes)"
        
        if !top, let dict = GFDefaults.channelTimeStamps, let stamp = dict[kMainChannel] {
            urlString = "\(urlString)?limit=25&timestamp=\(stamp)"
        }
        
        if let url = URL(string: urlString) {
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            let token = User.refreshToken ?? "702bf98b-60b2-4206-ae9c-7e587714a22a"
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                guard let arr = json["body"]["flexes"].array else {
                    closure(false, [], nil, nil)
                    return
                }
                guard !arr.isEmpty else {
                    closure(false, [], nil, error)
                    return
                }
                let result = Flex.parse(arr)
                let ts = json["body"]["timestamp"].stringValue
                closure(true, result, ts, nil)
            }
            dataTask?.resume()
        }
        closure(false, [], nil, GFError(errorType: .flawedURL))
    }

    // for uploading media and automatically creating the channel using addChannelContent()
    // The contents are from the parameter in createChannel's interior call using CreateChannelViewModel.newChannelParameters()
    static func createChannel(image: UIImage, closure: @escaping (_ success: Bool, _ channel: Channel?, _ error: Error? ) -> Void) {
        if let im = image.compress(to: AppInfo.maxFileSizekb), let data = im.jpegData(compressionQuality: 1.0) /* 2MB limit*/ {
            let dated = String(format: "%.f", Date().timeIntervalSinceReferenceDate)
            let urlString = String(format: kUploadMediaURL, kChannelEndPoint, "\(dated)_\(GFDefaults.completedFlexCount).png")
            if let url = URL(string: urlString), let token = User.refreshToken, User.refreshToken != "" {
                var request = URLRequest(url: url)
                request.httpMethod = "PUT"
                request.httpBody = data
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, nil, error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        closure(false, nil, error)
                        return
                    }
                    if let data = data, let resultUrlString = String(data: data, encoding: .utf8) {
                        CreateChannelViewModel.profileImageUrl = resultUrlString
                        self.addChannelContent() { (succeeded, channel, error2) in
                            closure(succeeded, channel, error2)
                        }
                    }
                })
                task.resume()
            }
        }
    }
    
    // this combines all the required parameters of the httpBody
    private static func addChannelContent(_ closure: @escaping (_ success: Bool, _ channel: Channel?, _ error: Error?) -> Void) {
    
        let urlString = "\(kBaseURL)/\(kChannelEndPoint)"
        if let url = URL(string: urlString) {
            var request = URLRequest(url: url)
            request.httpMethod = "PUT"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            let token = User.refreshToken ?? "702bf98b-60b2-4206-ae9c-7e587714a22a"
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            var contented = CreateChannelViewModel.createChannelParameters
            if let im = CreateChannelViewModel.profileImageUrl, var contents = contented {
                contents[.profileImage] = im
                contented = contents
            }
            if let content = contented {
                var update: [String: String] = [:]
                for tuple in content {
                    update[tuple.key.rawValue] = tuple.value
                }
                request.httpBody = try? JSONSerialization.data(withJSONObject: update, options: .prettyPrinted)
                
                let defaultSession = URLSession(configuration: .default)
                
                var dataTask: URLSessionDataTask?
                dataTask = defaultSession.dataTask(with: request) { data, response, error in
                    
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    let channel = Channel.parseTheChannel(json)

                    closure(true, channel, nil)
                }
                dataTask?.resume()
            }
        }
    }
    
    static func deleteChannel(channelId: String, _ closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kChannelEndPoint)/\(channelId)"
        
        if let url = URL(string: urlString),let token = User.refreshToken, User.refreshToken != "" {
            var request = URLRequest(url: url)
            request.httpMethod = "DELETE"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")
            
            let contents = ["days": "now"]
            
            request.httpBody = try? JSONSerialization.data(withJSONObject: contents, options: .prettyPrinted)
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let _ = data, error == nil else { return }
                
//                let json = JSON(data)
        
                closure(true, nil)
            }
            dataTask?.resume()
        }
    }
    
    // invite/de-invite func
    static func handleInvitesUninvites(invite: Bool, participants: [String], channelId: String, _ closure: @escaping(_ success: Bool, _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kInviteEndpoint)/\(channelId)"
        
        if let url = URL(string: urlString),let token = User.refreshToken, User.refreshToken != "" {
            var request = URLRequest(url: url)
            if invite {
                request.httpMethod = "POST"
            } else {
                request.httpMethod = "DELETE"
            }
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")
            let update: [String: [String]] = ["participants": participants]
            request.httpBody = try? JSONSerialization.data(withJSONObject: update, options: .prettyPrinted)
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let data = data, error == nil else { return }
                
//                let json = JSON(data)
                
                closure(true, nil)
            }
            dataTask?.resume()
        }
    }
    
    static func postAllLastSeen(_ report: [String: Any], _ closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kLastSeenEndPoint)"
        
        if let url = URL(string: urlString),let token = User.refreshToken, User.refreshToken != "" {
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")
            request.httpBody = try? JSONSerialization.data(withJSONObject: report, options: .prettyPrinted)
            let defaultSession = URLSession(configuration: .default)
            
            var dataTask: URLSessionDataTask?
            dataTask = defaultSession.dataTask(with: request) { data, response, error in
                
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                
                closure(true, nil)
            }
            dataTask?.resume()
        }

    }
    
    //MARK: - Likes, Comments, Notifications
    
    static func likeThisFlex(_ like: Bool = true, _ flexId: String, closure: @escaping (_ success: Bool, _ likes: Int?, _ error: Error?) -> Void) {
        if let token = User.refreshToken, token != "" {
            let urlString = "\(kBaseURL)/\(kReactionEndPoint)/\(flexId)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "POST"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                if like {
                    request.httpBody = try? JSONSerialization.data(withJSONObject: ["reactionType": "like"], options: .prettyPrinted)
                } else {
                    request.httpBody = try? JSONSerialization.data(withJSONObject: ["reactionType": "unlike"], options: .prettyPrinted)
                }

                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, nil, error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, nil, error)
                            return
                        }
                        closure(false, nil, error)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    if let number = json[0]["likes"]["number"].int {
                        closure(true, number, nil)
                    }
                })
                task.resume()
            }
        }
    }
    
    static func getUserReactions(_ closure: @escaping (_ success: Bool, _ array: [String]?, _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kUserReactionsEndPoint)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")

                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, nil, error)
                    }
                    let res = response as? HTTPURLResponse
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, nil, error)
                            return
                        }
                        // if no comments, returns 404 not found error
                        if let resp = res, resp.statusCode == 404 {
                            let err = GFError(errorType: .contentNotFound)
                            closure(true, [], err)
                            return
                        }
                        let err = GFError(errorType: .unknownError)
                        closure(false, nil, err)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    var array: [String] = []
                    if let likes = json["reactions"]["likes"].array {
                        for flexId in likes {
                            if let fid = flexId.string {
                                array.append(fid)
                            }
                        }
                    }
                    closure(true, array, nil)
                })
                task.resume()
            }
        }
    }

    
    static func makeComment(_ comment: String, _ flexId: String, closure: @escaping (_ success: Bool, _ count: Int?, _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kCommentEndPoint)/\(flexId)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "POST"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                request.httpBody = try? JSONSerialization.data(withJSONObject: ["comment": comment], options: .prettyPrinted)

                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, nil, error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, nil, error)
                            return
                        }
                        closure(false, nil, error)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    if let number = json["numberOfComments"].int {
                        closure(true, number, nil)
                    }
                })
                task.resume()
            }
        }
    }
    
    static func getComments(_ flexId: String, _ lastReceivedTimeStamp: String? = nil, closure: @escaping (_ success: Bool, _ array: [Comment]?, _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            var urlString = "\(kBaseURL)/\(kCommentEndPoint)/\(flexId)?limit=25"
            if let ts = lastReceivedTimeStamp {
                urlString = "\(urlString)&timestamp=\(ts)"
            }
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")

                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, nil, error)
                    }
                    let res = response as? HTTPURLResponse
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, nil, error)
                            return
                        }
                        // if no comments, returns 404 not found error
                        if let resp = res, resp.statusCode == 404 {
                            let err = GFError(errorType: .contentNotFound)
                            closure(true, [], err)
                            return
                        }
                        let err = GFError(errorType: .unknownError)
                        closure(false, nil, err)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    let array = Comment.parse(json["comments"].arrayValue)
                    closure(true, array, nil)
                })
                task.resume()
            }
        }
    }
    
    static func getNotifications(_ closure: @escaping (_ success: Bool, _ result: [GFNotification], _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kNotificationsEndPoint)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")

                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, [], error)
                    }
                    let res = response as? HTTPURLResponse
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, [], error)
                            return
                        }
                        // if no comments, returns 404 not found error
                        if let resp = res, resp.statusCode == 404 {
                            let err = GFError(errorType: .contentNotFound)
                            closure(true, [], err)
                            return
                        }
                        let err = GFError(errorType: .unknownError)
                        closure(false, [], err)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    let array = GFNotification.parse(json.arrayValue)
                    closure(true, array, nil)
                })
                task.resume()
            }
        }
    }
    
    //MARK: - AWS Rekognition Method
    static func sendImageToRekognition(_ imageViewData: Data, closure: @escaping (_ success: Bool, _ error: Error?) -> Void){
        let rekognition = AWSRekognition.default()
        
        let im = AWSRekognitionImage()
        im?.bytes = imageViewData
        let req = AWSRekognitionDetectModerationLabelsRequest()
        req?.image = im
        rekognition.detectModerationLabels(req!){
            (result, error) in
            guard error == nil else {
                closure(false, error)
                return
            }
            if result != nil {
                if let results = result?.moderationLabels {
                    let score = FlexManager.safetyScore(results)
                    if let num = score.first?.key, let arr = score.first?.value {
                        FlexManager.flex?.safetyScore = num
                        FlexManager.flex?.safetyIssues = arr
                    } else {
                        FlexManager.flex?.safetyScore = 0.0
                        FlexManager.flex?.safetyIssues = []
                    }
                } else {
                    FlexManager.flex?.safetyScore = 0.0
                    FlexManager.flex?.safetyIssues = []
                }
                closure(true, nil)
            } else {
                print("No result")
                FlexManager.flex?.safetyScore = -1.0 // failed recognition
                FlexManager.flex?.safetyIssues = []
                closure(false, GFError(errorType: .rekognitionFailed))
            }
        }
    }
    
    // MARK: - register and login AtGameFlexWithEmail
    // first time login = register and autoLogin
    static func registerAtGameFlexWithEmail(_ email: String, closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kRegisterEndPoint)"
        if let url = URL(string: urlString) {

            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue("702bf98b-60b2-4206-ae9c-7e587714a22a", forHTTPHeaderField: "Authorization")
            
            request.httpBody = try? JSONSerialization.data(withJSONObject: ["email": email], options: .prettyPrinted)
            
            let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                if let error = error {
                    print ("error: \(error.localizedDescription)")
                    closure(false, error)
                }
                let code = (response as? HTTPURLResponse)?.statusCode
                guard let response = response as? HTTPURLResponse,
                    (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, error)
                            return
                        }
                    if code == 400 {
                        closure(false, GFError(errorType: .cantRegisterExistingAccount))
                        return
                    }
                    closure(false, GFError(errorType: .unknownError))
                    return
                }
                guard let data = data, error == nil else { return }

                let json = JSON(data)
                User.userId = json["userId"].string ?? ""
                User.refreshToken = json["token"].string
                closure(true, nil)
            })
            task.resume()
        }
    }
    
    // GameFlex server assumes that if a client presents the email, FireBase has authenticated the user appropriately
    static func loginAtGameFlexWithEmail(_ email: String, closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        let urlString = "\(kBaseURL)/\(kLoginEndPoint)"
        if let url = URL(string: urlString) {
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue("702bf98b-60b2-4206-ae9c-7e587714a22a", forHTTPHeaderField: "Authorization")
            
            request.httpBody = try? JSONSerialization.data(withJSONObject: ["email": email], options: .prettyPrinted)
            
            let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                if let error = error {
                    print ("error: \(error.localizedDescription)")
                    closure(false, error)
                }
                let code = (response as? HTTPURLResponse)?.statusCode
                guard let response = response as? HTTPURLResponse,
                      (200...299).contains(response.statusCode) else {
                    // code 500 is internal server error for existing account, note error == nil in this case too (sigh).
                    if code == 500 {
                        registerAtGameFlexWithEmail(email) { (success, error) in
                            guard let data = data, error == nil else { return }
                            
                            let json = JSON(data)
                            User.userId = json["userId"].string ?? ""
                            User.refreshToken = json["token"].string
                            closure(true, nil)
                            return
                        }
                    } else {
                        closure(false, GFError(errorType: nil))
                        return
                    }
                    return
                }
                guard let data = data, error == nil else { return }
                
                let json = JSON(data)
                User.userId = json["userId"].string ?? ""
                User.refreshToken = json["token"].string
                closure(true, nil)
            })
            task.resume()
            
        }
    }
    
    // MARK: - User services
    static func getUserProfile(_ userId: String, closure: @escaping (_ success: Bool, _ flexter: Flexter?, _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kUserEndPoint)/\(userId)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, nil, error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, nil, error)
                            return
                        }
                        closure(false, nil, error)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    let result = Flexter.parseTheFlexter(json: json)
                    closure(true, result, nil)
                })
                task.resume()
            }
        }
    }
    
    static func getFollowers(_ userId: String, closure: @escaping (_ success: Bool, _ results: [Channel], _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kFollowersEndPoint)?userid=\(userId)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, [], error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, [], error)
                            return
                        }
                        closure(false, [], error)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    let result = Channel.parseArray(json["followers"].arrayValue)
                    closure(true, result, nil)
                })
                task.resume()
            }
        }
    }
        
    // converts one following into array of following and updates user profile
    static func followThis(_ id: [String], _ closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        patchUserProfile(content: nil, [.following: id]) { (success, error) in
            if let userId = User.userId {
                GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                    if success, let flexter = flexter {
                        User.updateTheUser(flexter)
                        closure(success, error)
                    }
                }
            }
        }
    }
    
    // converts one followingX into array of followingX and updates user profile
    static func unFollowThis(_ id: [String], _ closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        patchUserProfile(content: nil, [.followingX: id]) { (success, error) in
            closure(success, error)
            if let userId = User.userId {
                GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                    if success, let flexter = flexter {
                        User.updateTheUser(flexter)
                        closure(success, error)
                    }
                }
            }
        }
    }
    
    // accepts an invitation to join a channel offered by another user
    static func acceptThis(_ notificationId: String, _ closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {        
        if let url = URL(string: "\(kBaseURL)/\(kAcceptInviteEndPoint)/\(notificationId)"), let token = User.refreshToken, User.refreshToken != ""  {
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            request.addValue(token, forHTTPHeaderField: "Authorization")

            let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                if let error = error {
                    print ("error: \(error.localizedDescription)")
                    closure(false, error)
                }
                guard let response = response as? HTTPURLResponse,
                    (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, error)
                            return
                        }
                        closure(false, GFError(errorType: nil))
                        return
                }
                if let userId = User.userId {
                    GFNetworkServices.getUserProfile(userId) { (success, flexter, error) in
                        if success, let flexter = flexter {
                            User.updateTheUser(flexter)
                            closure(success, error)
                        }
                    }
                }
            })
            task.resume()
        } else {
            closure(false, GFError.init(errorType: .flawedURL))
        }
    }

        
    // works with either content or contents, but not both
    static func patchUserProfile(content: [ProfileUpdateDataType: String]?, _ contents: [ProfileUpdateDataType: [String]]? = nil, closure: @escaping (_ success: Bool, _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kBaseURL)/\(kUserEndPoint)"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "PATCH"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                if let content = content {
                    var update: [String: String] = [:]
                    for tuple in content {
                        update[tuple.key.rawValue] = tuple.value
                    }
                    request.httpBody = try? JSONSerialization.data(withJSONObject: update, options: .prettyPrinted)
                } else {
                    if let contents = contents {
                        var update: [String: [String]] = [:]
                        for tuple in contents {
                            var arr: [String] = []
                            for stuff in tuple.value {
                                arr.append(stuff)
                            }
                            update[tuple.key.rawValue] = arr
                        }
                        request.httpBody = try? JSONSerialization.data(withJSONObject: update, options: .prettyPrinted)
                    }
                }
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, error)
                            return
                        }
                        closure(false, error)
                        return
                    }
                    guard error == nil else {
                        closure(false, error)
                        return
                    }
                    closure(true, nil)
                })
                task.resume()
            }
        }
    }
    
    static func uploadMediaToUserProfile(image: UIImage, closure: @escaping (_ success: Bool, _ error: Error? ) -> Void) {
        if let im = image.compress(to: AppInfo.maxFileSizekb), let data = im.jpegData(compressionQuality: 1.0) /* 2MB limit*/, let userId = User.userId, userId != "", let token = User.refreshToken, User.refreshToken != "" {
            let dated = String(format: "%.f", Date().timeIntervalSinceReferenceDate)
            let urlString = String(format: kUploadMediaURL, userId, "profile/\(dated)_\(GFDefaults.completedFlexCount).png")
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "PUT"
                request.httpBody = data
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        closure(false, error)
                        return
                    }
                    if let data = data, let resultUrlString = String(data: data, encoding: .utf8) {
                        self.patchUserProfile(content: [.profileImage: resultUrlString]) { (success, error) in
                            closure(success, error)
                        }
                    } else {
                        closure(false, GFError(errorType: .failedURLPut))
                    }
                })
                task.resume()
            }
        }
    }
    
    static func getSearchResults(_ name: String, closure: @escaping (_ success: Bool, _ result: [SearchResult], _ error: Error?) -> Void) {
        if let token = User.refreshToken, User.refreshToken != "" {
            let urlString = "\(kSearchURL)/\(kSearchUsersEndPoint)?name=\(name)&limit=100"
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                let task = URLSession.shared.dataTask(with: request, completionHandler: { (data, response, error) in
                    if let error = error {
                        print ("error: \(error.localizedDescription)")
                        closure(false, [], error)
                    }
                    guard let response = response as? HTTPURLResponse,
                          (200...299).contains(response.statusCode) else {
                        guard error == nil else {
                            closure(false, [], error)
                            return
                        }
                        closure(false, [], error)
                        return
                    }
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data).dictionary?["entities"]
                    var results: [SearchResult] = []
                    if let jays = json?.array {
                        for jay in jays {
                        results.append(SearchResult.parse(jay))
                    }
                    
                    closure(true, results, nil)
                    }
                })
                task.resume()
            }
        }
    }
    
    static func getUserFlexes(channelId: String, top: Bool, _ closure: @escaping (_ success: Bool, _ results: [Flex], _ error: Error?) -> Void) {
        if channelId != "" {
            var urlString = "\(kBaseURL)/\(kUserFlexesEndPoint)/\(channelId)"
            if channelId == User.userId {
                urlString = "\(kBaseURL)/\(kUserFlexesEndPoint)"
            }
            if !top, let dict = GFDefaults.channelTimeStamps, let stamp = dict[channelId] {
                urlString = "\(urlString)&timestamp=\(stamp)?limit=25"
        }
            if !urlString.contains("limit") {
                urlString = "\(urlString)?limit=25"
            }
        
            if let url = URL(string: urlString) {
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.addValue("application/json", forHTTPHeaderField: "Content-Type")
                request.addValue("application/json", forHTTPHeaderField: "Accept")
                let token = User.refreshToken ?? "702bf98b-60b2-4206-ae9c-7e587714a22a"
                request.addValue(token, forHTTPHeaderField: "Authorization")
                
                let defaultSession = URLSession(configuration: .default)
                
                var dataTask: URLSessionDataTask?
                dataTask = defaultSession.dataTask(with: request) { data, response, error in
                    
                    guard let data = data, error == nil else { return }
                    
                    let json = JSON(data)
                    if json["code"].intValue == 500001 {
                        closure(false, [], GFError.init(errorType: .unknownError))
                        return
                    }
                    guard let arr = json["body"]["flexes"].array else {
                        closure(false, [], nil)
                        return
                    }
                    guard !arr.isEmpty else {
                        closure(false, [], error)
                        return
                    }
                    let result = Flex.parse(arr)
                    
                    closure(true, result, nil)
                }
                dataTask?.resume()
            }
        }
        closure(false, [], GFError(errorType: .flawedURL))
    }

}

