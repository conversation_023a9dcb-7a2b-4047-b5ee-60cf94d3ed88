<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="u8y-Qf-ik2">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Navigation Controller-->
        <scene sceneID="a3A-2f-KsS">
            <objects>
                <navigationController storyboardIdentifier="vc3" id="fY6-Ik-Wj6" customClass="GFNavigationController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="" image="navCreate" selectedImage="navCreate" id="2du-Pl-n98"/>
                    <navigationItem key="navigationItem" id="E8s-Hb-csW"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" barStyle="black" translucent="NO" id="yDs-3O-flK">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="YIE-el-Qzz" kind="relationship" relationship="rootViewController" id="zoL-p0-CbW"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iUn-1O-aaG" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1443" y="136"/>
        </scene>
        <!--PreCameraViewController-->
        <scene sceneID="oVd-ya-GuH">
            <objects>
                <viewController storyboardIdentifier="PreCameraViewController" title="PreCameraViewController" id="YIE-el-Qzz" customClass="PreCameraViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="dsj-NH-Yxi">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="808"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="nnw-sw-XXH"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                    <navigationItem key="navigationItem" id="27J-zL-3XM"/>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="nib-JD-0by" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-568.1159420289855" y="135.9375"/>
        </scene>
        <!--Channel Directory View Controller-->
        <scene sceneID="HZs-Z9-OW3">
            <objects>
                <viewController storyboardIdentifier="ChannelDirectoryViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="UYG-NT-s6l" customClass="ChannelDirectoryViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="VFU-VY-viG">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="pWf-Nx-LHd">
                                <rect key="frame" x="0.0" y="88" width="414" height="808"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="PEL-rC-T2N"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="pWf-Nx-LHd" firstAttribute="top" secondItem="VFU-VY-viG" secondAttribute="top" constant="88" id="454-Ob-2db"/>
                            <constraint firstAttribute="trailing" secondItem="pWf-Nx-LHd" secondAttribute="trailing" id="JlF-Cu-W2Z"/>
                            <constraint firstItem="pWf-Nx-LHd" firstAttribute="leading" secondItem="VFU-VY-viG" secondAttribute="leading" id="cCH-hB-FH3"/>
                            <constraint firstAttribute="bottom" secondItem="pWf-Nx-LHd" secondAttribute="bottom" id="jVZ-ng-vTO"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="pWf-Nx-LHd" id="0OD-GD-YEQ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hd8-xu-d01" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-2904.347826086957" y="1208.7053571428571"/>
        </scene>
        <!--Channel Navigation Controller-->
        <scene sceneID="ypC-4m-PBj">
            <objects>
                <navigationController storyboardIdentifier="vc1" id="0du-1w-HDA" customClass="GFChannelNavigationController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="" image="navHome" id="8gO-B3-m0D"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Uah-8f-sMB">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="jHB-Kj-13M" kind="relationship" relationship="rootViewController" id="Ks6-5Z-Ren"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="t0d-Qd-rpH" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1880" y="1515"/>
        </scene>
        <!--MainViewController-->
        <scene sceneID="A7Z-BW-2Xy">
            <objects>
                <viewController storyboardIdentifier="MainViewController" title="MainViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="zEn-cP-YqI" customClass="MainViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8IF-SD-Yc6">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="gab-cK-VXX">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="s3c-TL-bIZ"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="gab-cK-VXX" secondAttribute="bottom" id="ByZ-nE-aJP"/>
                            <constraint firstItem="gab-cK-VXX" firstAttribute="trailing" secondItem="8IF-SD-Yc6" secondAttribute="trailing" id="FNO-VJ-UCh"/>
                            <constraint firstItem="gab-cK-VXX" firstAttribute="leading" secondItem="8IF-SD-Yc6" secondAttribute="leading" id="ZSh-rM-z6K"/>
                            <constraint firstItem="gab-cK-VXX" firstAttribute="top" secondItem="8IF-SD-Yc6" secondAttribute="top" id="s8Q-jE-Bix"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="b7o-1x-y3c"/>
                    <connections>
                        <outlet property="tableView" destination="gab-cK-VXX" id="j7R-vj-ZB9"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="rIX-xs-LtH" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1165" y="1509"/>
        </scene>
        <!--Home View Controller-->
        <scene sceneID="VSx-7y-cFE">
            <objects>
                <viewController storyboardIdentifier="HomeViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="jHB-Kj-13M" customClass="HomeViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="HgI-xE-VTU">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KnD-ky-E61">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <connections>
                                    <segue destination="zEn-cP-YqI" kind="embed" id="TZ7-S6-rJV"/>
                                </connections>
                            </containerView>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="2Xe-Pz-zYa">
                                <rect key="frame" x="0.0" y="141" width="414" height="78"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="78" id="nor-YO-47p"/>
                                </constraints>
                                <color key="separatorColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <inset key="separatorInset" minX="10000" minY="0.0" maxX="0.0" maxY="0.0"/>
                            </tableView>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="CRe-Qi-lDD">
                                <rect key="frame" x="0.0" y="88" width="414" height="54"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="54" id="lUV-MG-zQN"/>
                                </constraints>
                                <color key="separatorColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="exF-VQ-1hf"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="exF-VQ-1hf" firstAttribute="top" secondItem="CRe-Qi-lDD" secondAttribute="top" id="G9a-oS-L06"/>
                            <constraint firstItem="KnD-ky-E61" firstAttribute="leading" secondItem="exF-VQ-1hf" secondAttribute="leading" id="JiK-5e-1vI"/>
                            <constraint firstItem="KnD-ky-E61" firstAttribute="top" secondItem="HgI-xE-VTU" secondAttribute="top" id="Su0-fL-Nyx"/>
                            <constraint firstItem="CRe-Qi-lDD" firstAttribute="leading" secondItem="exF-VQ-1hf" secondAttribute="leading" id="ZTy-6M-lTP"/>
                            <constraint firstItem="2Xe-Pz-zYa" firstAttribute="leading" secondItem="exF-VQ-1hf" secondAttribute="leading" id="aH8-Lc-EAI"/>
                            <constraint firstItem="2Xe-Pz-zYa" firstAttribute="trailing" secondItem="exF-VQ-1hf" secondAttribute="trailing" id="c8f-mt-pwq"/>
                            <constraint firstItem="2Xe-Pz-zYa" firstAttribute="top" secondItem="CRe-Qi-lDD" secondAttribute="bottom" constant="-1" id="dkI-YX-XGJ"/>
                            <constraint firstAttribute="bottom" secondItem="KnD-ky-E61" secondAttribute="bottom" id="ehk-FE-HLD"/>
                            <constraint firstItem="exF-VQ-1hf" firstAttribute="trailing" secondItem="KnD-ky-E61" secondAttribute="trailing" id="g7j-E6-pJV"/>
                            <constraint firstItem="exF-VQ-1hf" firstAttribute="trailing" secondItem="CRe-Qi-lDD" secondAttribute="trailing" id="j75-4z-H3d"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="ix8-KV-jud"/>
                    <connections>
                        <outlet property="flagTableView" destination="2Xe-Pz-zYa" id="HOb-DQ-tpI"/>
                        <outlet property="mainContainerView" destination="KnD-ky-E61" id="0NX-Hu-MGL"/>
                        <outlet property="tableView" destination="CRe-Qi-lDD" id="70A-TL-NH9"/>
                        <outlet property="tableViewHeightConstraint" destination="lUV-MG-zQN" id="Mp9-zG-pnl"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="W0F-S4-ASq" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1294.2028985507247" y="2294.1964285714284"/>
        </scene>
        <!--Comment View Controller-->
        <scene sceneID="aik-fc-hl4">
            <objects>
                <viewController storyboardIdentifier="CommentViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="2sN-Gy-7gn" customClass="CommentViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="RJc-SU-8kq">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="1Hw-dC-g4j">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <inset key="separatorInset" minX="15" minY="0.0" maxX="15" maxY="0.0"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="s7J-2Q-vUu"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="1Hw-dC-g4j" firstAttribute="top" secondItem="RJc-SU-8kq" secondAttribute="top" id="3VZ-oa-Wg6"/>
                            <constraint firstItem="1Hw-dC-g4j" firstAttribute="leading" secondItem="RJc-SU-8kq" secondAttribute="leading" id="I2S-lj-chX"/>
                            <constraint firstAttribute="bottom" secondItem="1Hw-dC-g4j" secondAttribute="bottom" id="MnD-ff-VYW"/>
                            <constraint firstAttribute="trailing" secondItem="1Hw-dC-g4j" secondAttribute="trailing" id="dVf-Hg-cEY"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="1Hw-dC-g4j" id="X2T-D3-ab1"/>
                        <outlet property="tableViewBottomConstraint" destination="MnD-ff-VYW" id="DcL-rR-SmF"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Vde-u4-AUH" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-413.04347826086962" y="2312.9464285714284"/>
        </scene>
        <!--Reflex View Controller-->
        <scene sceneID="d7l-Zx-FRv">
            <objects>
                <viewController storyboardIdentifier="ReflexViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="8dL-ub-CxC" customClass="ReflexViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Uer-oq-HwL">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="i3N-MW-O2L">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="0.0" minimumInteritemSpacing="0.0" id="om3-Lg-T2w">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                            </collectionView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="q1b-GI-WfW"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="i3N-MW-O2L" firstAttribute="top" secondItem="Uer-oq-HwL" secondAttribute="top" id="6Yp-XX-QNH"/>
                            <constraint firstAttribute="bottom" secondItem="i3N-MW-O2L" secondAttribute="bottom" id="KGJ-Xa-dA1"/>
                            <constraint firstAttribute="trailing" secondItem="i3N-MW-O2L" secondAttribute="trailing" id="r14-0o-bIC"/>
                            <constraint firstItem="i3N-MW-O2L" firstAttribute="leading" secondItem="Uer-oq-HwL" secondAttribute="leading" id="uP1-Ue-So2"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="collectionView" destination="i3N-MW-O2L" id="hjR-OC-3Ga"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="jHd-m2-Vq2" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="371.01449275362324" y="2319.6428571428569"/>
        </scene>
        <!--My Feed View Controller-->
        <scene sceneID="ndL-CU-7sT">
            <objects>
                <viewController storyboardIdentifier="MyFeedViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="nQj-w4-MOS" customClass="MyFeedViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="1ty-WS-QaL">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="UCJ-Kr-vx7">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hu2-L4-GMT"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="UCJ-Kr-vx7" firstAttribute="leading" secondItem="hu2-L4-GMT" secondAttribute="leading" id="bkK-aH-7RL"/>
                            <constraint firstItem="UCJ-Kr-vx7" firstAttribute="top" secondItem="1ty-WS-QaL" secondAttribute="top" id="sYS-jr-Cji"/>
                            <constraint firstAttribute="bottom" secondItem="UCJ-Kr-vx7" secondAttribute="bottom" id="wZt-Ip-t6S"/>
                            <constraint firstItem="UCJ-Kr-vx7" firstAttribute="trailing" secondItem="hu2-L4-GMT" secondAttribute="trailing" id="we4-ZP-k3z"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="UCJ-Kr-vx7" id="grH-EC-jHj"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Bmh-Y4-fXX" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-517.39130434782612" y="1508.7053571428571"/>
        </scene>
        <!--Whats New View Controller-->
        <scene sceneID="cZ5-cm-ADz">
            <objects>
                <viewController storyboardIdentifier="WhatsNewViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="Ag8-ga-iPC" customClass="WhatsNewViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="4G6-8e-r97">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" editable="NO" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FGj-4S-gLq">
                                <rect key="frame" x="20" y="93" width="374" height="756"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                <dataDetectorType key="dataDetectorTypes" phoneNumber="YES" link="YES"/>
                            </textView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="jWU-ky-nRS"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="FGj-4S-gLq" firstAttribute="leading" secondItem="jWU-ky-nRS" secondAttribute="leading" constant="20" id="akh-rb-NUh"/>
                            <constraint firstItem="jWU-ky-nRS" firstAttribute="bottom" secondItem="FGj-4S-gLq" secondAttribute="bottom" constant="13" id="f4b-zu-oI4"/>
                            <constraint firstItem="jWU-ky-nRS" firstAttribute="trailing" secondItem="FGj-4S-gLq" secondAttribute="trailing" constant="20" id="kGP-tY-5v7"/>
                            <constraint firstItem="FGj-4S-gLq" firstAttribute="top" secondItem="jWU-ky-nRS" secondAttribute="top" constant="49" id="lz0-XX-Sfd"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="textView" destination="FGj-4S-gLq" id="FlB-LP-1kA"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="wu2-jF-DAP" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1549" y="1515"/>
        </scene>
        <!--Sign Up View Controller-->
        <scene sceneID="YFg-7s-Xy0">
            <objects>
                <viewController storyboardIdentifier="SignUpViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="f5y-20-4Tz" customClass="SignUpViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="OeZ-YB-gCI">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="gameflexBranding" translatesAutoresizingMaskIntoConstraints="NO" id="lgV-eQ-OJX">
                                <rect key="frame" x="20" y="44" width="374" height="210"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="lgV-eQ-OJX" secondAttribute="height" multiplier="187:105" id="X9w-PC-1Ie"/>
                                </constraints>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wrR-jr-onb">
                                <rect key="frame" x="99.5" y="803" width="215" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="AxH-sc-gqa"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="215" id="Vuo-MD-Yia"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="18"/>
                                <state key="normal" title="Log in"/>
                                <connections>
                                    <action selector="didTapLoginButton" destination="f5y-20-4Tz" eventType="touchUpInside" id="l2N-Ib-gNi"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5y8-rF-ohe">
                                <rect key="frame" x="99.5" y="753" width="215" height="40"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="8X3-io-xNS"/>
                                    <constraint firstAttribute="width" constant="215" id="UOj-x1-k2h"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sd2-rl-5oA">
                                <rect key="frame" x="50" y="703" width="314" height="40"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PPY-Oi-0br" customClass="GIDSignInButton">
                                        <rect key="frame" x="49.5" y="0.0" width="215" height="56"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="XUw-Eg-ZnM"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="314" id="8jY-5C-Noy"/>
                                    <constraint firstAttribute="height" constant="40" id="ZZE-X8-DNK"/>
                                    <constraint firstItem="PPY-Oi-0br" firstAttribute="top" secondItem="Sd2-rl-5oA" secondAttribute="top" id="dw4-Ud-enN"/>
                                    <constraint firstItem="PPY-Oi-0br" firstAttribute="centerX" secondItem="Sd2-rl-5oA" secondAttribute="centerX" id="e6F-6i-BJK"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RWo-MR-HNz">
                                <rect key="frame" x="69.5" y="656" width="275" height="43"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" image="signUpWithEmail" translatesAutoresizingMaskIntoConstraints="NO" id="sfa-wn-yJD">
                                <rect key="frame" x="69.5" y="656" width="275" height="43"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="43" id="ex5-dt-VKo"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="whh-5u-vyA"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="wrR-jr-onb" secondAttribute="bottom" constant="53" id="3CN-DB-j7u"/>
                            <constraint firstItem="Sd2-rl-5oA" firstAttribute="top" secondItem="sfa-wn-yJD" secondAttribute="bottom" constant="4" id="8UM-yQ-K22"/>
                            <constraint firstItem="RWo-MR-HNz" firstAttribute="trailing" secondItem="sfa-wn-yJD" secondAttribute="trailing" id="CC9-Dv-fz6"/>
                            <constraint firstItem="RWo-MR-HNz" firstAttribute="top" secondItem="sfa-wn-yJD" secondAttribute="top" id="CXa-K6-r3W"/>
                            <constraint firstItem="lgV-eQ-OJX" firstAttribute="leading" secondItem="whh-5u-vyA" secondAttribute="leading" constant="20" id="JfS-0E-wFg"/>
                            <constraint firstItem="5y8-rF-ohe" firstAttribute="top" secondItem="Sd2-rl-5oA" secondAttribute="bottom" constant="10" id="N7T-eB-FiF"/>
                            <constraint firstItem="wrR-jr-onb" firstAttribute="top" secondItem="5y8-rF-ohe" secondAttribute="bottom" constant="10" id="SYx-Xr-FeC"/>
                            <constraint firstItem="RWo-MR-HNz" firstAttribute="leading" secondItem="sfa-wn-yJD" secondAttribute="leading" id="T8q-Tp-c0Q"/>
                            <constraint firstItem="wrR-jr-onb" firstAttribute="centerX" secondItem="OeZ-YB-gCI" secondAttribute="centerX" id="Uqq-aY-N19"/>
                            <constraint firstItem="lgV-eQ-OJX" firstAttribute="top" secondItem="whh-5u-vyA" secondAttribute="top" id="Yau-KU-6Hx"/>
                            <constraint firstItem="Sd2-rl-5oA" firstAttribute="centerX" secondItem="sfa-wn-yJD" secondAttribute="centerX" id="aVO-We-YSY"/>
                            <constraint firstItem="whh-5u-vyA" firstAttribute="trailing" secondItem="lgV-eQ-OJX" secondAttribute="trailing" constant="20" id="cpo-z7-E3f"/>
                            <constraint firstItem="sfa-wn-yJD" firstAttribute="centerX" secondItem="OeZ-YB-gCI" secondAttribute="centerX" id="jHr-iZ-50Z"/>
                            <constraint firstItem="RWo-MR-HNz" firstAttribute="bottom" secondItem="sfa-wn-yJD" secondAttribute="bottom" id="kVd-xf-Af1"/>
                            <constraint firstItem="5y8-rF-ohe" firstAttribute="centerX" secondItem="OeZ-YB-gCI" secondAttribute="centerX" id="qOH-qe-qr8"/>
                            <constraint firstItem="PPY-Oi-0br" firstAttribute="width" secondItem="sfa-wn-yJD" secondAttribute="width" multiplier="0.781818" id="sGQ-fH-aw8"/>
                            <constraint firstItem="PPY-Oi-0br" firstAttribute="width" secondItem="5y8-rF-ohe" secondAttribute="width" id="yxb-KT-zYs"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="googleView" destination="Sd2-rl-5oA" id="z9S-eT-yqj"/>
                        <outlet property="loginButton" destination="wrR-jr-onb" id="g4U-Ti-UC7"/>
                        <outlet property="logoImageView" destination="lgV-eQ-OJX" id="Jaa-nF-rsm"/>
                        <outlet property="signInWithAppleView" destination="5y8-rF-ohe" id="7Ge-RG-JBP"/>
                        <outlet property="signUpWithEmail" destination="RWo-MR-HNz" id="rfk-3m-GtF"/>
                        <outlet property="signUpWithGoogleButton" destination="PPY-Oi-0br" id="B8p-rj-8m9"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5f2-z1-8e0" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-549.27536231884062" y="802.90178571428567"/>
        </scene>
        <!--Email View Controller-->
        <scene sceneID="Xx3-UF-kmJ">
            <objects>
                <viewController storyboardIdentifier="EmailViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="ur9-z1-V0Z" customClass="EmailViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2sk-Vh-nwY">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsVerticalScrollIndicator="NO" bouncesZoom="NO" dataMode="prototypes" style="grouped" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" sectionFooterHeight="18" translatesAutoresizingMaskIntoConstraints="NO" id="KZZ-KL-Xhq">
                                <rect key="frame" x="0.0" y="117" width="414" height="644"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3TI-P8-ij9">
                                <rect key="frame" x="87" y="44" width="240" height="64.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SnK-fc-MP2" customClass="LoginHeaderView" customModule="GameFlex" customModuleProvider="target">
                                        <rect key="frame" x="43" y="10" width="154" height="24"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="WWM-I4-bdi"/>
                                            <constraint firstAttribute="width" constant="154" id="lEz-da-eLe"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gsz-dH-McA">
                                        <rect key="frame" x="8" y="44" width="224" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="SnK-fc-MP2" firstAttribute="top" secondItem="3TI-P8-ij9" secondAttribute="top" constant="10" id="Fuk-Ch-Fx1"/>
                                    <constraint firstItem="gsz-dH-McA" firstAttribute="top" secondItem="SnK-fc-MP2" secondAttribute="bottom" constant="10" id="Iyg-aK-XvZ"/>
                                    <constraint firstAttribute="width" constant="240" id="Vnd-1y-9Yk"/>
                                    <constraint firstItem="SnK-fc-MP2" firstAttribute="centerX" secondItem="3TI-P8-ij9" secondAttribute="centerX" id="YIi-Dr-ZCo"/>
                                    <constraint firstAttribute="bottom" secondItem="gsz-dH-McA" secondAttribute="bottom" id="Zq5-1w-nMV"/>
                                    <constraint firstAttribute="trailing" secondItem="gsz-dH-McA" secondAttribute="trailing" constant="8" id="oT2-7h-dCI"/>
                                    <constraint firstItem="gsz-dH-McA" firstAttribute="leading" secondItem="3TI-P8-ij9" secondAttribute="leading" constant="8" id="ofh-YE-6Ja"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="D43-Ct-D49">
                                <rect key="frame" x="50" y="836" width="314" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="YHw-05-DOU"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="18"/>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="didTapSignUpButton" destination="ur9-z1-V0Z" eventType="touchUpInside" id="eLI-JT-bJW"/>
                                </connections>
                            </button>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" editable="NO" textAlignment="center" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Uub-zE-aoo">
                                <rect key="frame" x="36" y="768" width="342" height="66"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                <dataDetectorType key="dataDetectorTypes" link="YES"/>
                            </textView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="rWL-OI-D9U"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailingMargin" secondItem="Uub-zE-aoo" secondAttribute="trailing" constant="16" id="0Ch-rN-6zI"/>
                            <constraint firstItem="3TI-P8-ij9" firstAttribute="top" secondItem="rWL-OI-D9U" secondAttribute="top" id="2GM-1q-2Q8"/>
                            <constraint firstItem="KZZ-KL-Xhq" firstAttribute="top" secondItem="3TI-P8-ij9" secondAttribute="bottom" constant="8.5" id="9kL-Mz-DCs"/>
                            <constraint firstItem="3TI-P8-ij9" firstAttribute="centerX" secondItem="2sk-Vh-nwY" secondAttribute="centerX" id="CCf-8h-Ju9"/>
                            <constraint firstItem="KZZ-KL-Xhq" firstAttribute="trailing" secondItem="2sk-Vh-nwY" secondAttribute="trailing" id="ODc-JQ-etm"/>
                            <constraint firstAttribute="bottom" secondItem="D43-Ct-D49" secondAttribute="bottom" constant="20" id="TAe-dE-vwk"/>
                            <constraint firstItem="KZZ-KL-Xhq" firstAttribute="leading" secondItem="2sk-Vh-nwY" secondAttribute="leading" id="dLJ-OE-bbK"/>
                            <constraint firstAttribute="trailing" secondItem="D43-Ct-D49" secondAttribute="trailing" constant="50" id="nPU-Ct-j2n"/>
                            <constraint firstAttribute="bottom" secondItem="KZZ-KL-Xhq" secondAttribute="bottom" constant="135" id="o4q-Gy-3Aj"/>
                            <constraint firstItem="Uub-zE-aoo" firstAttribute="top" secondItem="KZZ-KL-Xhq" secondAttribute="bottom" constant="7" id="qxB-bA-jrr"/>
                            <constraint firstItem="D43-Ct-D49" firstAttribute="leading" relation="lessThanOrEqual" secondItem="2sk-Vh-nwY" secondAttribute="leading" constant="50" id="rR7-Vg-ln1"/>
                            <constraint firstItem="D43-Ct-D49" firstAttribute="top" secondItem="Uub-zE-aoo" secondAttribute="bottom" constant="2" id="uxQ-Cc-TSL"/>
                            <constraint firstItem="Uub-zE-aoo" firstAttribute="leading" secondItem="2sk-Vh-nwY" secondAttribute="leadingMargin" constant="16" id="vkx-XZ-a6Z"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="headerView" destination="3TI-P8-ij9" id="0dW-vC-B4P"/>
                        <outlet property="legalTextView" destination="Uub-zE-aoo" id="fMk-eI-t5y"/>
                        <outlet property="logoLabel" destination="gsz-dH-McA" id="Qc7-GE-NFr"/>
                        <outlet property="logoView" destination="SnK-fc-MP2" id="pNJ-v7-YFG"/>
                        <outlet property="signUpButton" destination="D43-Ct-D49" id="DnN-IF-KEP"/>
                        <outlet property="tableView" destination="KZZ-KL-Xhq" id="wjx-Ri-P3s"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5Ve-IU-PRZ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="134.78260869565219" y="798.88392857142856"/>
        </scene>
        <!--Bug View Controller-->
        <scene sceneID="kYf-TJ-pqr">
            <objects>
                <viewController storyboardIdentifier="BugViewController" id="AEy-iG-PsA" customClass="BugViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="5hz-US-SpH">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eAU-Ak-Bw8">
                                <rect key="frame" x="20" y="44" width="374" height="49"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="49" id="wtI-zc-NQF"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SEg-u5-ugK">
                                <rect key="frame" x="20" y="183" width="374" height="150"/>
                                <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="xKw-Cl-xlm"/>
                                </constraints>
                                <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                            </textView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Qyj-rF-x2Z">
                                <rect key="frame" x="20" y="343" width="374" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="LUe-jY-oWF"/>
                                </constraints>
                                <state key="normal" title="Attach photo/movie from photo library"/>
                                <connections>
                                    <action selector="didTapButton:" destination="AEy-iG-PsA" eventType="touchUpInside" id="pea-DC-gp4"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Tni-yu-bCd">
                                <rect key="frame" x="20" y="843" width="374" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="Jca-NW-mw8"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="didTapButton:" destination="AEy-iG-PsA" eventType="touchUpInside" id="zS3-i1-SKl"/>
                                </connections>
                            </button>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="S2N-Hz-heF">
                                <rect key="frame" x="20" y="782" width="374" height="37"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Fue-xl-Qqu">
                                        <rect key="frame" x="0.0" y="0.0" width="13" height="37"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Fue-xl-Qqu" firstAttribute="leading" secondItem="S2N-Hz-heF" secondAttribute="leading" id="GK6-1q-NGm"/>
                                    <constraint firstItem="Fue-xl-Qqu" firstAttribute="top" secondItem="S2N-Hz-heF" secondAttribute="top" id="Lhw-64-6sb"/>
                                    <constraint firstAttribute="trailing" secondItem="Fue-xl-Qqu" secondAttribute="trailing" constant="361" id="S04-lK-peR"/>
                                    <constraint firstItem="Fue-xl-Qqu" firstAttribute="centerY" secondItem="S2N-Hz-heF" secondAttribute="centerY" id="pVs-PH-Ir6"/>
                                    <constraint firstAttribute="height" constant="37" id="vo9-MW-v5j"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M5e-W1-UHL">
                                <rect key="frame" x="20" y="135" width="100" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="100" id="q3p-Sz-mmS"/>
                                </constraints>
                                <state key="normal" title="Screenshot?"/>
                                <connections>
                                    <action selector="didTapButton:" destination="AEy-iG-PsA" eventType="touchUpInside" id="ELv-fl-KR1"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="249" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4BN-6A-cvq">
                                <rect key="frame" x="292" y="135" width="102" height="30"/>
                                <state key="normal" title="ScreenMovie?"/>
                                <connections>
                                    <action selector="didTapButton:" destination="AEy-iG-PsA" eventType="touchUpInside" id="QcC-cS-9FZ"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qbe-sP-tvu"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="M5e-W1-UHL" firstAttribute="top" secondItem="eAU-Ak-Bw8" secondAttribute="bottom" constant="42" id="1uR-00-Q54"/>
                            <constraint firstAttribute="bottom" secondItem="Tni-yu-bCd" secondAttribute="bottom" constant="13" id="2SX-Ta-OHp"/>
                            <constraint firstItem="Qyj-rF-x2Z" firstAttribute="leading" secondItem="S2N-Hz-heF" secondAttribute="leading" id="4Wf-iI-lXS"/>
                            <constraint firstItem="SEg-u5-ugK" firstAttribute="trailing" secondItem="Qyj-rF-x2Z" secondAttribute="trailing" id="6tZ-PW-4Uh"/>
                            <constraint firstItem="Qyj-rF-x2Z" firstAttribute="trailing" secondItem="S2N-Hz-heF" secondAttribute="trailing" id="7gp-nc-gdp"/>
                            <constraint firstItem="4BN-6A-cvq" firstAttribute="trailing" secondItem="SEg-u5-ugK" secondAttribute="trailing" id="A9G-oB-G0t"/>
                            <constraint firstItem="eAU-Ak-Bw8" firstAttribute="trailing" secondItem="4BN-6A-cvq" secondAttribute="trailing" id="ATO-42-O8r"/>
                            <constraint firstItem="eAU-Ak-Bw8" firstAttribute="trailing" secondItem="5hz-US-SpH" secondAttribute="trailingMargin" id="BVz-cI-TXT"/>
                            <constraint firstItem="Qyj-rF-x2Z" firstAttribute="top" secondItem="SEg-u5-ugK" secondAttribute="bottom" constant="10" id="EcO-Wv-nY7"/>
                            <constraint firstItem="eAU-Ak-Bw8" firstAttribute="top" secondItem="qbe-sP-tvu" secondAttribute="top" id="Fgx-fG-26W"/>
                            <constraint firstItem="M5e-W1-UHL" firstAttribute="leading" secondItem="SEg-u5-ugK" secondAttribute="leading" id="Gba-g5-jyN"/>
                            <constraint firstItem="4BN-6A-cvq" firstAttribute="leading" relation="lessThanOrEqual" secondItem="M5e-W1-UHL" secondAttribute="trailing" priority="999" constant="172" id="Jbu-UN-Sob"/>
                            <constraint firstItem="eAU-Ak-Bw8" firstAttribute="leading" secondItem="5hz-US-SpH" secondAttribute="leadingMargin" id="Jbv-T5-Ynb"/>
                            <constraint firstItem="eAU-Ak-Bw8" firstAttribute="leading" secondItem="M5e-W1-UHL" secondAttribute="leading" id="Orh-In-zwM"/>
                            <constraint firstItem="SEg-u5-ugK" firstAttribute="top" secondItem="M5e-W1-UHL" secondAttribute="bottom" constant="18" id="QPX-X8-2J0"/>
                            <constraint firstItem="M5e-W1-UHL" firstAttribute="baseline" secondItem="4BN-6A-cvq" secondAttribute="baseline" id="SS9-3b-yGL"/>
                            <constraint firstItem="SEg-u5-ugK" firstAttribute="leading" secondItem="Qyj-rF-x2Z" secondAttribute="leading" id="cam-vi-91y"/>
                            <constraint firstItem="Tni-yu-bCd" firstAttribute="top" secondItem="S2N-Hz-heF" secondAttribute="bottom" constant="24" id="ecC-Cz-AGv"/>
                            <constraint firstItem="S2N-Hz-heF" firstAttribute="trailing" secondItem="Tni-yu-bCd" secondAttribute="trailing" id="htw-L2-DMz"/>
                            <constraint firstItem="S2N-Hz-heF" firstAttribute="leading" secondItem="Tni-yu-bCd" secondAttribute="leading" id="x2h-bB-Dx3"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="attachButton" destination="Qyj-rF-x2Z" id="v9s-vu-cHF"/>
                        <outlet property="headerLabel" destination="eAU-Ak-Bw8" id="0qS-Xp-gdL"/>
                        <outlet property="progressBar" destination="Fue-xl-Qqu" id="kYw-Zc-XQX"/>
                        <outlet property="progressBarWidthConstraint" destination="S04-lK-peR" id="0nn-Te-jgA"/>
                        <outlet property="progressView" destination="S2N-Hz-heF" id="iAu-SV-ihv"/>
                        <outlet property="screenMovieButton" destination="4BN-6A-cvq" id="KSC-e6-5Zo"/>
                        <outlet property="screenshotButton" destination="M5e-W1-UHL" id="hox-Dd-2dr"/>
                        <outlet property="submitButton" destination="Tni-yu-bCd" id="v6s-OH-YOX"/>
                        <outlet property="textView" destination="SEg-u5-ugK" id="2RU-du-32b"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Oh8-xz-JK5" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="180" y="-561"/>
        </scene>
        <!--TryingToFlexViewController-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="TryingToFlexViewController" title="TryingToFlexViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="BYZ-38-t0r" customClass="TryingToFlexViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Pz5-zM-aW0">
                                <rect key="frame" x="186.5" y="244" width="41" height="40"/>
                                <state key="normal" image="x"/>
                                <connections>
                                    <action selector="didTapButton:" destination="BYZ-38-t0r" eventType="touchUpInside" id="W90-w0-4qW"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Trying To Flex?" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bqj-TM-4zu">
                                <rect key="frame" x="150.5" y="325" width="113" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="hlj-i4-MjU"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="subtitle" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Pa-L7-2hi">
                                <rect key="frame" x="182.5" y="355" width="49" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="LUt-sb-g0U"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="O8N-AS-pPi">
                                <rect key="frame" x="184" y="500" width="46" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="duY-Ch-4mC"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="didTapButton:" destination="BYZ-38-t0r" eventType="touchUpInside" id="HY0-nw-FiN"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="60J-nx-Q2F">
                                <rect key="frame" x="184" y="444" width="46" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="olt-Yt-Ilf"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="didTapButton:" destination="BYZ-38-t0r" eventType="touchUpInside" id="OUX-vd-AFR"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XA9-yb-MvE">
                                <rect key="frame" x="184" y="556" width="46" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="EKd-0Z-Zv7"/>
                                </constraints>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="didTapButton:" destination="BYZ-38-t0r" eventType="touchUpInside" id="r0J-4y-1VJ"/>
                                </connections>
                            </button>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="checkmark" translatesAutoresizingMaskIntoConstraints="NO" id="fhy-EA-5gz">
                                <rect key="frame" x="184" y="505.5" width="15" height="11"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="11" id="K4z-Rs-apU"/>
                                    <constraint firstAttribute="width" constant="15" id="dK3-Yi-7qG"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="checkmark" translatesAutoresizingMaskIntoConstraints="NO" id="LdE-qI-gtb">
                                <rect key="frame" x="184" y="449.5" width="15" height="11"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="11" id="U4n-HA-wgX"/>
                                    <constraint firstAttribute="width" constant="15" id="gCM-xT-mCV"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="checkmark" translatesAutoresizingMaskIntoConstraints="NO" id="OJa-WK-1o5">
                                <rect key="frame" x="184" y="561.5" width="15" height="11"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="11" id="Nmg-Je-FjF"/>
                                    <constraint firstAttribute="width" constant="15" id="eME-AH-NfG"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="OJa-WK-1o5" firstAttribute="centerY" secondItem="XA9-yb-MvE" secondAttribute="centerY" id="4Kd-YP-JeW"/>
                            <constraint firstItem="LdE-qI-gtb" firstAttribute="centerY" secondItem="60J-nx-Q2F" secondAttribute="centerY" id="9fq-ni-fRw"/>
                            <constraint firstItem="XA9-yb-MvE" firstAttribute="top" secondItem="O8N-AS-pPi" secondAttribute="bottom" constant="34" id="Bpu-cH-PZ4"/>
                            <constraint firstItem="1Pa-L7-2hi" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="JXb-uA-HBI"/>
                            <constraint firstItem="XA9-yb-MvE" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="NW5-Ll-cMK"/>
                            <constraint firstItem="O8N-AS-pPi" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="OUR-3N-Phj"/>
                            <constraint firstItem="Bqj-TM-4zu" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="Qnk-fs-oBI"/>
                            <constraint firstItem="Bqj-TM-4zu" firstAttribute="top" secondItem="Pz5-zM-aW0" secondAttribute="bottom" constant="41" id="XVP-La-1m7"/>
                            <constraint firstItem="60J-nx-Q2F" firstAttribute="top" secondItem="1Pa-L7-2hi" secondAttribute="bottom" constant="67" id="ZMB-Ih-Wfy"/>
                            <constraint firstItem="1Pa-L7-2hi" firstAttribute="top" secondItem="Bqj-TM-4zu" secondAttribute="bottom" constant="8" id="cwg-Ah-Q1s"/>
                            <constraint firstItem="O8N-AS-pPi" firstAttribute="top" secondItem="60J-nx-Q2F" secondAttribute="bottom" constant="34" id="eCr-0I-vYZ"/>
                            <constraint firstItem="fhy-EA-5gz" firstAttribute="centerY" secondItem="O8N-AS-pPi" secondAttribute="centerY" id="gnH-uI-bFK"/>
                            <constraint firstItem="Pz5-zM-aW0" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" constant="200" id="hfP-ay-qTN"/>
                            <constraint firstItem="Pz5-zM-aW0" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="oH5-QK-ue7"/>
                            <constraint firstItem="LdE-qI-gtb" firstAttribute="leading" secondItem="60J-nx-Q2F" secondAttribute="leading" id="r9p-Sd-eEL"/>
                            <constraint firstItem="fhy-EA-5gz" firstAttribute="leading" secondItem="O8N-AS-pPi" secondAttribute="leading" id="rQT-96-Xdg"/>
                            <constraint firstItem="OJa-WK-1o5" firstAttribute="leading" secondItem="XA9-yb-MvE" secondAttribute="leading" id="wV4-be-cnS"/>
                            <constraint firstItem="60J-nx-Q2F" firstAttribute="centerX" secondItem="8bC-Xf-vdC" secondAttribute="centerX" id="wXu-3W-BMq"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="cameraAccessButton" destination="O8N-AS-pPi" id="IbV-VR-N2V"/>
                        <outlet property="cameraCheckImageView" destination="fhy-EA-5gz" id="bHU-5n-Yyh"/>
                        <outlet property="cancelButton" destination="Pz5-zM-aW0" id="lN7-4a-zO6"/>
                        <outlet property="cancelButtonTopConstraint" destination="hfP-ay-qTN" id="kaS-sB-uNo"/>
                        <outlet property="libraryCheckImageView" destination="LdE-qI-gtb" id="mTK-7L-7Fq"/>
                        <outlet property="photosAccessButton" destination="60J-nx-Q2F" id="vrt-49-isM"/>
                        <outlet property="pushCheckImageView" destination="OJa-WK-1o5" id="xtt-Ha-mZp"/>
                        <outlet property="pushNotificationsButton" destination="XA9-yb-MvE" id="Ar0-xu-ZsL"/>
                        <outlet property="subTryingLabel" destination="1Pa-L7-2hi" id="zpq-bh-I2b"/>
                        <outlet property="tryingLabel" destination="Bqj-TM-4zu" id="Mzh-Q8-6tN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.68115942028987" y="138.61607142857142"/>
        </scene>
        <!--Camera View Controller-->
        <scene sceneID="ntP-2e-Bao">
            <objects>
                <viewController storyboardIdentifier="CameraViewController" extendedLayoutIncludesOpaqueBars="YES" useStoryboardIdentifierAsRestorationIdentifier="YES" id="Bi9-YV-x7C" customClass="CameraViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="7h2-88-9Q0">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="J5V-BI-Vv5">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="803"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <imageView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FKi-VO-d79">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wek-gm-XWM">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleAspectFill" translatesAutoresizingMaskIntoConstraints="NO" id="gob-FN-XA7">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="A4u-O3-9I7">
                                <rect key="frame" x="0.0" y="578" width="414" height="222"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="222" id="vdQ-A8-R6j"/>
                                </constraints>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="nxS-Pj-WhL">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                            </collectionView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="h1j-wt-1dE">
                                <rect key="frame" x="0.0" y="801" width="414" height="95"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3qh-lU-f0d">
                                        <rect key="frame" x="32" y="0.0" width="124" height="80"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="80" id="YDC-eZ-Z7a"/>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="124" id="vm6-Ii-9LR"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <connections>
                                            <action selector="didTapButton:" destination="Bi9-YV-x7C" eventType="touchUpInside" id="IDG-QA-Fk3"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="evw-SR-0Xd">
                                        <rect key="frame" x="258" y="0.0" width="124" height="80"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="80" id="1zX-TH-hBK"/>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="124" id="Hyt-4f-2dT"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <connections>
                                            <action selector="didTapButton:" destination="Bi9-YV-x7C" eventType="touchUpInside" id="m0V-H6-weB"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="3qh-lU-f0d" firstAttribute="top" secondItem="h1j-wt-1dE" secondAttribute="top" id="3KF-2Z-iVk"/>
                                    <constraint firstItem="evw-SR-0Xd" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="3qh-lU-f0d" secondAttribute="trailing" constant="8" symbolic="YES" id="Tp2-gO-EW7"/>
                                    <constraint firstAttribute="trailing" secondItem="evw-SR-0Xd" secondAttribute="trailing" constant="32" id="Zcw-1I-QzM"/>
                                    <constraint firstItem="3qh-lU-f0d" firstAttribute="leading" secondItem="h1j-wt-1dE" secondAttribute="leading" constant="32" id="e91-NY-n7m"/>
                                    <constraint firstAttribute="height" constant="95" id="oyd-Ny-YQt"/>
                                    <constraint firstItem="evw-SR-0Xd" firstAttribute="top" secondItem="h1j-wt-1dE" secondAttribute="top" id="v9e-y3-0UF"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZcR-E5-H4d">
                                <rect key="frame" x="16" y="530" width="40" height="40"/>
                                <subviews>
                                    <visualEffectView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="YbQ-Q6-D8u">
                                        <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="at9-i7-H53">
                                            <rect key="frame" x="0.0" y="0.0" width="40" height="40"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        </view>
                                        <blurEffect style="regular"/>
                                    </visualEffectView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="JF6-ty-PQ9">
                                        <rect key="frame" x="8" y="8" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="UXt-xq-Tcb"/>
                                            <constraint firstAttribute="height" constant="24" id="dlf-XL-hdT"/>
                                        </constraints>
                                        <state key="normal" image="trash"/>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="nNf-pI-bJk"/>
                                    <constraint firstAttribute="width" constant="40" id="rLl-dq-NSQ"/>
                                    <constraint firstItem="JF6-ty-PQ9" firstAttribute="centerX" secondItem="ZcR-E5-H4d" secondAttribute="centerX" id="v5r-mg-De2"/>
                                    <constraint firstItem="JF6-ty-PQ9" firstAttribute="centerY" secondItem="ZcR-E5-H4d" secondAttribute="centerY" id="w2h-Wa-gLM"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Wpn-qW-ggO" customClass="ImageEnhancementControlsView" customModule="GameFlex" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="803" width="414" height="93"/>
                                <subviews>
                                    <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tk3-qc-lxI">
                                        <rect key="frame" x="0.0" y="0.0" width="103.5" height="93"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Text" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nYR-tO-Dfp">
                                                <rect key="frame" x="40" y="59" width="24" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="14" id="kTf-io-Cul"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <color key="highlightedColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="O7S-dx-cMi">
                                                <rect key="frame" x="32" y="15" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="1VD-QY-ids"/>
                                                    <constraint firstAttribute="height" constant="40" id="Uen-Cn-Iaa"/>
                                                </constraints>
                                                <state key="normal" image="text"/>
                                                <connections>
                                                    <action selector="didTapButtonWithSender:" destination="Wpn-qW-ggO" eventType="touchUpInside" id="AjD-QC-NJH"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="nYR-tO-Dfp" firstAttribute="top" secondItem="O7S-dx-cMi" secondAttribute="bottom" constant="4" id="2sF-g5-xF0"/>
                                            <constraint firstAttribute="bottom" secondItem="nYR-tO-Dfp" secondAttribute="bottom" constant="20" id="9xm-hn-ypY"/>
                                            <constraint firstItem="O7S-dx-cMi" firstAttribute="centerX" secondItem="tk3-qc-lxI" secondAttribute="centerX" id="CPf-Iz-6nW"/>
                                            <constraint firstItem="nYR-tO-Dfp" firstAttribute="centerX" secondItem="O7S-dx-cMi" secondAttribute="centerX" id="sMf-3B-vA4"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pl5-bZ-IUW">
                                        <rect key="frame" x="103.5" y="0.0" width="103.5" height="93"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Flare" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MlP-Om-AiI">
                                                <rect key="frame" x="38" y="59" width="28" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="14" id="xDH-cJ-Z3P"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZoN-JY-lDF">
                                                <rect key="frame" x="32" y="15" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="LQH-uK-y1E"/>
                                                    <constraint firstAttribute="width" constant="40" id="hu7-iE-PaH"/>
                                                </constraints>
                                                <state key="normal" image="flare"/>
                                                <connections>
                                                    <action selector="didTapButtonWithSender:" destination="Wpn-qW-ggO" eventType="touchUpInside" id="DhQ-rI-QbH"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="MlP-Om-AiI" secondAttribute="bottom" constant="20" id="O4F-Ek-oRK"/>
                                            <constraint firstItem="MlP-Om-AiI" firstAttribute="centerX" secondItem="ZoN-JY-lDF" secondAttribute="centerX" id="Y54-hR-Y4a"/>
                                            <constraint firstItem="MlP-Om-AiI" firstAttribute="top" secondItem="ZoN-JY-lDF" secondAttribute="bottom" constant="4" id="kHL-gS-QUi"/>
                                            <constraint firstItem="ZoN-JY-lDF" firstAttribute="centerX" secondItem="pl5-bZ-IUW" secondAttribute="centerX" id="oOO-wS-ZMH"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DC6-Av-Wqa">
                                        <rect key="frame" x="207" y="0.0" width="103.5" height="93"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Filter" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Lwb-eN-RIq">
                                                <rect key="frame" x="37.5" y="59" width="29" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="14" id="fTC-3J-dau"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Rn1-gN-Pza">
                                                <rect key="frame" x="32" y="15" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="EwG-nq-Fkg"/>
                                                    <constraint firstAttribute="height" constant="40" id="V7h-cz-VFH"/>
                                                </constraints>
                                                <state key="normal" image="filter"/>
                                                <connections>
                                                    <action selector="didTapButtonWithSender:" destination="Wpn-qW-ggO" eventType="touchUpInside" id="qM9-a2-6lk"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Lwb-eN-RIq" secondAttribute="bottom" constant="20" id="IKO-B5-UCB"/>
                                            <constraint firstItem="Lwb-eN-RIq" firstAttribute="top" secondItem="Rn1-gN-Pza" secondAttribute="bottom" constant="4" id="cdO-sh-hgZ"/>
                                            <constraint firstItem="Lwb-eN-RIq" firstAttribute="centerX" secondItem="DC6-Av-Wqa" secondAttribute="centerX" id="kqd-5k-whJ"/>
                                            <constraint firstItem="Lwb-eN-RIq" firstAttribute="centerX" secondItem="Rn1-gN-Pza" secondAttribute="centerX" id="uBo-IA-KHI"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SQl-oB-FVv">
                                        <rect key="frame" x="310.5" y="0.0" width="103.5" height="93"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bI7-B2-Zlf">
                                                <rect key="frame" x="32" y="15" width="40" height="40"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="40" id="H5k-Fh-0yw"/>
                                                    <constraint firstAttribute="height" constant="40" id="fui-Ho-cQs"/>
                                                </constraints>
                                                <state key="normal" image="hand.draw.fill" catalog="system"/>
                                                <connections>
                                                    <action selector="didTapButtonWithSender:" destination="Wpn-qW-ggO" eventType="touchUpInside" id="6Xy-DZ-9uz"/>
                                                </connections>
                                            </button>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Draw" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P3r-KE-70P">
                                                <rect key="frame" x="37.5" y="59" width="29" height="14"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="14" id="627-Ic-4Pg"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="P3r-KE-70P" secondAttribute="bottom" constant="20" id="PuX-9A-fSz"/>
                                            <constraint firstItem="P3r-KE-70P" firstAttribute="top" secondItem="bI7-B2-Zlf" secondAttribute="bottom" constant="4" id="XfT-Jl-l8x"/>
                                            <constraint firstItem="bI7-B2-Zlf" firstAttribute="centerX" secondItem="SQl-oB-FVv" secondAttribute="centerX" id="fk8-Nv-2Fc"/>
                                            <constraint firstItem="P3r-KE-70P" firstAttribute="centerX" secondItem="bI7-B2-Zlf" secondAttribute="centerX" id="tcf-2C-hnA"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="pl5-bZ-IUW" firstAttribute="width" secondItem="Wpn-qW-ggO" secondAttribute="width" multiplier="0.25" id="BPe-WA-fUJ"/>
                                    <constraint firstItem="SQl-oB-FVv" firstAttribute="width" secondItem="Wpn-qW-ggO" secondAttribute="width" multiplier="0.25" id="LVr-vZ-USV"/>
                                    <constraint firstItem="tk3-qc-lxI" firstAttribute="width" secondItem="Wpn-qW-ggO" secondAttribute="width" multiplier="0.25" id="SBg-v7-mWX"/>
                                    <constraint firstItem="DC6-Av-Wqa" firstAttribute="width" secondItem="Wpn-qW-ggO" secondAttribute="width" multiplier="0.25" id="aLj-gE-d8f"/>
                                    <constraint firstAttribute="height" constant="93" id="efV-C5-zZM"/>
                                </constraints>
                                <connections>
                                    <outlet property="drawButton" destination="bI7-B2-Zlf" id="W31-bB-j7s"/>
                                    <outlet property="drawLabel" destination="P3r-KE-70P" id="l5K-KR-oxK"/>
                                    <outlet property="filterButton" destination="Rn1-gN-Pza" id="ywp-KG-jkC"/>
                                    <outlet property="filterLabel" destination="Lwb-eN-RIq" id="tyo-WS-MKZ"/>
                                    <outlet property="flareButton" destination="ZoN-JY-lDF" id="axm-Nw-9HU"/>
                                    <outlet property="flareLabel" destination="MlP-Om-AiI" id="hig-kM-AxW"/>
                                    <outlet property="textButton" destination="O7S-dx-cMi" id="nt5-pr-jpk"/>
                                    <outlet property="textLabel" destination="nYR-tO-Dfp" id="2oJ-6d-SRm"/>
                                </connections>
                            </stackView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oqQ-2A-aVk">
                                <rect key="frame" x="30" y="480" width="20" height="20"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="ay5-Nv-JlJ"/>
                                    <constraint firstAttribute="width" constant="20" id="l9j-xf-jb3"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sbl-gf-5T3">
                                <rect key="frame" x="35" y="485" width="10" height="10"/>
                                <color key="backgroundColor" red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="10" id="0so-2O-qcB"/>
                                    <constraint firstAttribute="width" secondItem="sbl-gf-5T3" secondAttribute="height" multiplier="1:1" id="hkz-k6-Fkl"/>
                                </constraints>
                            </view>
                            <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="0.5" minValue="0.0" maxValue="1" translatesAutoresizingMaskIntoConstraints="NO" id="DwF-V2-i53">
                                <rect key="frame" x="14" y="333.5" width="254" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="250" id="P12-Xr-UIk"/>
                                    <constraint firstAttribute="height" constant="29" id="axU-yZ-eFK"/>
                                </constraints>
                            </slider>
                            <view alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QlC-QI-thJ">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <subviews>
                                    <visualEffectView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sQF-xa-MAg">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                        <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="uF3-Ys-4Es">
                                            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <visualEffectView opaque="NO" contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="kGK-Cs-lBS">
                                                    <rect key="frame" x="0.0" y="0.0" width="174" height="768"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO" id="lTK-r0-eME">
                                                        <rect key="frame" x="0.0" y="0.0" width="174" height="768"/>
                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    </view>
                                                    <vibrancyEffect>
                                                        <blurEffect style="regular"/>
                                                    </vibrancyEffect>
                                                </visualEffectView>
                                            </subviews>
                                        </view>
                                        <blurEffect style="regular"/>
                                    </visualEffectView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yUw-Ic-RfO">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="400"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="sQF-xa-MAg" secondAttribute="bottom" id="3Ek-g0-RK8"/>
                                    <constraint firstItem="yUw-Ic-RfO" firstAttribute="top" secondItem="QlC-QI-thJ" secondAttribute="top" id="7oD-V1-lZw"/>
                                    <constraint firstAttribute="trailing" secondItem="sQF-xa-MAg" secondAttribute="trailing" id="C2c-jr-aN9"/>
                                    <constraint firstItem="sQF-xa-MAg" firstAttribute="top" secondItem="QlC-QI-thJ" secondAttribute="top" id="KBy-Ou-ScD"/>
                                    <constraint firstAttribute="trailing" secondItem="yUw-Ic-RfO" secondAttribute="trailing" id="KoH-fq-Me0"/>
                                    <constraint firstAttribute="bottom" secondItem="yUw-Ic-RfO" secondAttribute="bottom" id="Vif-Wa-542"/>
                                    <constraint firstItem="sQF-xa-MAg" firstAttribute="leading" secondItem="QlC-QI-thJ" secondAttribute="leading" id="gGp-DT-iUO"/>
                                    <constraint firstItem="yUw-Ic-RfO" firstAttribute="leading" secondItem="QlC-QI-thJ" secondAttribute="leading" id="w9U-1S-h9b"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="DHa-a4-oWY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="sbl-gf-5T3" firstAttribute="centerX" secondItem="oqQ-2A-aVk" secondAttribute="centerX" id="0hV-bX-2Qx"/>
                            <constraint firstAttribute="bottom" secondItem="FKi-VO-d79" secondAttribute="bottom" id="8ux-SY-MHR"/>
                            <constraint firstItem="wek-gm-XWM" firstAttribute="top" secondItem="FKi-VO-d79" secondAttribute="top" id="ADr-6Q-7Iv"/>
                            <constraint firstItem="FKi-VO-d79" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="AVx-Ld-Bz8"/>
                            <constraint firstItem="oqQ-2A-aVk" firstAttribute="top" secondItem="DwF-V2-i53" secondAttribute="bottom" constant="117.5" id="Fvw-tc-5gL"/>
                            <constraint firstItem="wek-gm-XWM" firstAttribute="leading" secondItem="FKi-VO-d79" secondAttribute="leading" id="IAM-cE-YCP"/>
                            <constraint firstItem="wek-gm-XWM" firstAttribute="bottom" secondItem="FKi-VO-d79" secondAttribute="bottom" id="Ip7-Xn-Zvq"/>
                            <constraint firstItem="oqQ-2A-aVk" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" constant="30" id="JTg-Fb-nTU"/>
                            <constraint firstAttribute="trailing" secondItem="Wpn-qW-ggO" secondAttribute="trailing" id="LCR-NW-kjq"/>
                            <constraint firstItem="J5V-BI-Vv5" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="ME1-bY-2xn"/>
                            <constraint firstItem="ZcR-E5-H4d" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" constant="16" id="MHs-MZ-QUS"/>
                            <constraint firstAttribute="bottom" secondItem="h1j-wt-1dE" secondAttribute="bottom" id="OSJ-CC-ssC"/>
                            <constraint firstItem="A4u-O3-9I7" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="Wri-v3-wiL"/>
                            <constraint firstItem="A4u-O3-9I7" firstAttribute="trailing" secondItem="7h2-88-9Q0" secondAttribute="trailing" id="Xpr-rD-XeF"/>
                            <constraint firstItem="gob-FN-XA7" firstAttribute="trailing" secondItem="7h2-88-9Q0" secondAttribute="trailing" id="YaN-tS-2bo"/>
                            <constraint firstItem="QlC-QI-thJ" firstAttribute="top" secondItem="7h2-88-9Q0" secondAttribute="top" id="ZD3-bT-a5g"/>
                            <constraint firstItem="DwF-V2-i53" firstAttribute="centerY" secondItem="7h2-88-9Q0" secondAttribute="centerY" constant="-100" id="a3U-Ps-cbZ"/>
                            <constraint firstItem="A4u-O3-9I7" firstAttribute="top" secondItem="ZcR-E5-H4d" secondAttribute="bottom" constant="8" id="bmT-3S-dPh"/>
                            <constraint firstItem="A4u-O3-9I7" firstAttribute="bottom" secondItem="7h2-88-9Q0" secondAttribute="bottom" constant="-96" id="cmJ-WT-60S"/>
                            <constraint firstItem="DwF-V2-i53" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" constant="16" id="dee-sz-iUI"/>
                            <constraint firstAttribute="bottom" secondItem="QlC-QI-thJ" secondAttribute="bottom" id="eRq-ci-M50"/>
                            <constraint firstItem="h1j-wt-1dE" firstAttribute="trailing" secondItem="7h2-88-9Q0" secondAttribute="trailing" id="fbz-hl-e9d"/>
                            <constraint firstAttribute="trailing" secondItem="QlC-QI-thJ" secondAttribute="trailing" id="gl5-zn-W3Y"/>
                            <constraint firstItem="J5V-BI-Vv5" firstAttribute="trailing" secondItem="7h2-88-9Q0" secondAttribute="trailing" id="hSJ-Kc-tTo"/>
                            <constraint firstAttribute="bottom" secondItem="J5V-BI-Vv5" secondAttribute="bottom" constant="93" id="jjw-lc-TT2"/>
                            <constraint firstItem="gob-FN-XA7" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="lSK-vo-9Ff"/>
                            <constraint firstItem="QlC-QI-thJ" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="n2o-fj-j2o"/>
                            <constraint firstItem="J5V-BI-Vv5" firstAttribute="top" secondItem="7h2-88-9Q0" secondAttribute="top" id="nBX-rU-dhW"/>
                            <constraint firstItem="Wpn-qW-ggO" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="pKf-0O-X9B"/>
                            <constraint firstAttribute="bottom" secondItem="Wpn-qW-ggO" secondAttribute="bottom" id="pVM-UU-iBd"/>
                            <constraint firstAttribute="trailing" secondItem="Wpn-qW-ggO" secondAttribute="trailing" id="pkU-Tl-2cE"/>
                            <constraint firstItem="gob-FN-XA7" firstAttribute="top" secondItem="7h2-88-9Q0" secondAttribute="top" id="qoA-lt-qYb"/>
                            <constraint firstItem="FKi-VO-d79" firstAttribute="trailing" secondItem="7h2-88-9Q0" secondAttribute="trailing" id="rJz-eO-mub"/>
                            <constraint firstItem="h1j-wt-1dE" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="rzE-wf-5r2"/>
                            <constraint firstAttribute="bottom" secondItem="gob-FN-XA7" secondAttribute="bottom" id="uDj-1x-hYg"/>
                            <constraint firstItem="wek-gm-XWM" firstAttribute="trailing" secondItem="FKi-VO-d79" secondAttribute="trailing" id="uHu-0z-XcZ"/>
                            <constraint firstItem="FKi-VO-d79" firstAttribute="top" secondItem="7h2-88-9Q0" secondAttribute="top" id="ySS-Uu-xuk"/>
                            <constraint firstItem="Wpn-qW-ggO" firstAttribute="leading" secondItem="7h2-88-9Q0" secondAttribute="leading" id="ygg-LB-faY"/>
                            <constraint firstItem="sbl-gf-5T3" firstAttribute="centerY" secondItem="oqQ-2A-aVk" secondAttribute="centerY" id="zgh-ij-Mf2"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="bottomButtonView" destination="h1j-wt-1dE" id="xvr-cP-olX"/>
                        <outlet property="collectionView" destination="A4u-O3-9I7" id="0p9-Ys-XSq"/>
                        <outlet property="collectionViewBottomConstraint" destination="cmJ-WT-60S" id="oW3-Ee-wpF"/>
                        <outlet property="collectionViewHeight" destination="vdQ-A8-R6j" id="AVg-iY-vfp"/>
                        <outlet property="flexBackgroundView" destination="J5V-BI-Vv5" id="nnV-sg-nRJ"/>
                        <outlet property="imageEnhancementControlsView" destination="Wpn-qW-ggO" id="d4v-T7-4fk"/>
                        <outlet property="libraryButton" destination="evw-SR-0Xd" id="t1j-NE-3XR"/>
                        <outlet property="lineWidthCenterBox" destination="oqQ-2A-aVk" id="83P-5O-wld"/>
                        <outlet property="lineWidthView" destination="sbl-gf-5T3" id="xCB-cV-SUC"/>
                        <outlet property="lineWidthViewConstraint" destination="0so-2O-qcB" id="VAf-Qp-s73"/>
                        <outlet property="photoButton" destination="3qh-lU-f0d" id="N7q-RZ-fhT"/>
                        <outlet property="photoPreviewImageView" destination="FKi-VO-d79" id="r2f-YP-d4C"/>
                        <outlet property="previewView" destination="gob-FN-XA7" id="KMX-up-i2A"/>
                        <outlet property="stickerBaseView" destination="wek-gm-XWM" id="09e-kw-2gF"/>
                        <outlet property="timerLabel" destination="yUw-Ic-RfO" id="Jft-nz-pBw"/>
                        <outlet property="timerView" destination="QlC-QI-thJ" id="cA0-1f-Luz"/>
                        <outlet property="trashCan" destination="JF6-ty-PQ9" id="RRv-bi-5sy"/>
                        <outlet property="trashView" destination="ZcR-E5-H4d" id="c8I-nh-Vk8"/>
                        <outlet property="verticalSlider" destination="DwF-V2-i53" id="Jkh-Eh-PnI"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="rhK-My-sI4" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="789.85507246376812" y="168.75"/>
        </scene>
        <!--Finalize View Controller-->
        <scene sceneID="4N1-ee-tTS">
            <objects>
                <viewController storyboardIdentifier="FinalizeViewController" id="TUu-hP-ncF" customClass="FinalizeViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="ckB-zL-oes">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="XCx-Cm-La7">
                                <rect key="frame" x="0.0" y="44" width="414" height="852"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="G8D-0l-PXR"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="XCx-Cm-La7" secondAttribute="trailing" id="7Xm-rq-4lC"/>
                            <constraint firstItem="XCx-Cm-La7" firstAttribute="leading" secondItem="ckB-zL-oes" secondAttribute="leading" id="D8M-vL-BLc"/>
                            <constraint firstAttribute="bottom" secondItem="XCx-Cm-La7" secondAttribute="bottom" id="Wml-Ct-cBE"/>
                            <constraint firstItem="XCx-Cm-La7" firstAttribute="top" secondItem="G8D-0l-PXR" secondAttribute="top" id="meA-GC-4j5"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="XCx-Cm-La7" id="MsB-BH-FWx"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="pan-XV-5OL" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1513.0434782608697" y="162.72321428571428"/>
        </scene>
        <!--Invite View Controller-->
        <scene sceneID="G3w-1N-wgw">
            <objects>
                <viewController storyboardIdentifier="InviteViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="8rd-9k-EGk" customClass="InviteViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="Zyx-ki-Mpa">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" allowsMultipleSelection="YES" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="6ph-G7-7If">
                                <rect key="frame" x="0.0" y="44" width="414" height="852"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="gcn-44-9Jz"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="6ph-G7-7If" secondAttribute="trailing" id="Ouu-ZZ-aqH"/>
                            <constraint firstItem="6ph-G7-7If" firstAttribute="leading" secondItem="Zyx-ki-Mpa" secondAttribute="leading" id="lra-qC-lP2"/>
                            <constraint firstAttribute="bottom" secondItem="6ph-G7-7If" secondAttribute="bottom" id="qFh-7o-hiD"/>
                            <constraint firstItem="6ph-G7-7If" firstAttribute="top" secondItem="gcn-44-9Jz" secondAttribute="top" id="vb8-8X-Yzm"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="6ph-G7-7If" id="l2l-CB-Uwa"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="6vg-Ee-fZk" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2174" y="169"/>
        </scene>
        <!--ProfileViewController-->
        <scene sceneID="f4C-xj-oX5">
            <objects>
                <viewController storyboardIdentifier="ProfileViewController" title="ProfileViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="vJl-ZK-cun" customClass="ProfileViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="ElT-h7-e8U">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="gjk-vH-ft1">
                                <rect key="frame" x="0.0" y="88" width="414" height="808"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="kOV-uK-ogT"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="gjk-vH-ft1" firstAttribute="top" secondItem="kOV-uK-ogT" secondAttribute="top" id="OWS-rh-gDU"/>
                            <constraint firstItem="gjk-vH-ft1" firstAttribute="leading" secondItem="kOV-uK-ogT" secondAttribute="leading" id="dPM-mg-3pq"/>
                            <constraint firstAttribute="bottom" secondItem="gjk-vH-ft1" secondAttribute="bottom" id="fhd-Wf-nW9"/>
                            <constraint firstItem="gjk-vH-ft1" firstAttribute="trailing" secondItem="kOV-uK-ogT" secondAttribute="trailing" id="g0Z-kw-ZXB"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="rIm-AX-jon"/>
                    <connections>
                        <outlet property="tableView" destination="gjk-vH-ft1" id="udI-k8-4z5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="E9F-zv-0vh" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-752.17391304347836" y="-1260.2678571428571"/>
        </scene>
        <!--Login View Controller-->
        <scene sceneID="r0j-Ht-r4s">
            <objects>
                <viewController storyboardIdentifier="LoginViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="2CB-j1-gMu" customClass="LoginViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="BY6-0H-Gbp">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" dataMode="prototypes" style="grouped" separatorStyle="none" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="18" sectionFooterHeight="18" translatesAutoresizingMaskIntoConstraints="NO" id="MYx-Va-Z5q">
                                <rect key="frame" x="0.0" y="100.5" width="414" height="599.5"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PoS-qm-vWO">
                                <rect key="frame" x="87" y="44" width="240" height="64.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1qE-Kc-zED" customClass="LoginHeaderView" customModule="GameFlex" customModuleProvider="target">
                                        <rect key="frame" x="43" y="10" width="154" height="24"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="MNW-rG-kTF"/>
                                            <constraint firstAttribute="width" constant="154" id="lPU-H0-MYb"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hN5-lh-JWg">
                                        <rect key="frame" x="8" y="44" width="224" height="20.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="hN5-lh-JWg" secondAttribute="trailing" constant="8" id="7PF-Zk-nPt"/>
                                    <constraint firstItem="1qE-Kc-zED" firstAttribute="centerX" secondItem="PoS-qm-vWO" secondAttribute="centerX" id="7Y8-VW-pMR"/>
                                    <constraint firstItem="hN5-lh-JWg" firstAttribute="leading" secondItem="PoS-qm-vWO" secondAttribute="leading" constant="8" id="9Vi-Nx-tjf"/>
                                    <constraint firstAttribute="bottom" secondItem="hN5-lh-JWg" secondAttribute="bottom" id="cPR-qE-Mxx"/>
                                    <constraint firstItem="hN5-lh-JWg" firstAttribute="top" secondItem="1qE-Kc-zED" secondAttribute="bottom" constant="10" id="edq-Hg-OM1"/>
                                    <constraint firstAttribute="width" constant="240" id="lsm-cb-rBG"/>
                                    <constraint firstItem="1qE-Kc-zED" firstAttribute="top" secondItem="PoS-qm-vWO" secondAttribute="top" constant="10" id="seA-Il-cI7"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="quc-fA-ezt">
                                <rect key="frame" x="50" y="836" width="314" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="vvz-ZQ-asM"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="18"/>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="didTapSignInButton" destination="2CB-j1-gMu" eventType="touchUpInside" id="Kth-KI-rlA"/>
                                </connections>
                            </button>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" bounces="NO" scrollEnabled="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" bouncesZoom="NO" editable="NO" textAlignment="center" selectable="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8Wy-4n-ZBU">
                                <rect key="frame" x="36" y="756" width="342" height="78"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <string key="text">Lorem ipsum dolor sit er elit lamet, consectetaur cillium adipisicing pecu, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Nam liber te conscient to factor tum poen legum odioque civiuda.</string>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                <dataDetectorType key="dataDetectorTypes" link="YES"/>
                            </textView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="f7a-k2-5gl">
                                <rect key="frame" x="16" y="708" width="54" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="bfn-9d-xft"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="18"/>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="resetPassword" destination="2CB-j1-gMu" eventType="touchUpInside" id="nda-O7-Eh6"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="sy6-uH-wII"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="MYx-Va-Z5q" firstAttribute="trailing" secondItem="BY6-0H-Gbp" secondAttribute="trailing" id="2wb-ue-a3b"/>
                            <constraint firstItem="f7a-k2-5gl" firstAttribute="leading" secondItem="sy6-uH-wII" secondAttribute="leading" constant="16" id="Cgp-tT-TZv"/>
                            <constraint firstItem="sy6-uH-wII" firstAttribute="trailing" relation="lessThanOrEqual" secondItem="f7a-k2-5gl" secondAttribute="trailing" constant="344" id="JLW-sk-CNa"/>
                            <constraint firstItem="quc-fA-ezt" firstAttribute="top" secondItem="8Wy-4n-ZBU" secondAttribute="bottom" constant="2" id="L1k-ef-GEK"/>
                            <constraint firstItem="PoS-qm-vWO" firstAttribute="top" secondItem="sy6-uH-wII" secondAttribute="top" id="MkQ-3B-Ezk"/>
                            <constraint firstItem="f7a-k2-5gl" firstAttribute="top" secondItem="MYx-Va-Z5q" secondAttribute="bottom" constant="8" symbolic="YES" id="NbF-6J-bkP"/>
                            <constraint firstItem="quc-fA-ezt" firstAttribute="leading" relation="lessThanOrEqual" secondItem="BY6-0H-Gbp" secondAttribute="leading" constant="50" id="bKF-I5-nqB"/>
                            <constraint firstAttribute="trailing" secondItem="quc-fA-ezt" secondAttribute="trailing" constant="50" id="cYd-Db-6YF"/>
                            <constraint firstItem="8Wy-4n-ZBU" firstAttribute="leading" secondItem="BY6-0H-Gbp" secondAttribute="leadingMargin" constant="16" id="kHg-2v-GFB"/>
                            <constraint firstItem="MYx-Va-Z5q" firstAttribute="leading" secondItem="BY6-0H-Gbp" secondAttribute="leading" id="pBY-dD-ECa"/>
                            <constraint firstAttribute="bottom" secondItem="MYx-Va-Z5q" secondAttribute="bottom" constant="196" id="pcx-r5-43G"/>
                            <constraint firstItem="MYx-Va-Z5q" firstAttribute="top" secondItem="PoS-qm-vWO" secondAttribute="bottom" constant="-8" id="tiG-fx-ZY9"/>
                            <constraint firstAttribute="trailingMargin" secondItem="8Wy-4n-ZBU" secondAttribute="trailing" constant="16" id="u3j-se-hVe"/>
                            <constraint firstItem="8Wy-4n-ZBU" firstAttribute="top" secondItem="f7a-k2-5gl" secondAttribute="bottom" constant="8" id="ywl-ES-ynO"/>
                            <constraint firstItem="PoS-qm-vWO" firstAttribute="centerX" secondItem="BY6-0H-Gbp" secondAttribute="centerX" id="zKc-0t-ZKI"/>
                            <constraint firstAttribute="bottom" secondItem="quc-fA-ezt" secondAttribute="bottom" constant="20" id="zwR-bU-aEE"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="headerView" destination="PoS-qm-vWO" id="mjC-Ls-aQC"/>
                        <outlet property="legalTextView" destination="8Wy-4n-ZBU" id="ceV-tr-cxU"/>
                        <outlet property="logoLabel" destination="hN5-lh-JWg" id="dxr-Ts-97Y"/>
                        <outlet property="logoView" destination="1qE-Kc-zED" id="Ipk-iG-wiZ"/>
                        <outlet property="passwordResetButton" destination="f7a-k2-5gl" id="Pmc-kf-ed1"/>
                        <outlet property="signInButton" destination="quc-fA-ezt" id="EZF-Yb-5F1"/>
                        <outlet property="tableView" destination="MYx-Va-Z5q" id="mgS-1X-6rz"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="esX-ZX-Q4N" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="779.71014492753625" y="798.88392857142856"/>
        </scene>
        <!--Temp2ViewController-->
        <scene sceneID="onZ-1l-1lM">
            <objects>
                <viewController storyboardIdentifier="Temp2ViewController" title="Temp2ViewController" id="yDX-YP-Yy7" customClass="Temp2ViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="43E-Yf-68s">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="WpY-qA-XVX">
                                <rect key="frame" x="20" y="165" width="110" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="1GR-La-p7T"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="110" id="f4U-4Z-L42"/>
                                </constraints>
                                <state key="normal" title="Login (temp)"/>
                                <connections>
                                    <action selector="didTapButton:" destination="yDX-YP-Yy7" eventType="touchUpInside" id="jgK-Hk-HGx"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oXx-a3-pYa">
                                <rect key="frame" x="281" y="165" width="113" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="4SO-tM-Tjt"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="113" id="Krm-tN-gts"/>
                                </constraints>
                                <state key="normal" title="Profile (temp)"/>
                                <connections>
                                    <action selector="didTapButton:" destination="yDX-YP-Yy7" eventType="touchUpInside" id="d54-jc-ZQC"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7iG-XP-Joj">
                                <rect key="frame" x="223" y="717" width="171" height="55"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="55" id="Ln8-Ie-kP3"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="171" id="TMD-Zw-Oxv"/>
                                </constraints>
                                <state key="normal" title="Reset Profile Details"/>
                                <connections>
                                    <action selector="didTapButton:" destination="yDX-YP-Yy7" eventType="touchUpInside" id="l0G-pg-DjO"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7ji-ld-hcF">
                                <rect key="frame" x="20" y="216" width="71" height="30"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="bhY-ww-EEg"/>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="71" id="fRX-mF-HVu"/>
                                </constraints>
                                <state key="normal" title="Logout"/>
                                <connections>
                                    <action selector="didTapButton:" destination="yDX-YP-Yy7" eventType="touchUpInside" id="9Jt-1G-Ye9"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="L7s-cH-og9">
                                <rect key="frame" x="20" y="652" width="139" height="40"/>
                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="139" id="YSk-BN-Nn5"/>
                                    <constraint firstAttribute="height" constant="40" id="rH4-tx-sYK"/>
                                </constraints>
                                <state key="normal" title="Subscribe to Push">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapButton:" destination="yDX-YP-Yy7" eventType="touchUpInside" id="siO-jW-cEo"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cUm-jE-hg9">
                                <rect key="frame" x="255" y="652" width="139" height="40"/>
                                <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="139" id="HVU-UG-0J4"/>
                                    <constraint firstAttribute="height" constant="40" id="ww9-LJ-zDq"/>
                                </constraints>
                                <state key="normal" title="Send Push">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="didTapButton:" destination="yDX-YP-Yy7" eventType="touchUpInside" id="Ibd-Za-vj5"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="nTA-j9-tlx"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="nTA-j9-tlx" firstAttribute="bottom" secondItem="7iG-XP-Joj" secondAttribute="bottom" constant="90" id="4Hi-R1-RS9"/>
                            <constraint firstItem="7iG-XP-Joj" firstAttribute="top" secondItem="cUm-jE-hg9" secondAttribute="bottom" constant="25" id="5u4-lK-dyq"/>
                            <constraint firstItem="WpY-qA-XVX" firstAttribute="baseline" secondItem="oXx-a3-pYa" secondAttribute="baseline" id="6CX-Cq-6kt"/>
                            <constraint firstItem="nTA-j9-tlx" firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="7ji-ld-hcF" secondAttribute="trailing" symbolic="YES" id="ErI-Wo-jfz"/>
                            <constraint firstItem="cUm-jE-hg9" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="L7s-cH-og9" secondAttribute="trailing" constant="8" symbolic="YES" id="J7r-X2-ThP"/>
                            <constraint firstItem="7ji-ld-hcF" firstAttribute="leading" secondItem="nTA-j9-tlx" secondAttribute="leading" constant="20" id="KK0-VF-Qgn"/>
                            <constraint firstItem="oXx-a3-pYa" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="WpY-qA-XVX" secondAttribute="trailing" constant="8" symbolic="YES" id="Khl-kq-FCh"/>
                            <constraint firstItem="7iG-XP-Joj" firstAttribute="leading" relation="lessThanOrEqual" secondItem="nTA-j9-tlx" secondAttribute="leading" constant="223" id="X0l-KB-T1a"/>
                            <constraint firstItem="nTA-j9-tlx" firstAttribute="trailing" secondItem="7iG-XP-Joj" secondAttribute="trailing" constant="20" id="ZDu-2x-85J"/>
                            <constraint firstItem="7ji-ld-hcF" firstAttribute="top" secondItem="WpY-qA-XVX" secondAttribute="bottom" constant="21" id="dY7-Tp-Ubf"/>
                            <constraint firstItem="L7s-cH-og9" firstAttribute="leading" secondItem="nTA-j9-tlx" secondAttribute="leading" constant="20" id="fw8-iC-a04"/>
                            <constraint firstItem="cUm-jE-hg9" firstAttribute="top" secondItem="L7s-cH-og9" secondAttribute="top" id="n1B-DA-uZl"/>
                            <constraint firstItem="nTA-j9-tlx" firstAttribute="trailing" secondItem="oXx-a3-pYa" secondAttribute="trailing" constant="20" id="oWB-Vn-1jV"/>
                            <constraint firstItem="nTA-j9-tlx" firstAttribute="trailing" secondItem="cUm-jE-hg9" secondAttribute="trailing" constant="20" id="sd3-eT-eIP"/>
                            <constraint firstItem="WpY-qA-XVX" firstAttribute="top" secondItem="nTA-j9-tlx" secondAttribute="top" constant="121" id="vHi-NN-h7a"/>
                            <constraint firstItem="WpY-qA-XVX" firstAttribute="leading" secondItem="nTA-j9-tlx" secondAttribute="leading" constant="20" id="yvf-nt-Hpm"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="GoS-Lr-06k"/>
                    <connections>
                        <outlet property="loginButton" destination="WpY-qA-XVX" id="z3i-RJ-RAc"/>
                        <outlet property="logoutButton" destination="7ji-ld-hcF" id="hp2-zd-riz"/>
                        <outlet property="profileButton" destination="oXx-a3-pYa" id="S4K-51-Z3k"/>
                        <outlet property="resetUserDetailsButton" destination="7iG-XP-Joj" id="egq-oO-mSR"/>
                        <outlet property="sendPush" destination="cUm-jE-hg9" id="aff-GH-ZXJ"/>
                        <outlet property="subscribeToPushButton" destination="L7s-cH-og9" id="D9C-aC-QYs"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5fc-wp-Qds" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-525" y="-561"/>
        </scene>
        <!--Quick Intro View Controller-->
        <scene sceneID="c6i-MN-A51">
            <objects>
                <viewController storyboardIdentifier="QuickIntroViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="0CQ-Y8-cr2" customClass="QuickIntroViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="qx0-9d-sTb">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jyn-IY-6Hi">
                                <rect key="frame" x="50" y="821" width="314" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="Cd8-bg-dbD"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="18"/>
                                <state key="normal" title="Button"/>
                                <connections>
                                    <action selector="skipIntro:" destination="0CQ-Y8-cr2" eventType="touchUpInside" id="w1q-aL-Ug0"/>
                                </connections>
                            </button>
                            <pageControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" numberOfPages="3" translatesAutoresizingMaskIntoConstraints="NO" id="Xge-hd-lde">
                                <rect key="frame" x="145.5" y="785" width="123" height="28"/>
                            </pageControl>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="P9n-QL-LaL">
                                <rect key="frame" x="50" y="69" width="314" height="21"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="21" id="xrz-X3-Ubg"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" image="logoFrontSmall" translatesAutoresizingMaskIntoConstraints="NO" id="Hyv-Dh-sqO">
                                <rect key="frame" x="87" y="100" width="240" height="29"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="240" id="iao-JF-GFu"/>
                                    <constraint firstAttribute="height" constant="29" id="zLc-2k-yYQ"/>
                                </constraints>
                            </imageView>
                            <view alpha="0.64999997615814209" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JkO-91-Hnb">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QWn-ra-zsY">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bigG" translatesAutoresizingMaskIntoConstraints="NO" id="fbN-8a-rDx">
                                        <rect key="frame" x="120" y="100" width="174" height="174"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="174" id="NG0-Za-bib"/>
                                            <constraint firstAttribute="height" constant="174" id="rLj-yW-w8f"/>
                                        </constraints>
                                    </imageView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Game" translatesAutoresizingMaskIntoConstraints="NO" id="YDX-Vw-MGD">
                                        <rect key="frame" x="67" y="304" width="136" height="54"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="54" id="3jV-Aw-Ed7"/>
                                            <constraint firstAttribute="width" constant="136" id="Wf5-yT-YYe"/>
                                        </constraints>
                                    </imageView>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Flex" translatesAutoresizingMaskIntoConstraints="NO" id="y6N-z5-2qr">
                                        <rect key="frame" x="207" y="312" width="114" height="35"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="35" id="5hK-nY-B8m"/>
                                            <constraint firstAttribute="width" constant="114" id="pFL-SW-cIm"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="YDX-Vw-MGD" firstAttribute="centerX" secondItem="QWn-ra-zsY" secondAttribute="centerX" constant="-72" id="Exq-ds-194"/>
                                    <constraint firstItem="fbN-8a-rDx" firstAttribute="centerX" secondItem="QWn-ra-zsY" secondAttribute="centerX" id="NeB-8B-gF9"/>
                                    <constraint firstItem="y6N-z5-2qr" firstAttribute="top" secondItem="fbN-8a-rDx" secondAttribute="bottom" constant="38" id="Rzk-l3-0Rx"/>
                                    <constraint firstItem="y6N-z5-2qr" firstAttribute="centerX" secondItem="QWn-ra-zsY" secondAttribute="centerX" constant="57" id="Vol-Jc-Pi4"/>
                                    <constraint firstItem="fbN-8a-rDx" firstAttribute="top" secondItem="QWn-ra-zsY" secondAttribute="top" constant="100" id="YeW-i5-rRR"/>
                                    <constraint firstItem="YDX-Vw-MGD" firstAttribute="top" secondItem="fbN-8a-rDx" secondAttribute="bottom" constant="30" id="Yii-aZ-wPU"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="rY5-Ks-cWV"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="jyn-IY-6Hi" firstAttribute="leading" secondItem="qx0-9d-sTb" secondAttribute="leading" constant="50" id="4Cv-ih-j8g"/>
                            <constraint firstAttribute="trailing" relation="lessThanOrEqual" secondItem="jyn-IY-6Hi" secondAttribute="trailing" constant="50" id="66e-RX-R4j"/>
                            <constraint firstItem="P9n-QL-LaL" firstAttribute="leading" secondItem="qx0-9d-sTb" secondAttribute="leadingMargin" constant="30" id="7P0-ke-egd"/>
                            <constraint firstAttribute="trailingMargin" relation="lessThanOrEqual" secondItem="P9n-QL-LaL" secondAttribute="trailing" constant="30" id="81l-2a-IbZ"/>
                            <constraint firstItem="JkO-91-Hnb" firstAttribute="leading" secondItem="rY5-Ks-cWV" secondAttribute="leading" id="BjV-oH-twq"/>
                            <constraint firstAttribute="bottom" secondItem="jyn-IY-6Hi" secondAttribute="bottom" constant="35" id="Ght-sU-ccQ"/>
                            <constraint firstItem="Xge-hd-lde" firstAttribute="centerX" secondItem="qx0-9d-sTb" secondAttribute="centerX" id="KNG-Rw-AXV"/>
                            <constraint firstItem="JkO-91-Hnb" firstAttribute="top" secondItem="qx0-9d-sTb" secondAttribute="top" id="LGX-VJ-60N"/>
                            <constraint firstItem="QWn-ra-zsY" firstAttribute="leading" secondItem="rY5-Ks-cWV" secondAttribute="leading" id="Mxk-Uz-Ise"/>
                            <constraint firstAttribute="bottom" secondItem="QWn-ra-zsY" secondAttribute="bottom" id="N0k-OM-kdT"/>
                            <constraint firstItem="QWn-ra-zsY" firstAttribute="top" secondItem="qx0-9d-sTb" secondAttribute="top" id="OTe-l4-ZNf"/>
                            <constraint firstItem="jyn-IY-6Hi" firstAttribute="top" secondItem="Xge-hd-lde" secondAttribute="bottom" constant="8" id="S1X-nu-a4S"/>
                            <constraint firstItem="Hyv-Dh-sqO" firstAttribute="top" secondItem="P9n-QL-LaL" secondAttribute="bottom" constant="10" id="ZWc-0E-I9g"/>
                            <constraint firstItem="JkO-91-Hnb" firstAttribute="trailing" secondItem="rY5-Ks-cWV" secondAttribute="trailing" id="mKU-Fr-Bng"/>
                            <constraint firstItem="P9n-QL-LaL" firstAttribute="top" secondItem="qx0-9d-sTb" secondAttribute="topMargin" constant="25" id="p3y-Ek-fHf"/>
                            <constraint firstAttribute="bottom" secondItem="JkO-91-Hnb" secondAttribute="bottom" id="qUQ-by-KYT"/>
                            <constraint firstItem="Hyv-Dh-sqO" firstAttribute="centerX" secondItem="qx0-9d-sTb" secondAttribute="centerX" id="rAy-yd-9Zb"/>
                            <constraint firstItem="rY5-Ks-cWV" firstAttribute="trailing" secondItem="QWn-ra-zsY" secondAttribute="trailing" id="wV0-ml-nTi"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="bigG" destination="fbN-8a-rDx" id="gii-ag-LbN"/>
                        <outlet property="blackMask" destination="JkO-91-Hnb" id="dM9-QT-ShF"/>
                        <outlet property="clearView" destination="QWn-ra-zsY" id="v4p-oe-KMd"/>
                        <outlet property="flexView" destination="y6N-z5-2qr" id="rxp-V1-5bW"/>
                        <outlet property="gameView" destination="YDX-Vw-MGD" id="yQJ-Fo-vrf"/>
                        <outlet property="headerLabel" destination="P9n-QL-LaL" id="d11-pp-eif"/>
                        <outlet property="headerLogo" destination="Hyv-Dh-sqO" id="HPF-sa-nq9"/>
                        <outlet property="pageControl" destination="Xge-hd-lde" id="wjI-q6-VwA"/>
                        <outlet property="skipButton" destination="jyn-IY-6Hi" id="h2f-cc-3oZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="eKv-gp-afZ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-4975.36231884058" y="-1698.2142857142856"/>
        </scene>
        <!--Page View Controller-->
        <scene sceneID="TME-yX-Pkq">
            <objects>
                <viewController storyboardIdentifier="PageViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="Bfh-5c-zWp" customClass="PageViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="K48-5H-YCZ">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="7" translatesAutoresizingMaskIntoConstraints="NO" id="vje-WS-Nya">
                                <rect key="frame" x="36" y="426" width="342" height="321"/>
                                <fontDescription key="fontDescription" type="system" pointSize="21"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xQQ-Mq-xjt">
                                <rect key="frame" x="36" y="398" width="342" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="lwd-22-F0W"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="56D-87-gZv">
                                <rect key="frame" x="82" y="144" width="250" height="250"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="250" id="lBR-rY-wC5"/>
                                </constraints>
                            </imageView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="D1Y-2W-TKO"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="xQQ-Mq-xjt" firstAttribute="top" secondItem="56D-87-gZv" secondAttribute="bottom" constant="4" id="4XB-6g-S0r"/>
                            <constraint firstItem="56D-87-gZv" firstAttribute="top" secondItem="D1Y-2W-TKO" secondAttribute="top" constant="100" id="9mk-Mr-o5O"/>
                            <constraint firstAttribute="trailingMargin" secondItem="xQQ-Mq-xjt" secondAttribute="trailing" constant="16" id="Try-p3-N75"/>
                            <constraint firstItem="vje-WS-Nya" firstAttribute="top" secondItem="xQQ-Mq-xjt" secondAttribute="bottom" constant="6" id="W0c-Zb-s4p"/>
                            <constraint firstItem="56D-87-gZv" firstAttribute="width" secondItem="56D-87-gZv" secondAttribute="height" multiplier="1:1" id="dcp-qG-ERb"/>
                            <constraint firstItem="D1Y-2W-TKO" firstAttribute="bottom" secondItem="vje-WS-Nya" secondAttribute="bottom" constant="115" id="eLq-iE-3QB"/>
                            <constraint firstAttribute="trailingMargin" secondItem="vje-WS-Nya" secondAttribute="trailing" constant="16" id="isq-Hj-Blh"/>
                            <constraint firstItem="xQQ-Mq-xjt" firstAttribute="leading" secondItem="K48-5H-YCZ" secondAttribute="leadingMargin" constant="16" id="sm5-Nd-5v0"/>
                            <constraint firstItem="56D-87-gZv" firstAttribute="centerX" secondItem="K48-5H-YCZ" secondAttribute="centerX" id="vc2-Zt-3dc"/>
                            <constraint firstItem="vje-WS-Nya" firstAttribute="leading" secondItem="K48-5H-YCZ" secondAttribute="leadingMargin" constant="16" id="zWY-B5-fsG"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="imageView" destination="56D-87-gZv" id="Sgw-8Q-OkY"/>
                        <outlet property="messageLabel" destination="vje-WS-Nya" id="DM9-CO-KTA"/>
                        <outlet property="titleLabel" destination="xQQ-Mq-xjt" id="btv-cy-1x0"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="SZr-yj-0Kp" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-4977" y="-1054"/>
        </scene>
        <!--First View Controller-->
        <scene sceneID="623-M5-tp4">
            <objects>
                <viewController storyboardIdentifier="FirstViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="P7c-TN-K0c" customClass="FirstViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="kBg-Qk-wPB">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="tIx-cV-baR"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Irt-fp-8eV" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-6775" y="-1666"/>
        </scene>
        <!--Age View Controller-->
        <scene sceneID="X5j-Fl-d0s">
            <objects>
                <viewController storyboardIdentifier="AgeViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="YYG-0d-77p" customClass="AgeViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="qVk-I5-dh1">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="DNd-6m-nL1">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <collectionViewFlowLayout key="collectionViewLayout" automaticEstimatedItemSize="YES" minimumLineSpacing="10" minimumInteritemSpacing="10" id="8pj-ym-WtK">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                            </collectionView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="UvM-zO-a0D"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="DNd-6m-nL1" firstAttribute="leading" secondItem="qVk-I5-dh1" secondAttribute="leading" id="CpO-Iw-Nfh"/>
                            <constraint firstItem="DNd-6m-nL1" firstAttribute="top" secondItem="qVk-I5-dh1" secondAttribute="top" id="DgP-uA-KNf"/>
                            <constraint firstAttribute="trailing" secondItem="DNd-6m-nL1" secondAttribute="trailing" id="IFi-0H-w3G"/>
                            <constraint firstAttribute="bottom" secondItem="DNd-6m-nL1" secondAttribute="bottom" id="t8b-Zh-f54"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="P4l-b6-RGF"/>
                    <connections>
                        <outlet property="collectionView" destination="DNd-6m-nL1" id="1R6-Hc-XOz"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Tvt-PY-dzu" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-5636" y="-1697"/>
        </scene>
        <!--LandingPageViewController-->
        <scene sceneID="b9q-bi-dTR">
            <objects>
                <tabBarController storyboardIdentifier="LandingPageViewController" title="LandingPageViewController" id="u8y-Qf-ik2" customClass="LandingPageViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <tabBar key="tabBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="WUp-EV-2on" customClass="SafeAreaFixTabBar" customModule="GameFlex" customModuleProvider="target">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="49"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </tabBar>
                    <connections>
                        <segue destination="fY6-Ik-Wj6" kind="relationship" relationship="viewControllers" id="p38-5N-iUl"/>
                        <segue destination="0du-1w-HDA" kind="relationship" relationship="viewControllers" id="hC2-A2-eP1"/>
                        <segue destination="HPG-5J-mnL" kind="relationship" relationship="viewControllers" id="yvE-k8-daE"/>
                        <segue destination="dRc-vL-MAi" kind="relationship" relationship="viewControllers" id="DeH-2L-3nz"/>
                        <segue destination="pmr-CM-kDg" kind="relationship" relationship="viewControllers" id="cRo-Ev-ILY"/>
                    </connections>
                </tabBarController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="5eQ-tF-VQs" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-4048" y="-1617"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="p5g-DY-qWT">
            <objects>
                <navigationController storyboardIdentifier="vc5" id="dRc-vL-MAi" customClass="GFNavigationController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="" image="navProfile" selectedImage="navProfile_filled" id="nUl-w5-6fd"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="axy-RV-2Jq">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="vJl-ZK-cun" kind="relationship" relationship="rootViewController" id="VGS-Sy-PQ0"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vMN-5O-by3" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1517" y="-1260"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="dhA-au-emi">
            <objects>
                <navigationController storyboardIdentifier="vc4" id="HPG-5J-mnL" customClass="GFNavigationController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="" image="navSearch" selectedImage="navSearch_filled" id="j6c-ux-lmF"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="fF6-Bh-R0M">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="wyO-mg-w7x" kind="relationship" relationship="rootViewController" id="dzl-zb-MwV"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="EMc-Fm-IFU" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-5025" y="1515"/>
        </scene>
        <!--SearchViewController-->
        <scene sceneID="Tdm-ma-qJO">
            <objects>
                <viewController storyboardIdentifier="SearchViewController" title="SearchViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="wyO-mg-w7x" customClass="SearchViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="7g2-DV-HXD">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="IzH-at-1VN">
                                <rect key="frame" x="0.0" y="88" width="414" height="808"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <searchBar key="tableFooterView" contentMode="redraw" barStyle="black" text="" placeholder="Search for users &amp; channels..." showsCancelButton="YES" translucent="NO" id="qjv-Mp-Eaw">
                                    <rect key="frame" x="0.0" y="0.0" width="414" height="44"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                    <textInputTraits key="textInputTraits" autocorrectionType="no" spellCheckingType="no" returnKeyType="search"/>
                                </searchBar>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="oOh-hp-2ty"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="IzH-at-1VN" firstAttribute="leading" secondItem="7g2-DV-HXD" secondAttribute="leading" id="4om-nY-E8j"/>
                            <constraint firstAttribute="bottom" secondItem="IzH-at-1VN" secondAttribute="bottom" id="5ZM-kj-odo"/>
                            <constraint firstItem="IzH-at-1VN" firstAttribute="trailing" secondItem="7g2-DV-HXD" secondAttribute="trailing" id="MPu-Ri-E6I"/>
                            <constraint firstItem="IzH-at-1VN" firstAttribute="top" secondItem="oOh-hp-2ty" secondAttribute="top" id="cHr-1H-0jy"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="jPd-kH-pFr"/>
                    <connections>
                        <outlet property="searchBar" destination="qjv-Mp-Eaw" id="lCH-dT-Pht"/>
                        <outlet property="tableView" destination="IzH-at-1VN" id="w3F-FM-1d4"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="0k9-4H-weL" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-4291.304347826087" y="1515.4017857142856"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="yke-CM-Li2">
            <objects>
                <navigationController storyboardIdentifier="vc2" id="pmr-CM-kDg" customClass="GFNavigationController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <tabBarItem key="tabBarItem" title="" image="navAlert" selectedImage="navAlert_filled" id="JBf-W0-R8V"/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="fc0-st-ZMN">
                        <rect key="frame" x="0.0" y="44" width="414" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="rHR-Xs-wVC" kind="relationship" relationship="rootViewController" id="8QA-DQ-wAW"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zcJ-b3-t6z" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-6881" y="1201"/>
        </scene>
        <!--AlertViewController-->
        <scene sceneID="om3-pF-7FM">
            <objects>
                <viewController storyboardIdentifier="AlertViewController" title="AlertViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="rHR-Xs-wVC" customClass="AlertViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="2Rq-cm-V0Z">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" allowsSelection="NO" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="3wy-h9-GrB">
                                <rect key="frame" x="0.0" y="64" width="414" height="832"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="38u-Lq-VQM"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="3wy-h9-GrB" firstAttribute="top" secondItem="2Rq-cm-V0Z" secondAttribute="top" constant="64" id="6Zk-u8-DPf"/>
                            <constraint firstItem="3wy-h9-GrB" firstAttribute="trailing" secondItem="2Rq-cm-V0Z" secondAttribute="trailing" id="Ec5-aw-gMM"/>
                            <constraint firstItem="3wy-h9-GrB" firstAttribute="leading" secondItem="2Rq-cm-V0Z" secondAttribute="leading" id="O0z-Y5-Qvs"/>
                            <constraint firstAttribute="bottom" secondItem="3wy-h9-GrB" secondAttribute="bottom" id="sQ6-xa-zMO"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="pNi-eX-1jV"/>
                    <connections>
                        <outlet property="tableView" destination="3wy-h9-GrB" id="RqY-Da-Tka"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="JPQ-Ru-2ax" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-6146.376811594203" y="1200.6696428571429"/>
        </scene>
        <!--EditProfileViewController-->
        <scene sceneID="yHo-R3-kpH">
            <objects>
                <viewController storyboardIdentifier="EditProfileViewController" title="EditProfileViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="j5I-JC-1my" customClass="EditProfileViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="2UL-5M-YKl">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="aHq-3s-dmu">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="yyK-tQ-uVZ"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="aHq-3s-dmu" firstAttribute="top" secondItem="2UL-5M-YKl" secondAttribute="top" id="EJC-kN-24f"/>
                            <constraint firstItem="aHq-3s-dmu" firstAttribute="trailing" secondItem="yyK-tQ-uVZ" secondAttribute="trailing" id="XIE-DP-JjA"/>
                            <constraint firstAttribute="bottom" secondItem="aHq-3s-dmu" secondAttribute="bottom" id="XdS-cU-jY1"/>
                            <constraint firstItem="aHq-3s-dmu" firstAttribute="leading" secondItem="yyK-tQ-uVZ" secondAttribute="leading" id="nts-Dm-G2r"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="Aqf-Qj-NGE"/>
                    <connections>
                        <outlet property="tableView" destination="aHq-3s-dmu" id="5tM-u9-EAe"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cuj-MT-LMC" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-109" y="-1260"/>
        </scene>
        <!--GetFlexterNameViewController-->
        <scene sceneID="MPK-AD-R9X">
            <objects>
                <viewController storyboardIdentifier="GetFlexterNameViewController" title="GetFlexterNameViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="7mD-4I-2yQ" customClass="GetFlexterNameViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="gcx-hV-asg">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="iqm-6g-SaZ">
                                <rect key="frame" x="0.0" y="44" width="414" height="852"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uFM-U0-oPv"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="iqm-6g-SaZ" firstAttribute="leading" secondItem="uFM-U0-oPv" secondAttribute="leading" id="4hm-xt-mOk"/>
                            <constraint firstAttribute="bottom" secondItem="iqm-6g-SaZ" secondAttribute="bottom" id="L8d-4R-Bi9"/>
                            <constraint firstItem="iqm-6g-SaZ" firstAttribute="top" secondItem="uFM-U0-oPv" secondAttribute="top" id="QtR-gr-8Ea"/>
                            <constraint firstItem="iqm-6g-SaZ" firstAttribute="trailing" secondItem="uFM-U0-oPv" secondAttribute="trailing" id="Zf1-hm-nQp"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="LSV-cx-RhC"/>
                    <connections>
                        <outlet property="tableView" destination="iqm-6g-SaZ" id="cgD-bz-VVb"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="fRG-XP-era" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="511.59420289855075" y="-1260.2678571428571"/>
        </scene>
        <!--CreateChannelViewController-->
        <scene sceneID="gn0-IR-MsS">
            <objects>
                <viewController storyboardIdentifier="CreateChannelViewController" title="CreateChannelViewController" extendedLayoutIncludesOpaqueBars="YES" useStoryboardIdentifierAsRestorationIdentifier="YES" id="aWS-XX-wjs" customClass="CreateChannelViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Rb7-cY-xeo">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="fNy-7j-AR7">
                                <rect key="frame" x="0.0" y="65" width="414" height="831"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7a2-Hw-w1h"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="fNy-7j-AR7" firstAttribute="top" secondItem="Rb7-cY-xeo" secondAttribute="top" constant="65" id="Ca2-J7-Wwp"/>
                            <constraint firstItem="fNy-7j-AR7" firstAttribute="trailing" secondItem="7a2-Hw-w1h" secondAttribute="trailing" id="L5i-TC-hpJ"/>
                            <constraint firstAttribute="bottom" secondItem="fNy-7j-AR7" secondAttribute="bottom" id="Qfh-2K-9Kd"/>
                            <constraint firstItem="fNy-7j-AR7" firstAttribute="leading" secondItem="7a2-Hw-w1h" secondAttribute="leading" id="exP-p0-Otp"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="ThA-x4-u2j"/>
                    <connections>
                        <outlet property="tableView" destination="fNy-7j-AR7" id="WuK-A3-8Ad"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="KtH-cN-pzE" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="512" y="-1896"/>
        </scene>
        <!--Portrait View Controller-->
        <scene sceneID="9Ud-n0-rh0">
            <objects>
                <viewController storyboardIdentifier="PortraitViewController" extendedLayoutIncludesOpaqueBars="YES" useStoryboardIdentifierAsRestorationIdentifier="YES" id="pwB-OQ-RPC" customClass="PortraitViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="1o8-ZJ-Qhz">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xha-cK-25V">
                                <rect key="frame" x="62" y="303" width="290" height="290"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="KVM-OQ-izq">
                                        <rect key="frame" x="0.0" y="0.0" width="290" height="290"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Edq-Aj-fVs">
                                                <rect key="frame" x="0.0" y="0.0" width="290" height="290"/>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Edq-Aj-fVs" secondAttribute="bottom" id="FIb-H4-naW"/>
                                            <constraint firstAttribute="trailing" secondItem="Edq-Aj-fVs" secondAttribute="trailing" id="FKW-dj-4au"/>
                                            <constraint firstItem="Edq-Aj-fVs" firstAttribute="top" secondItem="KVM-OQ-izq" secondAttribute="top" id="cxb-9x-vG3"/>
                                            <constraint firstItem="Edq-Aj-fVs" firstAttribute="leading" secondItem="KVM-OQ-izq" secondAttribute="leading" id="o1P-hS-zJE"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="KVM-OQ-izq" secondAttribute="bottom" id="48C-29-OYb"/>
                                    <constraint firstItem="KVM-OQ-izq" firstAttribute="top" secondItem="xha-cK-25V" secondAttribute="top" id="Vnv-Pd-ics"/>
                                    <constraint firstAttribute="width" secondItem="xha-cK-25V" secondAttribute="height" multiplier="1:1" id="d4p-dw-5EM"/>
                                    <constraint firstAttribute="trailing" secondItem="KVM-OQ-izq" secondAttribute="trailing" id="k8D-KN-aIE"/>
                                    <constraint firstItem="KVM-OQ-izq" firstAttribute="leading" secondItem="xha-cK-25V" secondAttribute="leading" id="tGh-mi-thv"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleAspectFill" translatesAutoresizingMaskIntoConstraints="NO" id="sum-oX-QfP">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view contentMode="scaleToFill" verticalHuggingPriority="1000" insetsLayoutMarginsFromSafeArea="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hOi-d3-9Uf">
                                <rect key="frame" x="0.0" y="801" width="414" height="95"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uSR-Rp-4Eh">
                                        <rect key="frame" x="32" y="0.0" width="124" height="80"/>
                                        <constraints>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="124" id="hwf-hx-UBy"/>
                                            <constraint firstAttribute="height" constant="80" id="okf-Lp-Mi4"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <connections>
                                            <action selector="didTapButton:" destination="pwB-OQ-RPC" eventType="touchUpInside" id="GPN-6n-oJw"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Hvd-Np-Pjb">
                                        <rect key="frame" x="258" y="0.0" width="124" height="80"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="80" id="Zs1-ZS-uY8"/>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="124" id="gFc-pa-6Jq"/>
                                        </constraints>
                                        <state key="normal" title="Button"/>
                                        <connections>
                                            <action selector="didTapButton:" destination="pwB-OQ-RPC" eventType="touchUpInside" id="vtL-JA-Ve7"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="95" id="JmR-oy-tZh"/>
                                    <constraint firstItem="uSR-Rp-4Eh" firstAttribute="top" secondItem="hOi-d3-9Uf" secondAttribute="top" id="KaO-Qf-s2g"/>
                                    <constraint firstItem="Hvd-Np-Pjb" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="uSR-Rp-4Eh" secondAttribute="trailing" constant="8" symbolic="YES" id="THd-ng-a47"/>
                                    <constraint firstItem="uSR-Rp-4Eh" firstAttribute="leading" secondItem="hOi-d3-9Uf" secondAttribute="leading" constant="32" id="V2A-sN-Mfn"/>
                                    <constraint firstItem="Hvd-Np-Pjb" firstAttribute="top" secondItem="hOi-d3-9Uf" secondAttribute="top" id="Yfq-gN-0Ig"/>
                                    <constraint firstAttribute="trailing" secondItem="Hvd-Np-Pjb" secondAttribute="trailing" constant="32" id="v7X-XM-vpr"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zEK-0o-reo">
                                <rect key="frame" x="294" y="822" width="100" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="100" id="3lo-QL-htC"/>
                                    <constraint firstAttribute="height" constant="40" id="XWY-oR-AcS"/>
                                </constraints>
                                <state key="normal" title="Done">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="didTapButton:" destination="pwB-OQ-RPC" eventType="touchUpInside" id="dNV-7T-7RG"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XOA-gL-cQf">
                                <rect key="frame" x="20" y="822" width="100" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="100" id="Fzm-kf-cFP"/>
                                    <constraint firstAttribute="height" constant="40" id="W5S-6P-CvF"/>
                                </constraints>
                                <state key="normal" title="Undo">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <connections>
                                    <action selector="didTapButton:" destination="pwB-OQ-RPC" eventType="touchUpInside" id="zfj-Vi-2Ui"/>
                                </connections>
                            </button>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="RvV-Kl-yPu">
                                <rect key="frame" x="0.0" y="579" width="414" height="222"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="222" id="Ksv-Id-eVE"/>
                                </constraints>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="iZf-MU-vwU">
                                    <size key="itemSize" width="128" height="128"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                            </collectionView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="vNG-ai-ctv"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="xha-cK-25V" firstAttribute="centerY" secondItem="1o8-ZJ-Qhz" secondAttribute="centerY" id="1cE-Tt-iWB"/>
                            <constraint firstItem="vNG-ai-ctv" firstAttribute="bottom" secondItem="XOA-gL-cQf" secondAttribute="bottom" id="7oD-DA-ltu"/>
                            <constraint firstItem="sum-oX-QfP" firstAttribute="trailing" secondItem="1o8-ZJ-Qhz" secondAttribute="trailing" id="985-hk-DtR"/>
                            <constraint firstItem="hOi-d3-9Uf" firstAttribute="leading" secondItem="1o8-ZJ-Qhz" secondAttribute="leading" id="Ffc-gW-bbI"/>
                            <constraint firstItem="sum-oX-QfP" firstAttribute="top" secondItem="1o8-ZJ-Qhz" secondAttribute="top" id="GJs-qU-WuL"/>
                            <constraint firstItem="XOA-gL-cQf" firstAttribute="leading" secondItem="1o8-ZJ-Qhz" secondAttribute="leading" constant="20" id="GMo-fP-Rg9"/>
                            <constraint firstItem="vNG-ai-ctv" firstAttribute="bottom" secondItem="zEK-0o-reo" secondAttribute="bottom" id="SSH-cf-hlb"/>
                            <constraint firstItem="sum-oX-QfP" firstAttribute="leading" secondItem="1o8-ZJ-Qhz" secondAttribute="leading" id="afF-mr-EFN"/>
                            <constraint firstItem="xha-cK-25V" firstAttribute="width" secondItem="1o8-ZJ-Qhz" secondAttribute="width" multiplier="0.7" id="cXw-hz-Jqg"/>
                            <constraint firstItem="zEK-0o-reo" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="XOA-gL-cQf" secondAttribute="trailing" constant="8" symbolic="YES" id="h8c-vK-cFj"/>
                            <constraint firstItem="RvV-Kl-yPu" firstAttribute="trailing" secondItem="1o8-ZJ-Qhz" secondAttribute="trailing" id="mmb-z0-jMf"/>
                            <constraint firstItem="hOi-d3-9Uf" firstAttribute="trailing" secondItem="1o8-ZJ-Qhz" secondAttribute="trailing" id="oJ0-pw-IoI"/>
                            <constraint firstItem="RvV-Kl-yPu" firstAttribute="leading" secondItem="1o8-ZJ-Qhz" secondAttribute="leading" id="osw-H3-RPk"/>
                            <constraint firstItem="xha-cK-25V" firstAttribute="centerX" secondItem="1o8-ZJ-Qhz" secondAttribute="centerX" id="pjj-6S-C7x"/>
                            <constraint firstAttribute="bottom" secondItem="hOi-d3-9Uf" secondAttribute="bottom" id="qbC-uG-lwe"/>
                            <constraint firstItem="hOi-d3-9Uf" firstAttribute="top" secondItem="RvV-Kl-yPu" secondAttribute="bottom" id="upH-mX-dkf"/>
                            <constraint firstAttribute="trailing" secondItem="zEK-0o-reo" secondAttribute="trailing" constant="20" id="vrs-Fv-aH7"/>
                            <constraint firstAttribute="bottom" secondItem="sum-oX-QfP" secondAttribute="bottom" id="yxj-ga-gOz"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="bottomButtonView" destination="hOi-d3-9Uf" id="aMi-eH-3g3"/>
                        <outlet property="collectionView" destination="RvV-Kl-yPu" id="xo1-4C-wVm"/>
                        <outlet property="doneButton" destination="zEK-0o-reo" id="NS4-Jf-Uxc"/>
                        <outlet property="flexBackgroundView" destination="xha-cK-25V" id="dxa-71-xnd"/>
                        <outlet property="libraryButton" destination="Hvd-Np-Pjb" id="3u2-uZ-xek"/>
                        <outlet property="photoButton" destination="uSR-Rp-4Eh" id="wNf-RJ-qQa"/>
                        <outlet property="photoContainerView" destination="KVM-OQ-izq" id="EcU-U1-9om"/>
                        <outlet property="photoPreviewImageView" destination="Edq-Aj-fVs" id="eYM-Co-nFB"/>
                        <outlet property="previewView" destination="sum-oX-QfP" id="jPH-hy-lXY"/>
                        <outlet property="undoButton" destination="XOA-gL-cQf" id="CTJ-2Q-4Kc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="1hY-82-cjO" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1150.7246376811595" y="-1260.2678571428571"/>
        </scene>
        <!--Chez Lui View Controller-->
        <scene sceneID="Yba-rE-lql">
            <objects>
                <viewController storyboardIdentifier="ChezLuiViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="Z4U-4k-EP2" customClass="ChezLuiViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="aRE-nx-Jpt">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="K0r-2b-iDi">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="798"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="5BZ-P9-p7j"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="K0r-2b-iDi" firstAttribute="trailing" secondItem="5BZ-P9-p7j" secondAttribute="trailing" id="Yyh-F6-67o"/>
                            <constraint firstItem="5BZ-P9-p7j" firstAttribute="bottom" secondItem="K0r-2b-iDi" secondAttribute="bottom" constant="64" id="beE-4V-gdJ"/>
                            <constraint firstItem="K0r-2b-iDi" firstAttribute="top" secondItem="aRE-nx-Jpt" secondAttribute="top" id="ciL-jT-HkN"/>
                            <constraint firstItem="K0r-2b-iDi" firstAttribute="leading" secondItem="5BZ-P9-p7j" secondAttribute="leading" id="hS2-OD-xcP"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="dummyTabBarHeightConstraint" destination="beE-4V-gdJ" id="YGB-yT-Wrh"/>
                        <outlet property="tableView" destination="K0r-2b-iDi" id="L4T-ky-YTm"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="JtO-fP-aRb" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="779.71014492753625" y="1508.7053571428571"/>
        </scene>
        <!--Channel View Controller-->
        <scene sceneID="b98-0l-FJz">
            <objects>
                <viewController storyboardIdentifier="ChannelViewController" useStoryboardIdentifierAsRestorationIdentifier="YES" id="lCZ-F8-Pli" customClass="ChannelViewController" customModule="GameFlex" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="S6Z-db-Nwi">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="1fz-l4-gDv">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="txI-E6-KKD">
                                    <size key="itemSize" width="600" height="9"/>
                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                </collectionViewFlowLayout>
                                <cells/>
                            </collectionView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="DKm-d6-K4Z"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="1fz-l4-gDv" firstAttribute="leading" secondItem="DKm-d6-K4Z" secondAttribute="leading" id="8CO-px-ata"/>
                            <constraint firstItem="1fz-l4-gDv" firstAttribute="bottom" secondItem="S6Z-db-Nwi" secondAttribute="bottom" id="nIJ-M2-Nro"/>
                            <constraint firstItem="1fz-l4-gDv" firstAttribute="top" secondItem="S6Z-db-Nwi" secondAttribute="top" id="tto-kI-KJw"/>
                            <constraint firstItem="1fz-l4-gDv" firstAttribute="trailing" secondItem="DKm-d6-K4Z" secondAttribute="trailing" id="z3a-aS-4FT"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="collectionView" destination="1fz-l4-gDv" id="EIj-y8-Wjf"/>
                        <outlet property="dummyTabBarHeightConstraint" destination="nIJ-M2-Nro" id="i82-hb-JKc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="7c9-aH-wP6" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="137.68115942028987" y="1566.9642857142856"/>
        </scene>
    </scenes>
    <resources>
        <image name="Flex" width="103.5" height="31.5"/>
        <image name="Game" width="124" height="33"/>
        <image name="bigG" width="201" height="201"/>
        <image name="checkmark" width="15" height="12"/>
        <image name="filter" width="40" height="40"/>
        <image name="flare" width="40" height="40"/>
        <image name="gameflexBranding" width="375" height="210"/>
        <image name="hand.draw.fill" catalog="system" width="128" height="109"/>
        <image name="logoFrontSmall" width="163" height="30"/>
        <image name="navAlert" width="18" height="21"/>
        <image name="navAlert_filled" width="24" height="24"/>
        <image name="navCreate" width="75" height="49"/>
        <image name="navHome" width="18" height="20"/>
        <image name="navProfile" width="19" height="18"/>
        <image name="navProfile_filled" width="14" height="18"/>
        <image name="navSearch" width="18.5" height="18.5"/>
        <image name="navSearch_filled" width="18.5" height="18.5"/>
        <image name="signUpWithEmail" width="275" height="56"/>
        <image name="text" width="40" height="40"/>
        <image name="trash" width="26" height="26"/>
        <image name="x" width="41" height="40"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
