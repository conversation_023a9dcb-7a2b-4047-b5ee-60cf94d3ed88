//
//  GFChannelViewController.swift
//  GameFlex
//
//  Created by <PERSON> on 9/8/20.
//  Copyright © 2020 GameFlex. All rights reserved.
//

import UIKit

enum ChannelAction {
    case more, like, play, reflex, comment, caption, goToTop
}

protocol ChannelDelegate: AnyObject {
    func didFinishTask()
    func didTapForChannelAction(_ action: ChannelAction)
}

class GFChannelViewController: UIViewController {

    var navButtons: [UIButton] = []
    var leftButt: UIButton?
    var rightButt: UIButton?
    var butt: UIButton?
    var sideBarView: SideBarView?
    
    override var title: String? {
        didSet{
            tabBarItem.title = ""
        }
    }
    
    // MARK: - Action funcs
    
    @objc func didTap(_ sender: Any) {
        if let but = sender as? UIButton {
            but.alpha = 1.0
            but.backgroundColor = .gfGreen
            but.setTitleColor(.black, for: .normal)
            let otherButtons: [UIButton] = navButtons.filter({ $0.tag != but.tag })
            handleOtherButtons(otherButtons)
            let home = self as? HomeViewController
            if but.tag == 102, home != nil { // channels
                home?.centerizeChannels()
            } else if but.tag == 101, home != nil {
                home?.centerizeMain()
            } else if but.tag == 100, home != nil { // following
                home?.centerizeFollowing()
            }
        }
    }
    
    func handleOtherButtons(_ others: [UIButton]) {
        others.forEach({
            $0.alpha = 0.6
            $0.setTitleColor(.lightGray, for: .normal)
            $0.backgroundColor = .darkGray
        })
    }
    
    func didFinishTask() {
        if let rvc = self as? ReflexViewController {
            rvc.doTimer()
        }
    }
    
    func successAlert() {
        DispatchQueue.main.async {
        let alert = UIAlertController(title: "Flex saved to Library.", message: "", preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default) { (success) in
                // create delegate to signal to restart the play
                self.didFinishTask()
            }
            alert.addAction(ok)
            self.present(alert, animated: true)
        }
    }
    
    /// So users can save photos on their channels in their photo library
    @objc func image(_ image: UIImage,
        didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if error != nil {
            let alert = UIAlertController(title: "Saving Error", message: "Something unexpected happened. Try again.", preferredStyle: .alert)
            let ok = UIAlertAction(title: "OK", style: .default, handler: nil)
            alert.addAction(ok)
            DispatchQueue.main.async {
                self.present(alert, animated: true)
            }
        } else {
            successAlert()
        }
    }

}
