### OS X temporary files ###
#
# c.f. http://www.westwind.com/reference/os-x/invisibles.html

.DS_Store

# c.f. http://www.westwind.com/reference/os-x/invisibles.html

.Trashes

# c.f. http://www.westwind.com/reference/os-x/invisibles.html

*.swp

# *.lock - this is used and abused by many editors for many different things.
#    For the main ones I use (e.g. Eclipse), it should be excluded 
#    from source-control, but YMMV

*.lock

#
# profile - REMOVED temporarily (on double-checking, this seems incorrect; I can't find it in OS X docs?)
#profile

### Xcode files ###

*.xccheckout

# Xcode temporary files that should never be committed
# 
# NB: NIB/XIB files still exist even on Storyboard projects, so we want this...

*~.nib

# Xcode build files -
#
# NB: slash on the end, so we only remove the FOLDER, not any files that were badly named "DerivedData"

DerivedData/

# NB: slash on the end, so we only remove the FOLDER, not any files that were badly named "build"

build/


# Xcode private settings (window sizes, bookmarks, breakpoints, custom executables, smart groups)

# .pbxuser: http://lists.apple.com/archives/xcode-users/2004/Jan/msg00193.html

*.pbxuser

# .mode1v3: http://lists.apple.com/archives/xcode-users/2007/Oct/msg00465.html

*.mode1v3

# .mode2v3: http://lists.apple.com/archives/xcode-users/2007/Oct/msg00465.html

*.mode2v3

# .perspectivev3: http://stackoverflow.com/questions/5223297/xcode-projects-what-is-a-perspectivev3-file

*.perspectivev3

#    NB: also, whitelist the default ones, some projects need to use these
!default.pbxuser
!default.mode1v3
!default.mode2v3
!default.perspectivev3

# Xcode - semi-personal settings

xcuserdata/


# Deprecated classes
#
# Allegedly, if you manually "deprecate" your classes, they get moved here.
#
# We're using source-control, so this is a "feature" that we do not want!

*.moved-aside

####
# UNKNOWN: recommended by others, but I can't discover what these files are
#
# ...none. Everything is now explained.

# Xcode temp file used to solve merge conflicts
*.orig

### CocoaPods ###
Pods
